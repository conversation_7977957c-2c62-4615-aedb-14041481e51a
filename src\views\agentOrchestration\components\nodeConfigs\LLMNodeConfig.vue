<template>
  <div>
          <n-form-item path="config.model" class="setrowbottom">
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span> 模型选择
                  </div>
                </template>
               <n-select
                  v-model:value="formData.config.modelConfig.model"
                  :options="modelOptions"
                  placeholder="请选择模型"
                  filterable
                  @update:value="handleModelChange"
                />
                <img
                  @click="changemodelParameterShow(true)"
                  class="w-[20px] h-[20px] ml-[10px] cursor-pointer"
                  src="@/assets/agentOrchestration/yitupeizhi.png"
                />
              </n-form-item>
              <n-form-item
                label-placement="left"
                class="setHeight mb-[9px] mt-[21px]"
              >
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span> 提示词
                  </div>
                </template>
                <div class="flex justify-end w-full">
                  <div class="text-[#125EFF] cursor-pointer"  @click="optimizing">一键优化</div>
                </div>
              </n-form-item>
              <n-form-item path="config.systemPromptTemplate" class="seteditable">
                <div class="relative w-full">
                  <n-spin :show="spinshow">
                  <div
                    ref="editableDiv"
                    contenteditable="true"
                    class="editable-div"
                    @keydown="handleKeydown"
                    @input="handleInput"
                  ></div>
                  <button
                    class="add-variable-btn"
                    @click="showVariableSelector = true"
                    type="button"
                  >
                    + 添加变量
                  </button>
                </n-spin>
                </div>
              </n-form-item>

              <n-form-item
                label-placement="left"
                class="setHeight mt-[24px] mb-[10px]"
              >
                <template #label>
                  <div class="rowstit"><span class="rowicon"></span>输出</div>
                </template>
              </n-form-item>

              <n-form-item label-placement="left">
                <div class="w-full flex text-[#C7C7C7]">
                  <div class="w-[50%]">名称</div>
                  <div>类型</div>
                </div>
              </n-form-item>

              <n-form-item
                path="config.wenbenshuchu"
                label-placement="left"
                class="outputrow"
              >
                <div class="w-full flex text-[#565756] items-center">
                  <div class="w-[50%]">
                    <div class="w-[234px] h-[38px]">
                      <n-input
                        v-model:value="formData.config.wenbenshuchu"
                        type="text"
                        placeholder="请输入输出名称"
                        @input="handleWenbenShuchuChange"
                      >
                      </n-input>
                    </div>
                  </div>
                  <div>文本</div>
                </div>
              </n-form-item>

    <!-- 变量选择器弹窗 -->
  <n-modal v-model:show="showVariableSelector">
    <n-card style="width: 600px" title="选择变量" :bordered="false" size="huge">
      <div class="variable-selector-content">
        <div class="selector-description">
          选择要插入到提示词中的变量，变量将以
          <code>{{ 变量名 }}</code> 的格式插入。
        </div>

             <AggregationSelector
                  v-model="selectedVariable"
                  :options="aggregationOptions"
                  placeholder="请选择变量"
                  @change="handleVariableSelect"
                />

        <div v-if="selectedVariableForInsert" class="insert-preview">
          <div class="preview-title">插入预览：</div>
          <div class="preview-content">
            <code>{{ selectedVariableForInsert }}</code>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="modal-footer">
          <n-button @click="closeVariableSelector">取消</n-button>
          <n-button
            type="primary"
            @click="insertVariableToPrompt"
            :disabled="!selectedVariableForInsert"
          >
            插入变量
          </n-button>
        </div>
      </template>
    </n-card>
  </n-modal>

    <!-- 模型配置弹窗 -->
  <NModal v-model:show="modelParameterShow" 
  	:bordered="false"
		preset="card"
		size="huge"
		style="width: 650px"
		title="模型参数">
      <NForm ref="modelParameterformRef" label-placement="left"
				label-width="auto"
				require-mark-placement="right-hanging" :model="modelParameterformData">

        	<NFormItem label="模型风格" label-placement="left" class="customcard">
					<NRadioGroup
			      v-model:value="modelParameterformData.style"
						name="radioGroup1"
						@update:value="updateRadio"
					>
						<NRadio
							v-for="song in modelParameterOptions"
							:key="song.model_style"
							:label="song.dictValue"
							:value="song.model_style"
						/>
					</NRadioGroup>
				</NFormItem>
				<NFormItem label="模型温度" label-placement="left" class="customcard">
					<n-slider v-model:value="modelParameterformData.temperature" :max="2" :min="0" :step="0.1" @update:value="modelTempUpdate"/>
					<NInputNumber
						v-model:value="modelParameterformData.temperature"
						:max="2"
						:min="0"
						:precision="1"
						:show-button="false"
						class="ml-10"
						@update:value="modelTempUpdate"
					/>
				</NFormItem>
				<NFormItem label="Top P" label-placement="left" class="customcard">
					<n-slider v-model:value="modelParameterformData.topP" :max="0.9" :min="0.1" :step="0.1" @update:value="topPUpdate"/>
					<NInputNumber
						v-model:value="modelParameterformData.topP"
						:max="0.9"
						:min="0.1"
						:precision="1"
						:show-button="false"
						class="ml-10"
						@update:value="topPUpdate"
					/>
				</NFormItem>
				<NFormItem label="最大输出长度" label-placement="left" class="customcard">
					<n-slider v-model:value="modelParameterformData.maxTokens" :max="32768" :min="1" :step="1"/>
					<NInputNumber
						v-model:value="modelParameterformData.maxTokens"
						:max="32768"
						:min="1"
						:precision="0"
						:show-button="false"
						class="ml-10"
						@update:value="(v:number | null)=>{if (v === null){modelParameterformData.maxTokens = 1}}"
					/>
				</NFormItem>

    
      </NForm>

      	<template #footer>
			<div class="flex flex-row-reverse w-full">
				<NButton type="info" @click="changemodelParameterfun"> 保存</NButton>
				<NButton class="!mr-5" @click="changemodelParameterShow(false)"> 取消</NButton>
			</div>
		</template>
    
  </NModal>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick,onMounted } from 'vue';
import {
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NSwitch,
  NSlider,
  NButton,
  NInputNumber,
  NScrollbar,
  NDropdown,
  NDynamicInput,
  useMessage,
  useDialog,
  NPopover,
  NDataTable,
  NCard,
  NDivider,
  NRadio,
  NRadioGroup,
  NModal,
  NCheckbox,
  NCheckboxGroup,
  SelectGroupOption,
  SelectOption,
  NSpin
} from "naive-ui";
import VariableSelector from '../VariableSelector.vue';
import AggregationSelector from '../AggregationSelector.vue';
import { agentParam, museModels,optimize } from "@/api/workShop";

import { generateId } from "@/store/modules/orchestration";

import { useOrchestrationStore } from "@/store";

const props = defineProps({
  formData: {
    type: Object,
    required: true,
  },
  node: {
    type: Object,
    required: true,
  },
  variableOptions: {
    type: Array,
    required: true,
  },
  aggregationOptions: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(['open-variable-management', 'update:formData']);

const orchestrationStore = useOrchestrationStore();
const message = useMessage();
onMounted(()=>{
  if(props.formData.config.systemPromptTemplate){
    rendereditableDiv(props.formData.config.systemPromptTemplate);
  }
})
//模型配置弹窗是否展示
var modelParameterShow = ref(false);
const modelParameterformRef = ref();
const modelParameterformData = ref({
  style: "",
  temperature: "",
  topP: "",
  maxTokens: "",
});
var spinshow = ref(false);
const modelOptions = ref<any[]>([]);
const modelParameterOptions = ref<any[]>([]);
const agentParams = ref<any>(null);
const optimizingLoading = ref(false);
const showVariableSelector = ref(false);
const selectedVariableForInsert = ref('');
const selectedVariable = ref('');
const selectedVariableObj = ref<any>({});

// 光标位置相关变量
const editableDiv = ref<HTMLElement | null>(null);
const savedRange = ref<Range | null>(null);

// 初始化数据
const initdatafun = async () => {
  const models = await museModels();
  const res = await agentParam();
  modelParameterOptions.value = res.data.model_style;
  agentParams.value = res.data;
  modelOptions.value = models.data.map((item: any) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
};


// 当节点变化时更新表单数据
watch(
  () => props.node,
  (newNode) => {
    console.log(newNode);
  },
  { immediate: true }
);

// 处理模型变化
const handleModelChange = (value: string) => {
  let model = modelOptions.value.find((item: any) => {
    return item.value === value;
  });
props.formData.config.modelConfig.modelName=model?.label || '';
};

const changemodelParameterShow = (status: boolean) => {
  if (status) {
    modelParameterformData.value.temperature =
      props.formData.config.modelConfig.completionParams.temperature;
    modelParameterformData.value.maxTokens =
      props.formData.config.modelConfig.completionParams.maxTokens;
    modelParameterformData.value.topP =
      props.formData.config.modelConfig.completionParams.topP;
    modelParameterformData.value.style =
      props.formData.config.modelConfig.completionParams.style;
  }
  modelParameterShow.value = status;
};
const changemodelParameterfun = async () => {
  await modelParameterformRef.value?.validate();
  props.formData.config.modelConfig.completionParams.temperature =
    modelParameterformData.value.temperature;
  props.formData.config.modelConfig.completionParams.maxTokens =
    modelParameterformData.value.maxTokens;
  props.formData.config.modelConfig.completionParams.topP =
    modelParameterformData.value.topP;
  props.formData.config.modelConfig.completionParams.style =
    modelParameterformData.value.style;
  changemodelParameterShow(false);
};
function updatemodelParameterfun(val: string) {
  modelParameterformData.value.temperature = val;
  	console.log(val)
	if (val === 0) {
		return
	}
	let obj: any = modelParameterOptions.value.find((item: any) => {
		return item.model_style == val
	})
	modelParameterformData.value.modelTemp = Number(obj.model_temp);
	modelParameterformData.value.topP = Number(obj.top_p);
}
// 优化提示词
const optimizing = async () => {
  spinshow.value = true;
  try {
    const res = await optimize({
      modelTemp: props.formData.config.modelConfig.completionParams.temperature ,
      promptTemplate: props.formData.config.systemPromptTemplate,
      modelId:props.formData.config.modelConfig.model,
      maxLength:props.formData.config.modelConfig.completionParams.maxTokens,
    });
    // model.value.promptTemplate = res.data;
    editableDiv.value.innerHTML = res.data;
    props.formData.config.systemPromptTemplate = res.data;
    console.log(res);
  } catch (e) {
    message.error("优化失败");
  } finally {
    spinshow.value = false;
  }
};

const handleWenbenShuchuChange=(val:any)=>{
    const variables = orchestrationStore.getVariablesByNodeId(props.node.id)[0] || [];
    orchestrationStore.updateVariable(variables.id, {
        name: props.formData.config.wenbenshuchu,
        code: variables.code,
        valueType: variables.type,
        value: variables.value
      })
}
const updateRadio = (val: any) => {
	console.log(val)
	if (val === 0) {
		return
	}
	let obj: any = modelParameterOptions.value.find((item: any) => {
		return item.model_style == val
	})
	modelParameterformData.value.temperature = Number(obj.model_temp);
	modelParameterformData.value.topP = Number(obj.top_p);
};
let modelTempUpdate = (v: number | null) => {
	console.log(v)
	if (v === null) {
		modelParameterformData.value.modelTemp = 0
	}
  modelParameterformData.value.style = 0;
}
// topP数据发生变化
let topPUpdate = (v: number | null) => {
	if (v === null) {
		modelParameterformData.value.topP = 0.1
	}
  modelParameterformData.value.style = 0;
}
const changeEditableDiv=()=>{
  // 创建临时div元素，不影响原始页面显示
  const tempDiv = document.createElement('div');
  
  // 复制原始editableDiv的内容到临时div
  if (editableDiv.value) {
    tempDiv.innerHTML = editableDiv.value.innerHTML;
    
    // 在临时div中查找并处理按钮元素
    const buttons = tempDiv.querySelectorAll('div[contenteditable="false"]');
    
    // 遍历按钮并处理
    buttons.forEach(btn => {
      const btnContent = btn.dataset.id;
      // 创建文本节点，将按钮内容用{{}}包含
      const textNode = document.createTextNode(`{{${btnContent}}}`);
      // 在临时div中替换按钮元素
      btn.parentNode?.replaceChild(textNode, btn);
    });
    
    // 获取处理后的完整内容
    const processedContent = tempDiv.innerHTML;
    console.log(processedContent);

      emit('update:formData', {
    ...props.formData,
    config: {
      ...props.formData.config,
      systemPromptTemplate: processedContent,
    },
  });
    // 如果需要返回处理后的内容，可以添加return语句
    // return processedContent;
  }
}
const closeVariableSelector=()=>{
  showVariableSelector.value = false;
  selectedVariable.value = "";
  selectedVariableForInsert.value = "";
  selectedVariableObj.value = {};
}
// 处理变量选择
const handleVariableSelect = (variable: any) => {
  console.log("选中变量:", variable);
  let variableobj=orchestrationStore.getVariableById(variable);
  selectedVariableForInsert.value=variableobj.name;
  selectedVariableObj.value = variableobj;
};
// 插入变量到提示词
const insertVariableToPrompt =async () => {
  if (!selectedVariableForInsert.value) return;
  const variableSyntax = selectedVariableForInsert.value;

  // 根据当前节点类型插入到对应的字段
    nextTick(() => {
      focusEditableDiv();
      // 调用insertVariable函数，传入选中的变量名,变量id
      insertVariable(variableSyntax,selectedVariableObj.value.id);
      
  // 关闭弹窗并清空选择
  showVariableSelector.value = false;
  selectedVariable.value = "";
  selectedVariableForInsert.value = "";
  selectedVariableObj.value = {};

  message.success("变量已插入");
    });

};


function handleKeydown(e: KeyboardEvent) {
  console.log(e.key);
  // contenteditable 默认支持删除，无需特殊处理
}

// 处理输入事件，检测{}
function handleInput(e: Event) {
  const target = e.target as HTMLElement;
  const text = target.textContent || "";

  // 检查是否包含{}
  if (text.includes("{}")) {
    // 延迟执行，确保DOM更新完成
    nextTick(() => {
      // 获取当前光标位置
      const selection = window.getSelection();
      if (!selection || !selection.rangeCount) return;

      const range = selection.getRangeAt(0);
      const startOffset = range.startOffset;

      // 只删除光标位置附近的{}，而不是整个文本内容
      const textNode = range.startContainer;
      if (textNode.nodeType === Node.TEXT_NODE) {
        const textContent = textNode.textContent || "";
        const beforeCursor = textContent.substring(0, startOffset);
        const afterCursor = textContent.substring(startOffset);

        // 检查光标前后是否有{}
        let newBeforeCursor = beforeCursor;
        let newAfterCursor = afterCursor;
        let hasReplacement = false;

        // 检查光标前是否有{}
        if (beforeCursor.endsWith("{}")) {
          newBeforeCursor = beforeCursor.slice(0, -2);
          hasReplacement = true;
        }

        // 检查光标后是否有{}
        if (afterCursor.startsWith("{}")) {
          newAfterCursor = afterCursor.slice(2);
          hasReplacement = true;
        }

        // 检查光标位置是否有{}
        if (beforeCursor.endsWith("{") && afterCursor.startsWith("}")) {
          newBeforeCursor = beforeCursor.slice(0, -1);
          newAfterCursor = afterCursor.slice(1);
          hasReplacement = true;
        }

        if (hasReplacement) {
          // 更新文本节点内容
          textNode.textContent = newBeforeCursor + newAfterCursor;

          // 重新设置光标位置
          const newRange = document.createRange();
          newRange.setStart(textNode, newBeforeCursor.length);
          newRange.collapse(true);
          selection.removeAllRanges();
          selection.addRange(newRange);

          // 保存当前光标位置，用于后续插入变量
          saveCursorPosition();

          // 打开变量选择器
          showVariableSelector.value = true;
        }
      }
    });
  }else{
    changeEditableDiv();
  }
}
function rendereditableDiv(content: string) {
  if (!content || !editableDiv.value) return;
  
  // 清空editableDiv
  editableDiv.value.innerHTML = '';
  focusEditableDiv();
  
  // 创建文档片段，用于暂存所有元素，确保顺序正确
  const fragment = document.createDocumentFragment();
  
  // 处理{{}}包裹的内容
  const regex = /\{\{([^{}]+)\}\}/g;
  let lastIndex = 0;
  let match;
  
  while ((match = regex.exec(content)) !== null) {
    // 添加{{}}前面的文本
    if (match.index > lastIndex) {
      const textNode = document.createTextNode(content.substring(lastIndex, match.index));
      fragment.appendChild(textNode);
    }
    
    // 添加{{}}内容作为div
    const variableId = match[1].trim();
    let variableobj = orchestrationStore.getVariableById(variableId);
    // 创建变量div但不直接插入，而是添加到文档片段
    const variableDiv = createVariableDiv(variableobj?.name,variableId);
    fragment.appendChild(variableDiv);
    
    lastIndex = match.index + match[0].length;
  }
  
  // 添加剩余文本
  if (lastIndex < content.length) {
    const textNode = document.createTextNode(content.substring(lastIndex));
    fragment.appendChild(textNode);
  }
  
  // 一次性将所有元素添加到editableDiv，确保顺序正确
  editableDiv.value.appendChild(fragment);
    // 添加取消光标逻辑：移除焦点
  if (editableDiv.value) {
    editableDiv.value.blur();
  }

}
// 保存光标位置
function saveCursorPosition() {
  const selection = window.getSelection();
  if (selection && selection.rangeCount > 0) {
    savedRange.value = selection.getRangeAt(0).cloneRange();
  }
}
// 获取焦点的辅助函数
function focusEditableDiv() {
  if (editableDiv.value) {
    editableDiv.value.focus();
    // 确保光标在内容末尾
    const selection = window.getSelection();
    const range = document.createRange();
    range.selectNodeContents(editableDiv.value);
    range.collapse(false); // false表示光标在末尾
    selection?.removeAllRanges();
    selection?.addRange(range);
  }
}
// 新增：创建变量div的辅助函数
function createVariableDiv(variableName?: string,variableId?:string) {
  // 创建一个div元素
  const btn = document.createElement("div");
  btn.innerText = variableName || "变量名称"; // 使用传入的变量名或默认值
  btn.setAttribute("contenteditable", "false");
  btn.setAttribute("data-id",variableId);

  // 参考图片优化样式
  btn.style.display = "inline-block";
  btn.style.padding = "2px 14px";
  btn.style.margin = "0 4px";
  btn.style.background = "#125EFF";
  btn.style.border = "none";
  btn.style.color = "#fff";
  btn.style.borderRadius = "8px";
  btn.style.fontSize = "15px";
  btn.style.fontWeight = "500";
  btn.style.cursor = "pointer";
  btn.style.outline = "none";
  btn.style.lineHeight = "22px";
  btn.style.height = "28px";
  btn.style.boxShadow = "none";
  btn.style.transition = "background 0.2s";

  // 悬浮时略微加深
  btn.onmouseenter = () => {
    btn.style.background = "#0d47a1";
  };
  btn.onmouseleave = () => {
    btn.style.background = "#125EFF";
  };
  
  return btn;
}
function insertVariable(variableName?: string,variableId?:string) {
  const el = editableDiv.value;
  if (!el) return;
  // 使用辅助函数创建变量div
  const btn = createVariableDiv(variableName,variableId);

  // 悬浮时略微加深
  btn.onmouseenter = () => {
    btn.style.background = "#0d47a1";
  };
  btn.onmouseleave = () => {
    btn.style.background = "#125EFF";
  };

  // 如果有保存的光标位置，使用保存的位置；否则使用当前光标位置
  if (savedRange.value) {
    insertNodeAtSavedPosition(btn);
  } else {
    insertNodeAtCursor(btn);
  }
  changeEditableDiv()
}

function insertNodeAtSavedPosition(node: Node) {
  if (!savedRange.value) return;

  // 恢复保存的光标位置
  const selection = window.getSelection();
  if (selection) {
    selection.removeAllRanges();
    selection.addRange(savedRange.value);
  }

  // 在保存的位置插入节点
  savedRange.value.insertNode(node);

  // 将光标移到插入的节点后面
  savedRange.value.setStartAfter(node);
  savedRange.value.collapse(true);

  // 更新选择
  if (selection) {
    selection.removeAllRanges();
    selection.addRange(savedRange.value);
  }

  // 清空保存的位置
  savedRange.value = null;

  // 让 contenteditable div 保持 focus
  if (editableDiv.value) editableDiv.value.focus();
  // 某些浏览器下异步 focus 更稳妥
  setTimeout(() => {
    editableDiv.value && editableDiv.value.focus();
  }, 0);
}

function insertNodeAtCursor(node: Node) {
  let sel = window.getSelection();
  if (!sel || !sel.rangeCount) return;
  let range = sel.getRangeAt(0);
  range.collapse(false);
  range.insertNode(node);
  // 关键：将光标移到按钮后面
  range.setStartAfter(node);
  range.collapse(true);
  sel.removeAllRanges();
  sel.addRange(range);
  // 让 contenteditable div 保持 focus
  if (editableDiv.value) editableDiv.value.focus();
  // 某些浏览器下异步 focus 更稳妥
  setTimeout(() => {
    editableDiv.value && editableDiv.value.focus();
  }, 0);
}

// 更新节点配置
const updateNodeConfig = () => {
  emit('update:formData', {
    ...props.node,
    config: formData.value.config
  });
};
// 初始化数据
initdatafun();
</script>

<style scoped lang="less">
@import './nodeConfigStyle.less';
</style>