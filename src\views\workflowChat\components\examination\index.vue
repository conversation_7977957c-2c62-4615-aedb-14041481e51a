<template>
  <div class="examinationbox">
    <div class="title">
      <span>模拟测试题</span>
      <img src="@/assets/close.png" alt="关闭" @click="closefun" />
    </div>
    <n-divider />
      <div class="loading" v-if="isloading">
        <img class="loadingicon" src="@/assets/loadingicon.png" />
        <!-- 添加进度条组件 -->
        <div class="progressbox">
            <n-progress
                type="line"
                :percentage="loadingProgress"
                indicator-placement="inside"
                processing
              />
        </div>
        <div class="loadingtitle">测验考题正在生成中</div>
      </div>
    <div class="topicbox" v-if="!isloading">
      <div class="topicType">单选题</div>
      <div v-for="(item, index) in topiclistarr" :key="index">
        <div class="topicname">{{ index + 1 }}、 {{ item.question }}</div>
        <n-radio-group v-model:value="item.answer" name="radiogroup">
          <div class="topicradio">
            <n-radio
              v-for="(itm, ind) in item.options"
              :key="itm"
              :value="indgetvalue(ind)"
            >
              {{ itm }}
            </n-radio>
          </div>
          <!-- <n-space>
               
                </n-space> -->
        </n-radio-group>
        <div
          class="analysisbox"
          :class="item.answer != item.correct_answer ? 'erranalysisbox' : ''"
          v-if="submitted"
        >
          <p>正确答案：{{ item.correct_answer }}</p>
          <p>解析：{{ item.explanation }}</p>
        </div>
      </div>
    </div>
    <n-divider />
    <div class="btnrow" v-if="btnisshow">
      <n-button color="#1263FF" @click="send">提交测验</n-button>
    </div>
  </div>
</template>
<script setup>
  import { NDivider, NButton, NRadio, NRadioGroup, NSpace,NSpin,useMessage, NProgress } from "naive-ui";
import { usestretchoutStore } from '@/store'
import {getquestionsApi,examweakApi} from "@/api/tools";
const useTretchOut = usestretchoutStore()
const message = useMessage()
import { ref, onMounted,onUnmounted } from "vue";
var isloading = ref(false);
const emit = defineEmits()
var submitted = ref(false);
var topiclistarr = ref([]); // 初始化为空数组
var btnisshow = ref(false);
var request=ref({});
var loadingProgress = ref(0); // 新增进度条进度

function closefun(){
  useTretchOut.setdocumentisshow(false)

}
  function gettopiclist(){
    isloading.value=true;
    loadingProgress.value = 1; // 初始化为 1
      // 设置一分钟的进度条计时器
      const progressInterval = 60000 / 98; // 一分钟内从 1 到 100
      const progressTimer = setInterval(() => {
        if (loadingProgress.value < 99) {
          loadingProgress.value++;
        } else {
          clearInterval(progressTimer);
          isloading.value=false;
        }
      }, progressInterval);
    getquestionsApi({conversationContentId:useTretchOut.stretchoutStorageInfo.conversationContentId}).then(res=>{
      console.log(res);
      clearInterval(progressTimer);
      isloading.value=false;

      btnisshow.value=true;
      request.value=res.data;
      const originalQuestions = JSON.parse(res.data.examQuestion).questions;
      const copiedQuestions = JSON.parse(JSON.stringify(originalQuestions));
      copiedQuestions.forEach(item => {
        item.answer = "";
      });

      let index = 0;
      const totalQuestions = copiedQuestions.length;
      const questionTimer = setInterval(() => {
        if (index < copiedQuestions.length) {
          topiclistarr.value.push(copiedQuestions[index]);
          index++;
        } else {
          clearInterval(questionTimer);
        }
      }, 500);


    })
  }
onMounted(() => {
    gettopiclist();
});
onUnmounted(() => {
  // 在组件卸载时清除定时器
  topiclistarr.value = [];
  closefun();
})
function send() {

 let answered= topiclistarr.value.some((item, index) => {
    return item.answer == "";
  });
  if(answered){
    message.warning("请完成所有题目再提交");
    return;
  }
  btnisshow.value=false;
  // console.log(request.value.question,"request.value.question");
  // console.log(request.value.examQuestion,"request.value.examQuestion");
  // console.log(topiclistarr.value,"topiclistarr.value");
  let params={
    conversationContentId:request.value.conversationContentId,
    examQuestion:request.value.examQuestion,
    examAnswer:JSON.stringify({questions:topiclistarr.value}),
    // question:request.value.question,

  }
  sessionStorage.setItem('examrequest',JSON.stringify(params));
  submitted.value = true;
  emit('fetchWeaknessfun')
}

function indgetvalue(index) {
  const letters = ["A", "B", "C", "D"];
  return letters[index] || "";
}
</script>
<style lang="less" scoped>
.examinationbox {
  height: 100%;
  padding-left: 35.5px;
  padding-right: 35.5px;
}
.title {
  height: 80px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 20px;
  color: #323233;
  letter-spacing: 0;
  img {
    width: 28px;
    height: 28px;
  }
}
.n-divider:not(.n-divider--vertical) {
  margin: 0;
}
.btnrow {
  margin-top: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  button {
    width: 152px;
    height: 36px;
    background: #1263ff;
    box-shadow: 0 2px 5px 0 #615ced4d;
    border-radius: 6px;
  }
}
.topicType {
  height: 28px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 20px;
  color: #323233;
  letter-spacing: 0;
  line-height: 29px;
  margin-top: 30px;
  // margin-bottom: 20px;
}
.topicname {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 16px;
  color: #0c0d0d;
  letter-spacing: 0;
  line-height: 26px;
  margin-top: 30px;
}
.n-radio {
  width: 344px;
  height: 46px;
  border-radius: 6px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 16px;
  color: #0c0d0d;
  letter-spacing: 0;
  line-height: 46px;
  padding-left: 20px;
  margin-top: 12px;
}
.n-radio--checked {
  background: #125eff1a;
  border: 1px solid #125eff;
}
.topicradio {
  display: flex;
  justify-content: space-between;
  // align-items: center;
  flex-wrap: wrap;
}
.topicbox {
  margin-bottom: 60px;
}
.analysisbox {
  background: #f8f9fe;
  border-radius: 6px;
  padding: 20px 32px 16px 28px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #00ad76;
  letter-spacing: 0;
  line-height: 28px;
  margin-top: 20px;
}
.erranalysisbox {
  color: #ff4b4b;
}
/deep/ .n-radio{
  display: flex;
  align-items: center;
}
/deep/ .n-radio__label{
  line-height: 20px;
}
.loading{
  width: 100%;
  height: 90%;
  background: url('@/assets/loadingbg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-top: 197px;
  margin:0 auto;
  margin-bottom: 21px;
  .loadingicon{
    width: 172px;
  height: 172px;
    margin:0 auto;
  }
 
  }
  .progressbox{
    width: 322.59px;
    height: 14px;
    border-radius: 8px;
    margin: 0 auto;
    margin-top: 21px;
  }
  .loadingtitle{
    height: 22px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #606266;
    letter-spacing: 0;
    text-align: center;
    margin-top: 20px;
  }
</style>