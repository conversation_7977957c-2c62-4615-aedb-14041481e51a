import service from './axios'; // 导入你原来的axios实例
import {RequestEnum} from '@/enums/httpEnum';

// 定义新的请求参数类型
interface RequestConfig {
	url: string;
	params?: any;
	data?: any;

	[key: string]: any; // 支持其他axios配置项
}

// 创建请求方法包装器
const request = {
	// 通用请求处理函数
	async request(method: RequestEnum, urlOrConfig: string | RequestConfig, dataOrParams?: any) {
		// 判断是否是新的调用方式（传入对象）
		if (typeof urlOrConfig === 'object' && urlOrConfig.url) {
			const {url, params, data, ...restConfig} = urlOrConfig;
			return service({
				method,
				url,
				params,
				data,
				...restConfig
			});
		}
		// 兼容原有调用方式（传入字符串url）
		else if (typeof urlOrConfig === 'string') {
			return service({
				method,
				url: urlOrConfig,
				// 根据请求方法决定参数位置
				[method.toUpperCase() === RequestEnum.GET ? 'params' : 'data']: dataOrParams
			});
		}

		throw new Error('Invalid request parameters');
	},

	// GET请求
	get(urlOrConfig: string | RequestConfig, params?: any) {
		return this.request(RequestEnum.GET, urlOrConfig, params);
	},

	// POST请求
	post(urlOrConfig: string | RequestConfig, data?: any) {
		return this.request(RequestEnum.POST, urlOrConfig, data);
	},

	// PUT请求
	put(urlOrConfig: string | RequestConfig, data?: any) {
		return this.request(RequestEnum.PUT, urlOrConfig, data);
	},

	// DELETE请求
	delete(urlOrConfig: string | RequestConfig, params?: any) {
		return this.request(RequestEnum.DELETE, urlOrConfig, params);
	}
};

export default request;
