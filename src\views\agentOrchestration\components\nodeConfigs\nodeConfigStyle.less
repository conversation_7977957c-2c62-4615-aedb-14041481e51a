.config-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: opacity 0.3s ease;
}

.config-panel {
  position: fixed;
  top: 0;
  right: -500px;
  width: 540px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
  width: 540px;
  padding: 0 16px;
}

.config-panel.panel-open {
  right: 0;
}

.panel-header {
  /* padding: 20px 24px 16px; */
  /* border-bottom: 1px solid #e2e8f0; */
  /* background: #fafbfc; */
  height: 40px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.node-info {
  flex: 1;
  display: flex;
  align-items: center;
}
.nodeIcon {
  width: 32px;
  height: 32px;
  margin-right: 8px;
}
.node-name {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  img {
    margin-right: 11px;
  }
}
.node-type-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
}

.panel-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.node-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-idle {
  background: #d9d9d9;
}
.status-running {
  background: #faad14;
  animation: pulse 2s infinite;
}
.status-success {
  background: #52c41a;
}
.status-error {
  background: #ff4d4f;
}

.run-btn,
.more-btn,
.close-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #64748b;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.run-btn:hover,
.more-btn:hover,
.close-btn:hover {
  background: #f1f5f9;
  color: #1a202c;
}

.run-btn {
  color: #125eff;
}

.run-btn:hover {
  background: #e6f0ff;
  color: #0d47a1;
}

.run-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.run-btn.running {
  color: #faad14;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.panel-content {
  flex: 1;
  /* padding: 24px; */
  overflow: hidden;
}

.config-section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  display: flex;
  align-items: center;
}

.panel-footer {
  padding: 20px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafbfc;
  flex-shrink: 0;
  border-radius: 0 0 0 12px; // 左下角圆角
  margin: 0 -16px; // 抵消父容器的padding
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  :deep(.n-button) {
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    min-height: 36px;

    // 取消按钮样式
    &:not([type]) {
      min-width: 88px;
      border: 1px solid #e0e0e0;
      background: #ffffff;
      color: #666666;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        border-color: #125eff;
        color: #125eff;
        box-shadow: 0 2px 6px rgba(18, 94, 255, 0.15);
      }

      &:active {
        transform: translateY(1px);
      }
    }

    // 保存按钮样式
    &[type="info"] {
      min-width: 108px;
      box-shadow: 0 2px 6px rgba(18, 94, 255, 0.2);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

/* 执行日志样式 */
.log-count-badge {
  background: #1890ff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  margin-left: 8px;
}

.clear-logs-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #64748b;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-logs-btn:hover {
  background: #f1f5f9;
  color: #ff4d4f;
}

.execution-logs {
  max-height: 300px;
  overflow-y: auto;
}

.log-entry {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.log-entry:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.log-entry.success {
  border-left: 3px solid #52c41a;
}

.log-entry.error {
  border-left: 3px solid #ff4d4f;
}

.log-entry.running {
  border-left: 3px solid #faad14;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.status-dot.success {
  background: #52c41a;
}

.status-dot.error {
  background: #ff4d4f;
}

.status-dot.running {
  background: #faad14;
}

.status-text {
  font-weight: 500;
  font-size: 12px;
}

.log-meta {
  display: flex;
  gap: 8px;
  font-size: 11px;
  color: #64748b;
}

.log-details {
  margin-top: 8px;
}

.log-output,
.log-error {
  margin-bottom: 6px;
}

.detail-label {
  font-size: 11px;
  color: #64748b;
  margin-bottom: 2px;
  font-weight: 500;
}

.detail-content {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 11px;
  font-family: "Courier New", monospace;
  word-break: break-all;
  max-height: 80px;
  overflow-y: auto;
}

.log-error .detail-content {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.error-text {
  color: #dc2626;
}

.more-logs {
  text-align: center;
  color: #64748b;
  font-size: 12px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 4px;
  margin-top: 8px;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
.rowstit {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 22px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 16px;
  color: #000000e0;
  width: 100%;
  .rowicon {
    width: 5px;
    height: 16px;
    background: #abc6ff;
    background-image: linear-gradient(180deg, #82fba5 0%, #058dfc 100%);
    border-radius: 3px;
    margin-right: 9px;
  }
  img {
    width: 16px;
    height: 16px;
    margin-left: 8px;
  }
}
/deep/ .n-data-table-thead {
  background-color: #ffffff;
  .n-data-table-th {
    background-color: #ffffff;
    .n-data-table-th__title {
      color: #bebebe;
    }
  }
}
.knowledgelist {
  align-items: center;
  min-height: 38px;
  justify-content: space-between;
  padding-top: 10px;
  padding-right: 16px;
  padding-bottom: 10px;
  padding-left: 16px;
  border-radius: 8px;
  background-color: #f5f5f5;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 13px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #3b3b3b;
  margin-bottom: 8px;
}

.divider {
  width: 100%;
  height: 0.92px;
  background: #0000000f;
  margin-bottom: 20px;
}
/deep/ .n-form-item-feedback-wrapper {
  display: none;
}
/deep/ .customcard{
.n-form-item-feedback-wrapper {
  display: block !important;
}
 .n-form-item-label{
  height: auto !important;
}
} 

/deep/ .n-form-item.n-form-item--left-labelled .n-form-item-label {
  height: 100%;
}
.rowtitle {
  margin-bottom: 9px;
}
/deep/ .n-data-table .n-data-table-th {
  border: 0px;
}
/deep/
  .n-data-table.n-data-table--bottom-bordered
  .n-data-table-td.n-data-table-td--last-row {
  border: 0px;
}
/deep/ .n-data-table-thead .n-data-table-th .n-data-table-th__title {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #c7c7c7;
}
/deep/ .n-data-table-td {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #565756;
  letter-spacing: 0;
}
.histit {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #565756;
  letter-spacing: 0;
}
/deep/ .n-radio-group {
  width: 100%;
}
.btnparent {
  /deep/ .n-button {
    width: 100%;
  }
}
/deep/ .setinputbg {
  background-color: #f5f5f5;
  .n-input__border {
    border: 0px;
  }
}
.modelParametercard {
  /deep/ .n-card__content {
    padding: 0px;
  }
  /deep/ .n-card__footer {
    padding: 0px;
  }
}

/* 条件节点配置样式 - 严格按照UI设计图 */
.condition-section {
  margin-bottom: 16px;
}

.condition-item-wrapper {
  margin-bottom: 12px;
  background: #f5f5f6;
  border-radius: 8px;
  padding: 14px 12px;
  transition: all 0.2s ease;
}
.condition-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.condition-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.condition-branch-label {
  background: #125eff;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 400;
  line-height: 1;
  margin-left: 8px;
  flex-shrink: 0;
}

.condition-delete-btn {
  width: 16px;
  height: 16px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.condition-delete-btn:hover {
  opacity: 1;
}

.condition-config-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.condition-select-group {
  flex: 0 0 120px;
}

.condition-input-group {
  flex: 1;
}

.condition-actions {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.action-icon:hover {
  opacity: 1;
}

.condition-field-select,
.condition-operator-select,
.condition-value-input {
  width: 100%;
}

.add-condition-wrapper,
.add-else-branch-wrapper {
  margin-top: 12px;
  width: 100%;
}

.add-condition-button,
.add-else-branch-button {
  width: 100%;
  height: 32px;
  border: 1px dashed #d9d9d9;
  background: transparent;
  color: #666;
  font-size: 14px;
}

.add-condition-button:hover,
.add-else-branch-button:hover {
  border-color: #125eff;
}

.add-else-branch-button {
  width: 104px;
  height: 33px;
  background: #125eff;
  border-radius: 7px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 13px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  border: 0;
}

/* 花括号连接器样式 - 严格按照UI设计图 */
.condition-group {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.condition-bracket-container {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  height: 100%;
  // min-height: 120px;
  position: relative;
  padding-top: 58px;
}

.condition-bracket {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 60px;
}

.bracket-top,
.bracket-bottom {
  width: 2px;
  background-color: #d9d9d9;
  flex: 1;
  min-height: 20px;
}

.bracket-top {
  border-top-left-radius: 8px;
  border-left: 1px solid #d3d7df;
  border-top: 1px solid #d3d7df;
  background: transparent;
  width: 28px;
  height: 20px;
  margin-bottom: 8px;
}

.bracket-bottom {
  border-bottom-left-radius: 8px;
  border-left: 1px solid #d3d7df;
  border-bottom: 1px solid #d3d7df;
  background: transparent;
  width: 28px;
  height: 20px;
  margin-top: 8px;
}
.bracket-middle {
  /deep/ .n-base-selection-input__content {
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    font-size: 15px;
    color: #125eff;
    letter-spacing: 0;
  }
}
.bracket-middle {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 8px 0;
  width: 58px;
}

// .group-logic-selector {
//   width: 70px !important;
//   min-width: 70px;
// }

.group-logic-selector .n-base-selection {
  background: #f5f5f6 !important;
  border: 1px solid #e5e5e5 !important;
  border-radius: 6px !important;
  font-size: 13px !important;
  text-align: center;
  padding: 0 8px !important;
}

.group-logic-selector .n-base-selection .n-base-selection-label {
  justify-content: center;
  font-weight: 500;
  color: #333 !important;
  padding: 0 !important;
}

.group-logic-selector:hover .n-base-selection {
  border-color: #125eff !important;
  background: #f0f7ff !important;
}

.condition-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  /deep/ .n-select {
    .n-base-selection-label {
      background: #ffffff !important;
    }
  }
  /deep/ .selector-trigger {
    background: #ffffff;
  }
}

/* 逻辑连接符样式 */
.condition-logic-connector {
  display: flex;
  justify-content: center;
  margin: 8px 0;
}

.logic-operator-select {
  width: 80px;
  text-align: center;
}

/* 变量选择器弹窗样式 */
.variable-selector-content {
  .selector-description {
    margin-bottom: 16px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 6px;
    font-size: 14px;
    color: #6b7280;

    code {
      background: #e5e7eb;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
      color: #374151;
    }
  }

  .insert-preview {
    margin-top: 16px;
    padding: 12px;
    background: #f0f9ff;
    border-radius: 6px;
    border-left: 3px solid #0ea5e9;

    .preview-title {
      font-size: 12px;
      color: #6b7280;
      margin-bottom: 6px;
    }

    .preview-content {
      code {
        background: #e0f2fe;
        padding: 4px 8px;
        border-radius: 4px;
        font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
        color: #0c4a6e;
        font-weight: 500;
      }
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.editable-div {
  min-height: 130px;
  width: 100%;
  background: #f5f5f6;
  border-radius: 8px;
  padding: 12px;
  outline: none;
  position: relative;
  padding-bottom: 50px;
  overflow-y: auto;
}

.add-variable-btn {
  position: absolute;
  right: 16px;
  bottom: 16px;
  z-index: 2;
  background: #125eff;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 4px 12px;
  cursor: pointer;
  font-size: 14px;
}
/deep/ .n-form-item {
  .n-form-item-label {
    height: 22px;
    // min-height: 22px;
    padding-bottom: 0px;
  }
  .n-form-item-blank {
    height: 38px;
    .n-input {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px;

      .n-input__border {
        display: none;
      }
    }
    .n-input__input-el {
      height: 100%;
      background: #f5f5f6;
    }
    .n-input-number {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px;
    }
  }
}
/deep/ .n-select {
  height: 100%;
  background: #f5f5f6;
  border-radius: 8px;

  .n-base-selection__border {
    display: none;
  }
  .n-base-selection {
    height: 100%;
    background: #f5f5f6;
    border-radius: 8px;

    .n-base-selection-label {
      height: 100%;
      background: #f5f5f6;
      border-radius: 8px !important;
    }
  }
}
.setrowbottom {
  /deep/ .n-form-item-label {
    margin-bottom: 9px;
  }
}
.setHeight {
  // height: 22px;
  /deep/ .n-form-item-label {
    height: 22px !important;
    min-height: 22px;
  }
  /deep/ .n-form-item-blank {
    height: 22px !important;
    min-height: 22px;
  }
}
/deep/.n-data-table-tr {
  height: 22px;
  min-height: 22px;
  // padding-bottom: 16px;
  .n-data-table-th {
    height: 30px;
    padding: 0px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #c7c7c7;
  }
  .n-data-table-td {
    height: 38px;
    padding: 0px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #565756;
    letter-spacing: 0;
  }
}
.seteditable {
  grid-template-areas:
        "blank"
        "feedback" ;
  /deep/ .n-form-item-blank {
    min-height: 130px !important;
    height: auto;
  }
}
.outputrow {
  .n-input {
    height: 100%;
    background: #f5f5f6;
    border-radius: 8px;
    /deep/ .n-input__border {
      display: none;
    }
  }
  .n-input__input-el {
    height: 100%;
    border: 0;
  }
  .n-input-number {
    height: 100%;
    border: 0;
  }
}