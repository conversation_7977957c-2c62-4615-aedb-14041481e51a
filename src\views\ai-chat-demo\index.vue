<template>
  <div class="ai-chat-demo">
    <div class="demo-header">
      <h1>AI对话组件演示</h1>
      <div class="demo-controls">
        <select v-model="selectedConfig" @change="applyConfig">
          <option value="default">默认配置</option>
          <!-- <option value="dark">深色主题</option> -->
          <option value="minimal">简约模式</option>
          <option value="full">完整功能</option>
        </select>
        <button @click="resetDemo" class="reset-btn">重置演示</button>
        <button @click="addThinkingDemo" class="demo-btn">思考过程演示</button>
        <button @click="addStageDemo" class="demo-btn">流程阶段演示</button>
        <button @click="showConfigPanel = !showConfigPanel" class="demo-btn">
          {{ showConfigPanel ? '隐藏' : '显示' }}配置面板
        </button>
        <button @click="testCurrentConfig" class="demo-btn">测试当前配置</button>
        <button @click="toggleCustomActions" class="demo-btn">
          {{ useCustomActions ? '使用默认' : '使用自定义' }}操作按钮
        </button>
        <button @click="toggleAIMessageMode" class="demo-btn">
          {{ currentConfig.styles?.aiMessageMode === 'flat' ? '气泡模式' : '平铺模式' }}
        </button>
      </div>
    </div>

    <div class="demo-container">
      <AIChat
        :config="currentConfig"
        :title="chatTitle"
        :subtitle="chatSubtitle"
        :messages="messages"
        :quick-prompts="quickPrompts"
        :knowledge-bases="knowledgeBases"
        :is-loading="isLoading"
        :is-generating="isGenerating"
        :loading-text="loadingText"
        :workflow-nodes="workflowNodes"
        :welcome-message="welcomeMessage"
        :conversation-id="conversationId"
        :category="category"
        :agent-id="agentId"
        :model-id="modelId"
        :theme="currentConfig.theme"
        :primary-color="currentConfig.styles?.primaryColor"
        :auto-scroll="currentConfig.behavior?.autoScroll"
        :enable-copy="currentConfig.features?.copy"
        :enable-regenerate="currentConfig.features?.regenerate"
        :enable-export="currentConfig.features?.export"
        :enable-voice="currentConfig.features?.voiceInput"
        :enable-upload="currentConfig.features?.fileUpload"
        :enable-feedback="currentConfig.features?.feedback"
        :input-placeholder="currentConfig.placeholders?.input"
        :on-api-call="handleAPICall"
        @send-message="handleSendMessage"
        @feedback="handleFeedback"
        @edit-message="handleEditMessage"
        @regenerate="handleRegenerate"
        @upload-file="handleUploadFile"
        @select-knowledge-base="handleSelectKnowledgeBase"
        @stop-generation="handleStopGeneration"
        @clear-chat="handleClearChat"
        @export-chat="handleExportChat"
        @voice-input="handleVoiceInput"
        @copy="handleCopy"
        @error="handleError"
        @success="handleSuccess"
      >
        <!-- 额外的自定义操作按钮插槽示例 -->
        <template v-if="useCustomActions" #message-actions="{ message }">
          <!-- 只添加额外的自定义按钮，原有按钮会自动保留 -->
          <button class="action-btn custom-action" @click="handleCustomAction(message, 'share')" title="分享">
            <span class="action-text">📤</span>
          </button>
          <button class="action-btn custom-action" @click="handleCustomAction(message, 'bookmark')" title="收藏">
            <span class="action-text">⭐</span>
          </button>
          <button class="action-btn custom-action" @click="handleCustomAction(message, 'translate')" title="翻译">
            <span class="action-text">🌐</span>
          </button>
        </template>

        <!-- 额外的自定义输入工具按钮插槽示例 -->
        <template v-if="useCustomActions" #input-tools="{ config }">
          <!-- 只添加额外的自定义工具按钮，原有按钮会自动保留 -->
          <button class="tool-btn custom-tool" @click="handleCustomTool('camera')" title="拍照">
            <span class="tool-text">📷</span>
          </button>
          <button class="tool-btn custom-tool" @click="handleCustomTool('microphone')" title="录音">
            <span class="tool-text">🎤</span>
          </button>
          <button class="tool-btn custom-tool" @click="handleCustomTool('location')" title="位置">
            <span class="tool-text">📍</span>
          </button>
        </template>

        <!-- 用户消息额外内容插槽示例 -->
        <template v-if="useCustomActions" #user-message-extra="{ message }">
          <!-- 在用户消息下方添加额外的元素 -->
          <div class="user-message-meta">
            <span class="message-time">{{ formatTime(message.timestamp) }}</span>
            <span class="message-status">已发送</span>
            <button class="message-action-btn" @click="handleUserMessageAction(message, 'pin')" title="置顶">
              📌
            </button>
            <button class="message-action-btn" @click="handleUserMessageAction(message, 'quote')" title="引用">
              💬
            </button>
          </div>
        </template>
      </AIChat>
    </div>

    <!-- 配置面板 -->
    <ConfigPanel
      :is-open="showConfigPanel"
      :config="currentConfig"
      @close="showConfigPanel = false"
      @update:config="handleConfigUpdate"
      @save="handleConfigSave"
    />

    <!-- 调试面板 -->
    <div v-if="showDebugPanel" class="debug-panel">
      <h3>调试信息</h3>
      <div class="debug-section">
        <h4>当前状态</h4>
        <p>消息数量: {{ messages.length }}</p>
        <p>正在生成: {{ isGenerating }}</p>
        <p>正在加载: {{ isLoading }}</p>
      </div>
      <div class="debug-section">
        <h4>最近事件</h4>
        <div class="debug-events">
          <div v-for="event in recentEvents" :key="event.id" class="debug-event">
            <span class="event-time">{{ formatTime(event.timestamp) }}</span>
            <span class="event-type">{{ event.type }}</span>
            <span class="event-data">{{ JSON.stringify(event.data) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 调试按钮 -->
    <button 
      class="debug-toggle" 
      @click="showDebugPanel = !showDebugPanel"
      :class="{ active: showDebugPanel }"
    >
      🐛
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import AIChat from '@/components/common/AIChat/index.vue'
import ConfigPanel from '@/components/common/AIChat/components/ConfigPanel.vue'
import { useChat } from '@/components/common/AIChat/composables/useChat'
import { createAPIAdapter } from '@/components/common/AIChat/adapters/apiAdapter'
import type {
  AIChatConfig,
  QuickPrompt,
  KnowledgeBase,
  Attachment,
  MessageFeedback,
  WorkflowNode,
  ChatAPIParams
} from '@/components/common/AIChat/types'

// 配置面板状态
const showConfigPanel = ref(false)

// 兼容性配置
const conversationId = ref('demo-conversation-' + Date.now())
const category = ref('demo')
const agentId = ref('demo-agent')
const modelId = ref('gpt-3.5-turbo')

// 演示配置
const selectedConfig = ref('default')
const showDebugPanel = ref(false)
const recentEvents = ref<Array<{ id: string, type: string, data: any, timestamp: number }>>([])
const useCustomActions = ref(false)

// 聊天配置
const chatTitle = ref('AI助手演示')
const chatSubtitle = ref('体验完整的AI对话功能')

const welcomeMessage = {
  title: '欢迎使用AI助手！',
  content: '我可以帮助您解答问题、处理文档、进行对话。请选择下方的快捷问题开始，或直接输入您的问题。'
}

// 快捷输入
const quickPrompts: QuickPrompt[] = [
  { id: '1', text: '介绍一下你的功能', category: 'intro' },
  { id: '2', text: '帮我写一份工作总结', category: 'work' },
  { id: '3', text: '解释一下Vue 3的新特性', category: 'tech' },
  { id: '4', text: '推荐一些学习资源', category: 'learning' }
]

// 知识库
const knowledgeBases: KnowledgeBase[] = [
  { 
    id: '1', 
    name: '技术文档', 
    description: 'Vue、React、Node.js等技术文档',
    icon: '📚'
  },
  { 
    id: '2', 
    name: '公司手册', 
    description: '员工手册、流程规范等',
    icon: '🏢'
  },
  { 
    id: '3', 
    name: '产品资料', 
    description: '产品介绍、使用说明等',
    icon: '📋'
  }
]

// 配置预设
const configs: Record<string, AIChatConfig> = {
  default: {
    theme: 'light',
    layout: 'responsive',
    features: {
      feedback: true,
      voiceInput: true,
      fileUpload: true,
      knowledgeBase: true,
      workflow: false,
      thinking: true,
      header: true, //是否显示头部
      messageEdit: true,
      stopGeneration: true
    },
    styles: {
      primaryColor: '#3b82f6',
      backgroundColor: '#f9fafb',
      messageStyle: 'bubble',
      aiMessageMode: 'bubble'
    },
    placeholders: {
      input: '请输入您的问题...',
      thinking: 'AI正在思考中...'
    },
    welcomePrompts: {
      enabled: true,
      title: '猜你想问',
      prompts: [
        {
          id: 'code-review',
          text: '帮我分析这段代码的优化空间',
          description: '代码审查和性能优化建议'
        },
        {
          id: 'bug-fix',
          text: '这个错误应该如何解决？',
          description: '问题诊断和解决方案'
        },
        {
          id: 'best-practice',
          text: '这个功能的最佳实践是什么？',
          description: '行业标准和推荐做法'
        },
        {
          id: 'architecture',
          text: '如何设计这个系统的架构？',
          description: '系统设计和架构建议'
        },
        {
          id: 'learning',
          text: '我想学习这个技术，从哪里开始？',
          description: '学习路径和资源推荐'
        },
        {
          id: 'comparison',
          text: '这两个技术方案有什么区别？',
          description: '技术对比和选型建议'
        }
      ]
    },
    // 启用真实API调用
    compatibility: {
      useOriginalAPI: true,
      conversationId: `chat-${Date.now()}`,
      category: '1',  // 根据用户提供的数据结构
      agentId: '1966317284699451393',  // 使用用户提供的agentId
      multiTurnFlag: false  // 对应数字0，只保留必要参数
    },
    api: {
      baseUrl: import.meta.env.VITE_GLOB_API_URL || '',
      chatEndpoint: '/emind/conversationContent/text_answers',
      uploadEndpoint: '/resource/files/upload',
      feedbackEndpoint: '/emind/conversation/feedback',
      voiceEndpoint: '/sse_api/speech_recognition',
      audioEndpoint: '/sse_api/text_to_speach',
      timeout: 30000
    }
  },
  dark: {
    theme: 'dark',
    layout: 'responsive',
    features: {
      feedback: true,
      voiceInput: true,
      fileUpload: true,
      knowledgeBase: true,
      workflow: false,
      thinking: true,
      messageEdit: true,
      stopGeneration: true
    },
    styles: {
      primaryColor: '#60a5fa',
      backgroundColor: '#1f2937',
      messageStyle: 'bubble',
      aiMessageMode: 'flat'
    },
    welcomePrompts: {
      enabled: true,
      title: '🌙 夜间模式提示',
      prompts: [
        {
          id: 'dark-theme',
          text: '深色主题的设计原则',
          description: '用户界面设计最佳实践'
        },
        {
          id: 'night-coding',
          text: '夜间编程的效率技巧',
          description: '提高深夜工作效率'
        },
        {
          id: 'eye-protection',
          text: '如何保护视力健康？',
          description: '长时间使用电脑的健康建议'
        }
      ]
    }
  },
  minimal: {
    theme: 'light',
    layout: 'responsive',
    features: {
      feedback: false,
      voiceInput: false,
      fileUpload: false,
      knowledgeBase: false,
      workflow: false,
      thinking: false,
      messageEdit: false,
      stopGeneration: true
    },
    styles: {
      messageStyle: 'card'
    }
  },
  full: {
    theme: 'light',
    layout: 'responsive',
    features: {
      feedback: true,
      voiceInput: true,
      fileUpload: true,
      knowledgeBase: true,
      workflow: true,
      thinking: true,
      messageEdit: true,
      stopGeneration: true
    },
    styles: {
      primaryColor: '#059669',
      messageStyle: 'bubble'
    }
  }
}

const currentConfig = ref<AIChatConfig>(configs.default)

// 使用聊天组合式函数
const {
  messages,
  isLoading,
  isGenerating,
  loadingText,
  workflowNodes,
  sendMessage,
  stopGeneration,
  regenerateMessage,
  editMessage,
  addFeedback,
  clearMessages,
  exportChat,
  sendMessageWithOriginalAPI,
  convertLegacyMessage,
  importLegacyMessages
} = useChat(currentConfig.value)

// API适配器
const apiAdapter = createAPIAdapter(currentConfig.value)

// 应用配置
const applyConfig = () => {
  currentConfig.value = configs[selectedConfig.value] || configs.default
  addEvent('config-changed', { config: selectedConfig.value })
}

// 事件处理
const handleSendMessage = async (content: string, attachments?: Attachment[]) => {
  addEvent('send-message', { content, attachments })
  
  // 模拟工作流
  if (currentConfig.value.features?.workflow && content.includes('工作流')) {
    const nodes: WorkflowNode[] = [
      { id: '1', title: '分析需求', status: 'completed', description: '理解用户需求' },
      { id: '2', title: '制定方案', status: 'running', description: '制定解决方案' },
      { id: '3', title: '执行任务', status: 'pending', description: '执行具体任务' },
      { id: '4', title: '总结结果', status: 'pending', description: '总结执行结果' }
    ]
    workflowNodes.value = nodes
    
    // 模拟节点状态更新
    setTimeout(() => {
      if (workflowNodes.value[1]) {
        workflowNodes.value[1].status = 'completed'
        workflowNodes.value[2].status = 'running'
      }
    }, 2000)
    
    setTimeout(() => {
      if (workflowNodes.value[2]) {
        workflowNodes.value[2].status = 'completed'
        workflowNodes.value[3].status = 'running'
      }
    }, 4000)
    
    setTimeout(() => {
      if (workflowNodes.value[3]) {
        workflowNodes.value[3].status = 'completed'
      }
    }, 6000)
  }
  
  await sendMessage(content, attachments)
}

const handleFeedback = (messageId: string, feedback: MessageFeedback) => {
  addEvent('feedback', { messageId, feedback })
  addFeedback(messageId, feedback)
}

const handleEditMessage = async (messageId: string, content: string) => {
  addEvent('edit-message', { messageId, content })
  await editMessage(messageId, content)
}

const handleRegenerate = async (messageId: string) => {
  addEvent('regenerate', { messageId })
  await regenerateMessage(messageId)
}

const handleUploadFile = async (file: File): Promise<Attachment> => {
  addEvent('upload-file', { fileName: file.name, fileSize: file.size })

  try {
    // 使用真实的文件上传API
    const response = await apiAdapter.uploadFile(file)

    if (response.code === '0' || response.success) {
      const fileData = response.data || response
      return {
        id: generateId(),
        name: file.name,
        type: file.type.startsWith('image/') ? 'image' : 'document',
        url: fileData.fileUrl || fileData.url || URL.createObjectURL(file),
        size: file.size
      }
    } else {
      throw new Error(response.message || '文件上传失败')
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    // 降级到本地预览
    return {
      id: generateId(),
      name: file.name,
      type: file.type.startsWith('image/') ? 'image' : 'document',
      url: URL.createObjectURL(file),
      size: file.size,
      // error: '上传失败，仅本地预览'
    }
  }
}

const handleSelectKnowledgeBase = (kb: KnowledgeBase) => {
  addEvent('select-knowledge-base', { knowledgeBase: kb.name })
}

const handleStopGeneration = () => {
  addEvent('stop-generation', {})
  stopGeneration()
}

const handleClearChat = () => {
  addEvent('clear-chat', {})
  clearMessages()
  workflowNodes.value = []
}

const handleExportChat = () => {
  addEvent('export-chat', {})
  exportChat()
}

const handleVoiceInput = (text: string) => {
  addEvent('voice-input', { text })
}

// 配置面板相关方法
const handleConfigUpdate = (config: AIChatConfig) => {
  currentConfig.value = { ...config }
  addEvent('config-updated', { config })
}

const handleConfigSave = (config: AIChatConfig) => {
  currentConfig.value = { ...config }
  localStorage.setItem('ai-chat-demo-config', JSON.stringify(config))
  addEvent('config-saved', { config })
  console.log('配置已保存:', config)
}

// API调用处理
const handleAPICall = async (params: any) => {
  addEvent('api-call', { params })

  try {
    if (currentConfig.value.compatibility?.useOriginalAPI) {
      return await sendMessageWithOriginalAPI(params)
    } else {
      return await apiAdapter.sendMessage(params)
    }
  } catch (error) {
    console.error('API调用失败:', error)
    throw error
  }
}

// 扩展事件处理
const handleCopy = (content: string) => {
  addEvent('copy', { content })
  navigator.clipboard.writeText(content)
  console.log('已复制到剪贴板:', content)
}

const handleError = (error: Error) => {
  addEvent('error', { error: error.message })
  console.error('组件错误:', error)
}

const handleSuccess = (response: any) => {
  addEvent('success', { response })
  console.log('操作成功:', response)
}

// 自定义操作处理函数
const handleCustomAction = (message: any, action: string) => {
  addEvent('custom-action', { messageId: message.id, action })

  switch (action) {
    case 'share':
      console.log('分享消息:', message.content)
      // 这里可以实现分享功能
      alert(`分享功能：${message.content.substring(0, 50)}...`)
      break
    case 'bookmark':
      console.log('收藏消息:', message.content)
      // 这里可以实现收藏功能
      alert(`已收藏消息：${message.content.substring(0, 50)}...`)
      break
    case 'translate':
      console.log('翻译消息:', message.content)
      // 这里可以实现翻译功能
      alert(`翻译功能：${message.content.substring(0, 50)}...`)
      break
    default:
      console.log('未知操作:', action)
  }
}

// 切换自定义操作按钮
const toggleCustomActions = () => {
  useCustomActions.value = !useCustomActions.value
  addEvent('toggle-custom-actions', { enabled: useCustomActions.value })
  console.log('切换自定义操作按钮:', useCustomActions.value ? '启用' : '禁用')
}

// 自定义工具按钮处理函数
const handleCustomTool = (tool: string) => {
  addEvent('custom-tool', { tool })

  switch (tool) {
    case 'camera':
      console.log('拍照功能')
      alert('拍照功能：打开相机进行拍照')
      break
    case 'microphone':
      console.log('录音功能')
      alert('录音功能：开始录制语音')
      break
    case 'location':
      console.log('位置功能')
      alert('位置功能：获取当前位置信息')
      break
    default:
      console.log('未知工具:', tool)
  }
}

// 格式化时间
const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 用户消息操作处理函数
const handleUserMessageAction = (message: any, action: string) => {
  addEvent('user-message-action', { messageId: message.id, action })

  switch (action) {
    case 'pin':
      console.log('置顶消息:', message.content)
      alert(`已置顶消息：${message.content.substring(0, 30)}...`)
      break
    case 'quote':
      console.log('引用消息:', message.content)
      alert(`引用消息：${message.content.substring(0, 30)}...`)
      break
    default:
      console.log('未知用户消息操作:', action)
  }
}

// 切换AI消息展示模式
const toggleAIMessageMode = () => {
  const currentMode = currentConfig.value.styles?.aiMessageMode || 'bubble'
  const newMode = currentMode === 'bubble' ? 'flat' : 'bubble'

  currentConfig.value = {
    ...currentConfig.value,
    styles: {
      ...currentConfig.value.styles,
      aiMessageMode: newMode
    }
  }

  addEvent('toggle-ai-message-mode', { mode: newMode })
  console.log('切换AI消息模式:', newMode === 'bubble' ? '气泡模式' : '平铺模式')
}

// 测试当前配置
const testCurrentConfig = () => {
  addEvent('test-config', { config: currentConfig.value })

  // 生成配置测试报告
  const report = generateConfigReport(currentConfig.value)

  // 添加测试消息
  const testMessage = {
    id: generateId(),
    content: `## 🔧 配置测试报告

### 当前配置概览
- **主题**: ${currentConfig.value.theme || 'light'}
- **布局**: ${currentConfig.value.layout || 'responsive'}
- **API模式**: ${currentConfig.value.compatibility?.useOriginalAPI ? '原有API' : '新API'}

### 功能状态
${report.features}

### API配置
- **基础URL**: ${currentConfig.value.api?.baseUrl || '未设置'}
- **聊天端点**: ${currentConfig.value.api?.chatEndpoint || '/chat'}
- **超时时间**: ${currentConfig.value.api?.timeout || 30000}ms

### 兼容性配置
- **会话ID**: ${currentConfig.value.compatibility?.conversationId || '未设置'}
- **分类**: ${currentConfig.value.compatibility?.category || '未设置'}
- **智能体ID**: ${currentConfig.value.compatibility?.agentId || '未设置'}

### 行为配置
- **自动滚动**: ${currentConfig.value.behavior?.autoScroll ? '✅' : '❌'}
- **显示时间戳**: ${currentConfig.value.behavior?.showTimestamp ? '✅' : '❌'}
- **Markdown渲染**: ${currentConfig.value.behavior?.enableMarkdown ? '✅' : '❌'}
- **最大消息数**: ${currentConfig.value.behavior?.maxMessages || 100}

### 测试建议
${report.suggestions}

---
*配置测试时间: ${new Date().toLocaleString()}*`,
    role: 'assistant' as const,
    timestamp: Date.now()
  }

  messages.value.push(testMessage)
  console.log('配置测试完成:', report)
}

// 生成ID
const generateId = () => {
  return Math.random().toString(36).substring(2, 9)
}

// 生成配置报告
const generateConfigReport = (config: AIChatConfig) => {
  const enabledFeatures: string[] = []
  const disabledFeatures: string[] = []
  const suggestions: string[] = []

  // 分析功能状态
  if (config.features) {
    Object.entries(config.features).forEach(([key, value]) => {
      if (value) {
        enabledFeatures.push(getFeatureLabel(key))
      } else {
        disabledFeatures.push(getFeatureLabel(key))
      }
    })
  }

  // 生成建议
  if (config.compatibility?.useOriginalAPI) {
    suggestions.push('• 当前使用原有API，确保后端接口可用')
  }

  if (!config.api?.baseUrl) {
    suggestions.push('• 建议设置API基础URL')
  }

  if (enabledFeatures.length === 0) {
    suggestions.push('• 建议启用一些功能以提升用户体验')
  }

  if (config.behavior?.maxMessages && config.behavior.maxMessages < 50) {
    suggestions.push('• 最大消息数较少，可能影响对话连续性')
  }

  return {
    features: `
**已启用功能** (${enabledFeatures.length}个):
${enabledFeatures.map(f => `✅ ${f}`).join('\n')}

**已禁用功能** (${disabledFeatures.length}个):
${disabledFeatures.map(f => `❌ ${f}`).join('\n')}`,
    suggestions: suggestions.length > 0 ? suggestions.join('\n') : '• 当前配置良好，无特殊建议'
  }
}

// 功能标签映射
const getFeatureLabel = (key: string) => {
  const labels: Record<string, string> = {
    feedback: '反馈功能',
    voiceInput: '语音输入',
    fileUpload: '文件上传',
    knowledgeBase: '知识库',
    workflow: '工作流',
    thinking: '思考过程',
    messageEdit: '消息编辑',
    stopGeneration: '停止生成',
    copy: '复制功能',
    regenerate: '重新生成',
    export: '导出功能',
    quickPrompts: '快捷提示',
    streaming: '流式输出',
    audio: '音频播放',
    translation: '翻译功能',
    search: '搜索功能',
    annotation: '标注功能',
    multiTurn: '多轮对话',
    contextMemory: '上下文记忆'
  }
  return labels[key] || key
}

// 重置演示
const resetDemo = () => {
  clearMessages()
  workflowNodes.value = []
  recentEvents.value = []
  selectedConfig.value = 'default'
  applyConfig()
  addEvent('demo-reset', {})
}

// 添加思考过程演示消息
const addThinkingDemo = () => {
  const demoMessage = {
    id: 'thinking-demo-' + Date.now(),
    content: '基于您的需求，我来帮您分析和优化代码结构。',
    role: 'assistant' as const,
    timestamp: Date.now(),
    thinking: `让我仔细分析一下您的需求...

## 🤔 需求理解
用户希望实现一个现代化的AI对话组件，需要支持思考过程的显示和折叠功能。

## 📋 技术分析
1. **前端框架**: Vue 3 + TypeScript
2. **设计要求**: 现代化、扁平化设计
3. **功能需求**:
   - 思考过程实时显示
   - 完成后自动折叠
   - 支持手动展开查看

## 🎯 实现方案
基于分析，我建议采用以下技术方案：

### 组件结构设计
- 使用统一的消息气泡容器
- 思考过程和最终回答分层显示
- 添加折叠/展开交互控制

### 样式设计
- 采用渐变背景和多层阴影
- 使用品牌色 #125EFF 作为主色调
- 添加流畅的动画过渡效果

### 交互逻辑
- 思考过程中实时显示内容
- 思考完成后自动折叠
- 点击头部可以展开/折叠
- 保持状态记忆

## 💡 优化建议
1. **性能优化**: 使用CSS transform而不是改变布局属性
2. **用户体验**: 添加加载动画和状态指示器
3. **响应式设计**: 确保在移动端也有良好体验

## 🔧 技术细节
- 使用Vue 3 Composition API管理状态
- 采用CSS Grid/Flexbox进行布局
- 使用CSS custom properties支持主题切换
- 添加TypeScript类型定义确保类型安全

这样的设计既符合现代化的视觉要求，又能提供良好的用户体验。`,
    isThinkingComplete: true
  }

  messages.value.push(demoMessage)
  addEvent('thinking-demo-added', { messageId: demoMessage.id })
}

// 添加流程阶段演示消息
const addStageDemo = () => {
  const demoMessage = {
    id: 'stage-demo-' + Date.now(),
    content: '基于您的需求，我已经完成了代码分析和优化建议。',
    role: 'assistant' as const,
    timestamp: Date.now(),
    stages: [
      {
        id: 'analyze',
        name: '代码分析',
        status: 'complete' as const,
        content: `正在深入分析您提供的代码结构...

## 📊 代码结构分析
- **框架版本**: Vue 3.5.17 + TypeScript 4.9.5
- **组件架构**: Composition API + \\\`<script setup>\\\` 语法
- **状态管理**: 使用 Pinia 进行全局状态管理
- **样式方案**: CSS Modules + 原子化CSS

## 🔍 发现的关键点
1. **组件设计模式**
   - 采用了组合式API的最佳实践
   - 组件职责分离清晰
   - Props和Emits定义完整

2. **性能考虑**
   - 使用了响应式引用和计算属性
   - 组件懒加载和代码分割
   - 内存泄漏预防措施

3. **用户体验**
   - 加载状态和错误处理完善
   - 交互反馈及时
   - 响应式设计适配多端

## ⚠️ 需要优化的地方
- 部分组件可以进一步拆分
- 某些计算属性可以使用缓存优化
- 动画效果可以更加流畅`,
        startTime: Date.now() - 8000,
        endTime: Date.now() - 5000,
        isCollapsed: true
      },
      {
        id: 'optimize',
        name: '优化建议',
        status: 'complete' as const,
        content: `基于分析结果，提供以下详细优化建议：

## 🚀 性能优化方案
1. **组件层面优化**
   \\\`\\\`\\\`vue
   &lt;!-- 使用v-memo缓存复杂计算 --&gt;
   &lt;div v-memo="[expensiveValue]"&gt;
     &lt;ComplexComponent :data="expensiveValue" /&gt;
   &lt;/div&gt;
   \\\`\\\`\\\`

2. **虚拟滚动实现**
   - 对于长列表使用虚拟滚动
   - 减少DOM节点数量
   - 提升渲染性能

3. **懒加载策略**
   - 图片懒加载
   - 组件按需加载
   - 路由懒加载

## 🎨 用户体验优化
1. **加载状态优化**
   - 骨架屏加载效果
   - 进度条显示
   - 错误状态处理

2. **动画效果增强**
   \\\`\\\`\\\`css
   .smooth-transition {
     transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
   }
   \\\`\\\`\\\`

3. **交互反馈改进**
   - 按钮点击反馈
   - 悬停状态优化
   - 键盘导航支持

## 📱 响应式设计
- 移动端适配优化
- 触摸手势支持
- 屏幕尺寸自适应`,
        startTime: Date.now() - 5000,
        endTime: Date.now() - 2000,
        isCollapsed: true
      },
      {
        id: 'implement',
        name: '代码实现',
        status: 'complete' as const,
        content: `正在生成优化后的代码实现...

## 🔧 核心组件实现
\\\`\\\`\\\`vue
&lt;template&gt;
  &lt;div class="optimized-component"&gt;
    &lt;!-- 思考过程显示区域 --&gt;
    &lt;div v-if="message.thinking" class="thinking-section"&gt;
      &lt;div class="thinking-header" @click="toggleThinking"&gt;
        &lt;div class="thinking-indicator"&gt;
          &lt;div v-if="!message.isThinkingComplete" class="thinking-spinner"&gt;&lt;/div&gt;
          &lt;div v-else class="thinking-check"&gt;🤔&lt;/div&gt;
        &lt;/div&gt;
        &lt;span class="thinking-text"&gt;思考过程&lt;/span&gt;
        &lt;span class="thinking-toggle" :class="{ 'expanded': !isThinkingCollapsed }"&gt;
          &lt;img src="/src/assets/chat/rightrow.png" alt="展开" class="toggle-icon" /&gt;
        &lt;/span&gt;
      &lt;/div&gt;

      &lt;div class="thinking-content" :class="{ 'collapsed': isThinkingCollapsed }"&gt;
        &lt;div class="thinking-text-content" v-html="renderMarkdown(message.thinking)"&gt;&lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;

    &lt;!-- 主要内容区域 --&gt;
    &lt;div class="main-content"&gt;
      &lt;div class="message-text" v-html="renderMarkdown(message.content)"&gt;&lt;/div&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup lang="ts"&gt;
import { ref } from 'vue'
import { marked } from 'marked'

const isThinkingCollapsed = ref(true)

const toggleThinking = () => {
  isThinkingCollapsed.value = !isThinkingCollapsed.value
}

const renderMarkdown = (content: string) => {
  return marked(content)
}
&lt;/script&gt;
\\\`\\\`\\\`

## 🎨 样式实现
\\\`\\\`\\\`css
.thinking-section {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid rgba(18, 94, 255, 0.08);
  background: linear-gradient(135deg, rgba(248, 250, 255, 0.6) 0%, rgba(240, 244, 255, 0.6) 100%);
  overflow: hidden;
}

.thinking-content {
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  max-height: 1000px;
}

.thinking-content.collapsed {
  max-height: 0;
  opacity: 0;
  padding: 0;
}
\\\`\\\`\\\`

## ✅ 实现特性
- ✅ 统一消息气泡设计
- ✅ 思考过程实时显示
- ✅ 自动折叠功能
- ✅ 流畅动画效果
- ✅ 响应式适配
- ✅ TypeScript类型安全`,
        startTime: Date.now() - 2000,
        endTime: Date.now(),
        isCollapsed: true
      }
    ],
    isThinkingComplete: true
  }

  messages.value.push(demoMessage)
  addEvent('stage-demo-added', { messageId: demoMessage.id })
}

// 添加事件记录
const addEvent = (type: string, data: any) => {
  const event = {
    id: Math.random().toString(36).substr(2, 9),
    type,
    data,
    timestamp: Date.now()
  }
  recentEvents.value.unshift(event)
  
  // 只保留最近20个事件
  if (recentEvents.value.length > 20) {
    recentEvents.value = recentEvents.value.slice(0, 20)
  }
}
</script>

<style scoped>
.ai-chat-demo {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.demo-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.demo-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.demo-controls select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

.reset-btn {
  padding: 8px 16px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.reset-btn:hover {
  background: #dc2626;
}

.demo-btn {
  padding: 8px 16px;
  background: #125EFF;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
  margin-left: 8px;
}

.demo-btn:hover {
  background: #0f4fd4;
}

.demo-container {
  flex: 1;
  padding: 24px;
  display: flex;
  justify-content: center;
}

.demo-container > * {
  width: 100%;
  max-width: 800px;
  height: 100%;
}

.debug-panel {
  position: fixed;
  right: 20px;
  top: 100px;
  width: 300px;
  max-height: 60vh;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 1000;
}

.debug-panel h3 {
  margin: 0;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
  font-weight: 600;
}

.debug-section {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
}

.debug-section h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
}

.debug-section p {
  margin: 4px 0;
  font-size: 12px;
  color: #374151;
}

.debug-events {
  max-height: 200px;
  overflow-y: auto;
}

.debug-event {
  display: flex;
  gap: 8px;
  padding: 4px 0;
  font-size: 11px;
  border-bottom: 1px solid #f9fafb;
}

.event-time {
  color: #6b7280;
  min-width: 60px;
}

.event-type {
  color: #059669;
  font-weight: 500;
  min-width: 80px;
}

.event-data {
  color: #374151;
  flex: 1;
  word-break: break-all;
}

.debug-toggle {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 48px;
  height: 48px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  z-index: 999;
}

.debug-toggle:hover {
  background: #2563eb;
  transform: scale(1.1);
}

.debug-toggle.active {
  background: #ef4444;
}

@media (max-width: 768px) {
  .demo-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .demo-controls {
    justify-content: center;
  }
  
  .demo-container {
    padding: 12px;
  }
  
  .debug-panel {
    right: 10px;
    width: calc(100vw - 20px);
    max-width: 300px;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .ai-chat-demo {
    padding: 0;
    height: 100vh;
    overflow: hidden;
  }

  .demo-header {
    padding: 12px 16px;
    border-radius: 0;
    /* 适配刘海屏 */
    padding-top: max(12px, env(safe-area-inset-top));
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .demo-header h1 {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .demo-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .demo-controls select {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 8px;
    width: 100%;
  }

  .demo-controls button {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 8px;
    white-space: nowrap;
  }

  .demo-container {
    height: calc(100vh - 120px);
    border-radius: 0;
    margin: 0;
    padding: 0;
  }

  .debug-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.9);
    padding: 16px;
    overflow-y: auto;
    border-radius: 0;
  }

  .debug-panel h3 {
    font-size: 18px;
    margin-bottom: 16px;
    color: white;
  }

  .debug-section {
    margin-bottom: 16px;
    background: rgba(255, 255, 255, 0.1);
    padding: 12px;
    border-radius: 8px;
  }

  .debug-section h4 {
    font-size: 14px;
    margin-bottom: 8px;
    color: #e0e0e0;
  }

  .debug-section p {
    font-size: 13px;
    color: #ccc;
    margin-bottom: 4px;
  }

  .debug-events {
    max-height: 200px;
    overflow-y: auto;
  }

  .debug-event {
    padding: 6px 8px;
    margin-bottom: 4px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    font-size: 11px;
  }

  .event-time {
    color: #888;
    margin-right: 8px;
  }

  .event-type {
    color: #4CAF50;
    margin-right: 8px;
    font-weight: 600;
  }

  .event-data {
    color: #ddd;
  }

  .debug-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 48px;
    height: 48px;
    border-radius: 24px;
    font-size: 20px;
    z-index: 999;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
  .demo-header {
    padding: 10px 12px;
    padding-top: max(10px, env(safe-area-inset-top));
  }

  .demo-header h1 {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .demo-controls {
    gap: 6px;
  }

  .demo-controls select {
    padding: 6px 10px;
    font-size: 13px;
  }

  .demo-controls button {
    padding: 6px 12px;
    font-size: 12px;
  }

  .demo-container {
    height: calc(100vh - 100px);
  }

  .debug-panel {
    padding: 12px;
  }

  .debug-panel h3 {
    font-size: 16px;
    margin-bottom: 12px;
  }

  .debug-section {
    margin-bottom: 12px;
    padding: 10px;
  }

  .debug-section h4 {
    font-size: 13px;
    margin-bottom: 6px;
  }

  .debug-section p {
    font-size: 12px;
  }

  .debug-event {
    padding: 4px 6px;
    font-size: 10px;
  }

  .debug-toggle {
    bottom: 16px;
    right: 16px;
    width: 44px;
    height: 44px;
    border-radius: 22px;
    font-size: 18px;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .demo-header {
    padding: 8px 16px;
    padding-top: max(8px, env(safe-area-inset-top));
  }

  .demo-header h1 {
    font-size: 16px;
    margin-bottom: 4px;
  }

  .demo-controls {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 6px;
  }

  .demo-controls select,
  .demo-controls button {
    flex: 1;
    min-width: 120px;
  }

  .demo-container {
    height: calc(100vh - 80px);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .demo-controls button,
  .debug-toggle {
    min-height: 44px;
  }

  .demo-controls button:active,
  .debug-toggle:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* 防止iOS双击缩放 */
  .ai-chat-demo {
    touch-action: manipulation;
  }

  /* 优化滚动性能 */
  .debug-events {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}

/* 自定义操作按钮样式 */
.custom-action {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
  border-color: rgba(14, 165, 233, 0.2) !important;
}

.custom-action:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%) !important;
  border-color: rgba(14, 165, 233, 0.3) !important;
}

.action-text {
  font-size: 14px;
  line-height: 1;
  display: inline-block;
}

/* 自定义工具按钮样式 */
.custom-tool {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%) !important;
  border-color: rgba(34, 197, 94, 0.2) !important;
}

.custom-tool:hover {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%) !important;
  border-color: rgba(34, 197, 94, 0.3) !important;
}

.tool-text {
  font-size: 16px;
  line-height: 1;
  display: inline-block;
}

/* 用户消息额外内容样式 */
.user-message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 6px 12px;
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.05) 0%, rgba(18, 94, 255, 0.02) 100%);
  border-radius: 8px;
  font-size: 12px;
  color: #6b7280;
}

.message-time {
  font-weight: 500;
}

.message-status {
  color: #10b981;
  font-weight: 500;
}

.message-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.message-action-btn:hover {
  background: rgba(18, 94, 255, 0.1);
  transform: scale(1.1);
}
</style>
