<template>
  <div class="config-panel" :class="{ 'config-panel--open': isOpen }">
    <div class="config-header">
      <h3>AI对话配置</h3>
      <button class="config-close" @click="$emit('close')">×</button>
    </div>
    
    <div class="config-content">
      <!-- 预设配置 -->
      <div class="config-section">
        <h4>预设配置</h4>
        <div class="config-presets">
          <button
            v-for="(preset, key) in presetConfigs"
            :key="key"
            class="preset-btn"
            :class="{ active: currentPreset === key }"
            @click="applyPreset(key)"
          >
            {{ preset.name }}
          </button>
        </div>
        <p class="preset-description">{{ presetConfigs[currentPreset]?.description }}</p>
      </div>

      <!-- 基础配置 -->
      <div class="config-section">
        <h4>基础设置</h4>
        <div class="config-item">
          <label>主题</label>
          <select v-model="localConfig.theme" @change="updateConfig">
            <option value="light">浅色</option>
            <option value="dark">深色</option>
            <option value="auto">自动</option>
          </select>
        </div>
        <div class="config-item">
          <label>布局</label>
          <select v-model="localConfig.layout" @change="updateConfig">
            <option value="desktop">桌面端</option>
            <option value="mobile">移动端</option>
            <option value="responsive">响应式</option>
          </select>
        </div>
        <div class="config-item">
          <label>主色调</label>
          <input 
            type="color" 
            v-model="localConfig.styles.primaryColor" 
            @change="updateConfig"
          />
        </div>
      </div>

      <!-- 功能开关 -->
      <div class="config-section">
        <h4>功能开关</h4>
        <div class="config-switches">
          <div class="config-switch" v-for="(value, key) in localConfig.features" :key="key">
            <label>
              <input 
                type="checkbox" 
                v-model="localConfig.features[key]" 
                @change="updateConfig"
              />
              <span>{{ getFeatureLabel(key) }}</span>
            </label>
          </div>
        </div>
      </div>

      <!-- API配置 -->
      <div class="config-section">
        <h4>API设置</h4>
        <div class="config-item">
          <label>基础URL</label>
          <input 
            type="text" 
            v-model="localConfig.api.baseUrl" 
            @change="updateConfig"
            placeholder="https://api.example.com"
          />
        </div>
        <div class="config-item">
          <label>聊天接口</label>
          <input 
            type="text" 
            v-model="localConfig.api.chatEndpoint" 
            @change="updateConfig"
            placeholder="/chat"
          />
        </div>
        <div class="config-item">
          <label>超时时间(ms)</label>
          <input 
            type="number" 
            v-model="localConfig.api.timeout" 
            @change="updateConfig"
            placeholder="30000"
          />
        </div>
      </div>

      <!-- 兼容性配置 -->
      <div class="config-section">
        <h4>兼容性设置</h4>
        <div class="config-item">
          <label>
            <input 
              type="checkbox" 
              v-model="localConfig.compatibility.useOriginalAPI" 
              @change="updateConfig"
            />
            使用原有API
          </label>
        </div>
        <div class="config-item">
          <label>会话ID</label>
          <input 
            type="text" 
            v-model="localConfig.compatibility.conversationId" 
            @change="updateConfig"
            placeholder="conversation-id"
          />
        </div>
        <div class="config-item">
          <label>分类</label>
          <input 
            type="text" 
            v-model="localConfig.compatibility.category" 
            @change="updateConfig"
            placeholder="category"
          />
        </div>
        <div class="config-item">
          <label>智能体ID</label>
          <input 
            type="text" 
            v-model="localConfig.compatibility.agentId" 
            @change="updateConfig"
            placeholder="agent-id"
          />
        </div>
        <div class="config-item">
          <label>模型ID</label>
          <input 
            type="text" 
            v-model="localConfig.compatibility.modelId" 
            @change="updateConfig"
            placeholder="model-id"
          />
        </div>
      </div>

      <!-- 行为配置 -->
      <div class="config-section">
        <h4>行为设置</h4>
        <div class="config-item">
          <label>最大消息数</label>
          <input 
            type="number" 
            v-model="localConfig.behavior.maxMessages" 
            @change="updateConfig"
            placeholder="100"
          />
        </div>
        <div class="config-item">
          <label>打字速度(ms)</label>
          <input 
            type="number" 
            v-model="localConfig.behavior.typingSpeed" 
            @change="updateConfig"
            placeholder="50"
          />
        </div>
        <div class="config-item">
          <label>上下文长度</label>
          <input 
            type="number" 
            v-model="localConfig.behavior.contextLength" 
            @change="updateConfig"
            placeholder="4000"
          />
        </div>
      </div>
    </div>

    <div class="config-actions">
      <button class="config-btn config-btn--primary" @click="saveConfig">保存配置</button>
      <button class="config-btn config-btn--secondary" @click="resetConfig">重置配置</button>
      <button class="config-btn config-btn--secondary" @click="exportConfig">导出配置</button>
      <input 
        type="file" 
        ref="importInput" 
        accept=".json" 
        style="display: none" 
        @change="importConfig"
      />
      <button class="config-btn config-btn--secondary" @click="$refs.importInput.click()">导入配置</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { AIChatConfig } from '../types'

interface Props {
  isOpen?: boolean
  config?: AIChatConfig
}

interface Emits {
  (e: 'close'): void
  (e: 'update:config', config: AIChatConfig): void
  (e: 'save', config: AIChatConfig): void
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  config: () => ({})
})

const emit = defineEmits<Emits>()

// 基于现有真实功能的默认配置
const getDefaultConfig = (): AIChatConfig => ({
  theme: 'light',
  layout: 'responsive',

  // 基于现有实现的功能开关
  features: {
    // 已实现的核心功能
    feedback: true,           // MessageItem中已实现反馈功能
    voiceInput: true,         // InputArea中已实现语音输入
    fileUpload: true,         // InputArea中已实现文件上传
    knowledgeBase: true,      // InputArea中已实现知识库选择
    workflow: true,           // useChat中已实现工作流节点
    thinking: true,           // MessageItem中已实现思考过程显示
    messageEdit: true,        // useChat中已实现消息编辑
    stopGeneration: true,     // useChat中已实现停止生成
    copy: true,              // 主组件中已实现复制功能
    regenerate: true,        // useChat中已实现重新生成
    export: true,            // useChat中已实现导出功能
    quickPrompts: true,      // InputArea中已实现快捷提示
    streaming: true,         // useChat中已实现流式响应

    // 待扩展功能（暂时关闭）
    audio: false,            // 音频播放功能
    translation: false,      // 翻译功能
    search: false,          // 搜索功能
    annotation: false,      // 标注功能
    multiTurn: true,        // 多轮对话（基础功能）
    contextMemory: true     // 上下文记忆（基础功能）
  },

  styles: {
    primaryColor: '#125EFF',
    backgroundColor: '#ffffff',
    messageStyle: 'bubble',
    avatarStyle: 'circle',
    fontSize: 'medium',
    messageSpacing: 'normal',
    borderRadius: 'medium',
    animation: 'fade'
  },

  // 基于现有API的配置
  api: {
    baseUrl: import.meta.env.VITE_GLOB_API_URL || '',
    chatEndpoint: '/emind/conversationContent/text_answers',  // 现有对话API端点
    uploadEndpoint: '/resource/files/upload',                 // 现有上传端点
    feedbackEndpoint: '/emind/conversation/feedback',         // 反馈接口
    voiceEndpoint: '/sse_api/speech_recognition',             // 语音识别接口
    audioEndpoint: '/sse_api/text_to_speach',                 // 文本转语音接口
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  },

  // 兼容原有chat组件的配置
  compatibility: {
    useOriginalAPI: true,     // 默认使用原有API
    enableLegacyMode: false,
    conversationId: `chat-${Date.now()}`,
    category: '0',
    agentId: '1950834085600997378',  // 使用真实的智能体ID
    modelId: 'gpt-3.5-turbo',
    modelTemp: 0.7,
    maxLength: 2000,
    promptTemplate: '',
    multiTurnFlag: true
  },

  behavior: {
    autoScroll: true,
    showTimestamp: true,
    enableMarkdown: true,
    maxMessages: 100,
    typingSpeed: 50,
    autoSave: true,
    contextLength: 4000,
    retryAttempts: 3,
    debounceDelay: 300
  },

  placeholders: {
    input: '请输入您的问题...',
    thinking: '正在思考中...',
    uploading: '正在上传文件...',
    recording: '正在录音...',
    generating: '正在生成回复...'
  }
})

// 预设配置
const presetConfigs = {
  default: {
    name: '默认配置',
    description: '包含所有已实现的功能，适合大多数场景',
    config: getDefaultConfig()
  },
  minimal: {
    name: '简约模式',
    description: '只保留基础聊天功能，界面简洁',
    config: {
      ...getDefaultConfig(),
      features: {
        feedback: false,
        voiceInput: false,
        fileUpload: false,
        knowledgeBase: false,
        workflow: false,
        thinking: false,
        messageEdit: false,
        stopGeneration: true,
        copy: true,
        regenerate: false,
        export: false,
        quickPrompts: false,
        streaming: true,
        audio: false,
        translation: false,
        search: false,
        annotation: false,
        multiTurn: true,
        contextMemory: false
      },
      styles: {
        ...getDefaultConfig().styles,
        messageStyle: 'card',
        animation: 'none'
      }
    }
  },
  professional: {
    name: '专业版',
    description: '启用所有功能，适合专业用户和开发调试',
    config: {
      ...getDefaultConfig(),
      features: {
        feedback: true,
        voiceInput: true,
        fileUpload: true,
        knowledgeBase: true,
        workflow: true,
        thinking: true,
        messageEdit: true,
        stopGeneration: true,
        copy: true,
        regenerate: true,
        export: true,
        quickPrompts: true,
        streaming: true,
        audio: true,
        translation: true,
        search: true,
        annotation: true,
        multiTurn: true,
        contextMemory: true
      },
      behavior: {
        ...getDefaultConfig().behavior,
        maxMessages: 200,
        contextLength: 8000
      }
    }
  },
  legacy: {
    name: '兼容模式',
    description: '完全兼容原有chat组件，使用原有API',
    config: {
      ...getDefaultConfig(),
      compatibility: {
        ...getDefaultConfig().compatibility,
        useOriginalAPI: true,
        enableLegacyMode: true
      },
      features: {
        ...getDefaultConfig().features,
        workflow: false,
        thinking: false,
        quickPrompts: false
      }
    }
  }
}

// 当前预设
const currentPreset = ref('default')

// 本地配置副本
const localConfig = reactive<AIChatConfig>(getDefaultConfig())

// 检测当前配置匹配哪个预设
const detectCurrentPreset = () => {
  // 简单检测：如果useOriginalAPI为true，则为legacy模式
  if (localConfig.compatibility?.useOriginalAPI && localConfig.compatibility?.enableLegacyMode) {
    currentPreset.value = 'legacy'
  } else if (!localConfig.features?.feedback && !localConfig.features?.voiceInput) {
    currentPreset.value = 'minimal'
  } else if (localConfig.features?.audio && localConfig.features?.translation) {
    currentPreset.value = 'professional'
  } else {
    currentPreset.value = 'default'
  }
}

// 监听外部配置变化
watch(() => props.config, (newConfig) => {
  Object.assign(localConfig, newConfig)
  // 检测当前配置匹配哪个预设
  detectCurrentPreset()
}, { deep: true, immediate: true })

// 功能标签映射
const featureLabels: Record<string, string> = {
  feedback: '反馈功能',
  voiceInput: '语音输入',
  fileUpload: '文件上传',
  knowledgeBase: '知识库',
  workflow: '工作流',
  thinking: '思考过程',
  messageEdit: '消息编辑',
  stopGeneration: '停止生成',
  copy: '复制功能',
  regenerate: '重新生成',
  export: '导出功能',
  quickPrompts: '快捷提示',
  streaming: '流式输出',
  audio: '音频播放',
  translation: '翻译功能',
  search: '搜索功能',
  annotation: '标注功能',
  multiTurn: '多轮对话',
  contextMemory: '上下文记忆'
}

const getFeatureLabel = (key: string) => {
  return featureLabels[key] || key
}

// 应用预设配置
const applyPreset = (presetKey: keyof typeof presetConfigs) => {
  const preset = presetConfigs[presetKey]
  if (preset) {
    Object.assign(localConfig, preset.config)
    currentPreset.value = presetKey
    updateConfig()
  }
}

const updateConfig = () => {
  emit('update:config', { ...localConfig })
}

const saveConfig = () => {
  emit('save', { ...localConfig })
  localStorage.setItem('ai-chat-config', JSON.stringify(localConfig))
}

const resetConfig = () => {
  // 重置为默认配置
  Object.assign(localConfig, getDefaultConfig())
  currentPreset.value = 'default'
  updateConfig()
}

const exportConfig = () => {
  const dataStr = JSON.stringify(localConfig, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = 'ai-chat-config.json'
  link.click()
  URL.revokeObjectURL(url)
}

const importInput = ref<HTMLInputElement>()

const importConfig = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target?.result as string)
      Object.assign(localConfig, config)
      updateConfig()
    } catch (error) {
      console.error('Failed to import config:', error)
      alert('配置文件格式错误')
    }
  }
  reader.readAsText(file)
}
</script>

<style scoped>
.config-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  z-index: 1000;
  overflow-y: auto;
}

.config-panel--open {
  right: 0;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.config-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.config-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.config-content {
  padding: 20px;
}

.config-section {
  margin-bottom: 30px;
}

.config-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.config-item {
  margin-bottom: 15px;
}

.config-item label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #666;
}

.config-item input,
.config-item select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.config-switches {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.config-switch label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.config-switch input[type="checkbox"] {
  width: auto;
}

.config-actions {
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.config-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.config-btn--primary {
  background: #125EFF;
  color: white;
}

.config-btn--primary:hover {
  background: #0d4ed4;
}

.config-btn--secondary {
  background: #f5f5f5;
  color: #666;
}

.config-btn--secondary:hover {
  background: #e8e8e8;
}

/* 预设配置样式 */
.config-presets {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 15px;
}

.preset-btn {
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.preset-btn:hover {
  border-color: #125EFF;
  color: #125EFF;
}

.preset-btn.active {
  border-color: #125EFF;
  background: #125EFF;
  color: white;
}

.preset-description {
  font-size: 12px;
  color: #999;
  margin: 0;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #125EFF;
}
</style>
