<script setup>
import {computed, onMounted, ref} from 'vue'
import {NDivider, NEllipsis, NInput, useMessage, NSpin} from 'naive-ui'
import {useRouter} from 'vue-router'
import notbookmarked from '@/assets/toolboxPage/notbookmarked.png'
import collected from '@/assets/toolboxPage/collected.png'
import icon1 from '@/assets/applicationPage/icon1.png'
import {getAgentlistAll} from '@/api/workShop'
import {delcollectionApi, setcollectionApi} from '@/api/applicationPage'
import {debounce} from '@/utils/functions/debounce'

import {getpictureDictTextByCodeValue} from '@/utils/microFrontEnd'
import {getlinksApi} from '@/api/tools'

import {useToolsStore} from '@/store'

const ToolsStore = useToolsStore()
const message = useMessage()

const router = useRouter()
const searchvalue = ref('')
const loadingshow = ref(false)
const moretagarr = ref([
	{name: '全部', ischeck: true, value: 0},
])
const applicationarr = ref([])

function jumpfun(item) {
	console.log(item)
	const toolitem = {
		...item,
		name: item.name,
		title: `你好~我是${item.name}`,
		des: item.openingWords,
		agentId: item.id,
		openingQuestionArr: [],
		icon: icon1,
		modelSessionId: item.modelId,
		modelTemp: item.modelTemp,
		maxLength: item.maxLength,
		promptTemplate: item.promptTemplate,
		multiTurnFlag: item.multiTurnFlag,
	}
	toolitem.openingQuestionArr = toolitem.openingQuestionArr.concat(JSON.parse(item.preQuestions || '[]'))
	console.log(toolitem)
	ToolsStore.updateToolInfo(toolitem)
	if(item.buildCategory == '1'){
		router.push({
		path: '/workflowChat',
		query: {
			id: item.id,
		},
	})
	}else{
		router.push({
				path: '/tankChat',
				query: {
					id: item.id,
				},
			})
	}
	
}

function jumpPage(url) {
	if (url)
		router.push(url)

	else
		router.go(-1)
}

const changecollectfun = (record) => {

	if (record.isCollection) {
		delcollectionApi({collectionId: record.collectionId}).then((res) => {
			getListfun()
		})
	} else {
		setcollectionApi({mainBodyId: record.id, category: '2'}).then((res) => {
			getListfun()
		})
	}
}

const changmoretagfun = (index) => {
	moretagarr.value.forEach((item, i) => {
		item.ischeck = i === index
	})
}

function getListfun() {
	loadingshow.value = true;

	getAgentlistAll({name: searchvalue.value, status: 1,agentPermissionFlag:1}).then((res) => {
		loadingshow.value = false;
		console.log(res)

		if (res.code == 0) {
			res.data.forEach((item) => {
				item.iconUrl = getpictureDictTextByCodeValue('profile_picture', item.icon)
				// 修改此处，生成三位数随机数
				// item.heatNum = Math.floor(Math.random() * 900) + 100;
			})
			applicationarr.value = res.data
		} else {
			message.error(res.message)
		}
	}).catch(() => {
		loadingshow.value = false;
	})
}

const searchTap = debounce(getListfun, 500)
onMounted(async () => {
	getListfun()
})
</script>

<template>
	<div class="app p-8 pr-[37px] pl-[40px]">
		<n-spin :show="loadingshow">
			<header class="bothends flex justify-between items-center">
				<div class="title h-9 font-semibold text-[26px] text-[#2f3033] leading-9 flex items-center">
					<img alt="" class="w-[22px] h-[22px] mr-2" src="@/assets/toolboxPage/titicon.png"> 智教共创汇
				</div>
				<div class="w-[400px] h-12">
					<NInput
						v-model:value="searchvalue" class="modern-search-input"
						clearable
						placeholder="搜索智能体"
						round
						size="large"
						@update:value="getListfun"
					>
						<template #prefix>
							<div class="search-prefix-icon">
								<img class="w-[18px] h-[18px] opacity-60" src="../../assets/toolboxPage/SearchOutline.png">
							</div>
						</template>
						<template #suffix>
							<div class="search-suffix-btn">
								<img class="w-[18px] h-[18px] cursor-pointer" src="../../assets/toolboxPage/SearchOutline.png"
										 @click="searchTap">
							</div>
						</template>
					</NInput>
				</div>
			</header>

			<div class="section-title">
				<img class="w-6 h-6 mr-3" src="@/assets/toolboxPage/collecticon.png">
				我的收藏
			</div>

			<div class="collectbox flex flex-wrap">
				<div
					v-for="(item, index) in applicationarr.filter(
						(item) => item.isCollection
					)"
					:key="index"
					class="collect-tag"
					@click="jumpfun(item)"
				>
					<img :src="icon1" class="collect-icon"/>
					<p>{{ item.name }}</p>
				</div>
			</div>

			<div class="section-title section-title-top">
				<img class="w-6 h-6 mr-3" src="@/assets/toolboxPage/moreicon.png">
				发现更多
			</div>

			<div class="collectbox flex flex-wrap">
				<div v-for="(item, index) in moretagarr" :key="index"
						 :class="{ 'category-tag-active': item.ischeck }"
						 class="category-tag"
						 @click="changmoretagfun(index)">
					{{ item.name }}
				</div>
			</div>
			<div v-if="applicationarr.length" class="applicationrow">
				<div v-for="(item, index) in applicationarr" :key="index"
						 class="application-card"
						 @click="jumpfun(item)">
					<div class="card-content">
						<div class="app-icon-wrapper">
							<img :src="icon1" class="app-icon">
						</div>
						<div class="app-info">
							<h3 class="app-name">{{ item.name }}</h3>
							<div class="app-description">
								<NEllipsis :line-clamp="2">
									{{ item.description }}
									<template #tooltip>
										<div style="text-align: center;width: 250px;">
											{{ item.description }}
										</div>
									</template>
								</NEllipsis>
							</div>
						</div>
					</div>

					<div class="card-divider"></div>

					<div class="app-meta">
						<div class="collect-action" @click.stop="changecollectfun(item)">
							<img :src="item.isCollection ? collected : notbookmarked" class="meta-icon">
							<span>{{ item.isCollection ? '已收藏' : '收藏' }}</span>
						</div>
					</div>
				</div>
			</div>
			<div v-else class="empty-state">
      <span class="empty-svg">
        <svg fill="none" height="96" viewBox="0 0 96 96" width="96" xmlns="http://www.w3.org/2000/svg">
          <rect fill="#eaf1ff" height="48" rx="16" width="72" x="12" y="28"/>
          <rect fill="#b6d0ff" height="24" rx="8" width="48" x="24" y="40"/>
          <circle cx="36" cy="52" fill="#fff" r="4"/>
          <circle cx="60" cy="52" fill="#fff" r="4"/>
          <rect fill="#b6d0ff" height="4" rx="2" width="8" x="44" y="64"/>
          <rect fill="#b6d0ff" height="12" rx="6" width="16" x="40" y="20"/>
          <rect fill="#b6d0ff" height="8" rx="2" width="4" x="46" y="12"/>
        </svg>
      </span>
				<div class="empty-text">暂无智能体！</div>
			</div>
		</n-spin>
	</div>
</template>

<style lang="less" scoped>
// 现代化搜索框样式
.modern-search-input {
	:deep(.n-input-wrapper) {
		padding-right: 4px;
		padding-left: 16px;
		border: none;
		background: transparent;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
		transition: all 0.3s ease;
		border-radius: 3rem;

		&:hover {
			box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
		}

		&.n-input-wrapper--focus {
			box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1);
		}
	}

	:deep(.n-input__input-el) {
		font-size: 15px;
		color: #2f3033;

		&::placeholder {
			color: #9ca3af;
			font-weight: 400;
		}
	}
}

.search-prefix-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 8px;
}

.search-suffix-btn {
	width: 36px;
	height: 36px;
	background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.2s ease;

	&:hover {
		transform: scale(1.05);
		box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
	}

	&:active {
		transform: scale(0.98);
	}

	img {
		filter: brightness(0) invert(1);
	}
}

// 章节标题样式
.section-title {
	height: 36px;
	font-weight: 600;
	font-size: 20px;
	color: #1f2937;
	line-height: 36px;
	display: flex;
	align-items: center;
	margin-top: 32px;
	margin-bottom: 16px;

	&.section-title-top {
		margin-top: 40px;
	}
}

// 收藏标签样式
.collect-tag {
	min-width: 120px;
	height: 40px;
	background: #125eff14;
	border: 1px solid #125eff26;
	border-radius: 10px;

	line-height: 40px;
	text-align: center;
	margin-right: 12px;
	margin-bottom: 12px;
	padding: 0 16px;
	cursor: pointer;
	transition: all 0.2s ease;
	display: flex;
	justify-content: space-evenly;

	.collect-icon {
		width: 22px;
		height: 22px;
		margin-top: 8px;
		margin-right: 4px;
	}

	> p {
		font-weight: 500;
		font-size: 14px;
	}

	&:hover {
		background: linear-gradient(135deg, #125eff 0%, #1e7fff 100%);
		color: #ffffff;
		transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(18, 94, 255, 0.25);
	}
}

// 分类标签样式
.category-tag {
	width: 128px;
	height: 40px;
	background: #ffffff;
	border: 1px solid #e5e7eb;
	border-radius: 20px;
	font-weight: 500;
	font-size: 14px;
	color: #6b7280;
	line-height: 40px;
	text-align: center;
	margin-right: 12px;
	cursor: pointer;
	transition: all 0.2s ease;

	&:hover {
		border-color: #125EFF;
		color: #125EFF;
		box-shadow: 0 2px 8px rgba(18, 94, 255, 0.1);
	}

	&.category-tag-active {
		background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
		border-color: #125EFF;
		color: #ffffff;
		box-shadow: 0 4px 12px rgba(18, 94, 255, 0.25);
	}
}

// 应用卡片网格布局
.applicationrow {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(330px, 1fr));
	gap: 20px;
	margin-top: 32px;
}

// 现代化应用卡片设计
.application-card {
	background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
	border: 1px solid #e8ecf0;
	border-radius: 16px;
	padding: 24px;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

	&:hover {
		background: #ffffff;
		border-color: #125EFF;
		box-shadow: 0 8px 32px rgba(18, 94, 255, 0.12);
		transform: translateY(-2px);
	}
}

.card-content {
	display: flex;
	align-items: flex-start;
	margin-bottom: 16px;
}

.app-icon-wrapper {
	position: relative;
	margin-right: 20px;

	// 默认状态的微妙背景
	&::before {
		content: '';
		position: absolute;
		top: -6px;
		left: -6px;
		right: -6px;
		bottom: -6px;
		background: linear-gradient(135deg, rgba(18, 94, 255, 0.05) 0%, rgba(30, 127, 255, 0.02) 100%);
		border-radius: 18px;
		opacity: 1;
		transition: all 0.3s ease;
	}

	.application-card:hover &::before {
		background: linear-gradient(135deg, rgba(18, 94, 255, 0.15) 0%, rgba(30, 127, 255, 0.08) 100%);
		transform: scale(1.05);
	}
}

.app-icon {
	width: 64px;
	height: 64px;
	border-radius: 12px;
	position: relative;
	z-index: 1;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;

	.application-card:hover & {
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
		transform: scale(1.02);
	}
}

.app-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.app-name {
	font-size: 18px;
	font-weight: 600;
	color: #1f2937;
	line-height: 24px;
	margin: 0 0 8px 0;
	transition: color 0.2s ease;

	.application-card:hover & {
		color: #125EFF;
	}
}

.app-description {
	font-size: 14px;
	color: #6b7280;
	line-height: 20px;
	margin: 0;
}

.card-divider {
	height: 1px;
	background: linear-gradient(90deg, transparent 0%, #e5e7eb 20%, #e5e7eb 80%, transparent 100%);
	margin: 16px 0;
}

.app-meta {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.collect-action {
	display: flex;
	align-items: center;
	font-size: 13px;
	color: #6b7280;
	cursor: pointer;
	padding: 4px 8px;
	border-radius: 6px;
	transition: all 0.2s ease;

	.meta-icon {
		width: 14px;
		height: 14px;
		margin-right: 6px;
		opacity: 0.8;
	}

	&:hover {
		background: rgba(18, 94, 255, 0.08);
		color: #125EFF;

		.meta-icon {
			opacity: 1;
		}
	}
}

.empty-state {
	width: 100%;
	min-height: 320px;
	padding: 48px 0;
	margin: 40px auto;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #f8faff 0%, #eef4ff 100%);
	border-radius: 24px;
	box-shadow: 0 4px 16px rgba(18, 94, 255, 0.08);

	.empty-svg {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20px;

		svg {
			display: block;
			width: 96px;
			height: 96px;
			opacity: 0.85;
		}
	}

	.empty-text {
		color: #7a8ca3;
		font-size: 18px;
		font-weight: 500;
		text-align: center;
		letter-spacing: 1px;
	}
}
</style>
