<script lang="ts" setup>
import {computed, h, nextTick, onMounted, ref, useTemplateRef} from "vue";
import {useRouter, useRoute} from "vue-router";
import {
	NSpin,
	NStep,
	NSteps,
	NEllipsis,
	NDropdown,
	NButton,
	NBadge,
	NTabs,
	NTabPane,
	NPopover,
	NBreadcrumb,
	NBreadcrumbItem,
	NEmpty,
	NModal,
	useMessage,
	useDialog,
} from "naive-ui";
import VideoPlayer from "@/components/common/VideoPlayer/index.vue";
import {useToolsStore, usestretchoutStore} from "@/store";
import {infoStore} from "@/store/modules/info";
import {SvgIcon} from "@/components/common";
import nd from "@/assets/chat/nd.png";
import zd from "@/assets/chat/zd.png";
import kj from "@/assets/toolboxPage/kj.png";
import sp from "@/assets/toolboxPage/sp.png";
import jc from "@/assets/toolboxPage/jc.png";
import kja from "@/assets/toolboxPage/kja.png";
import spa from "@/assets/toolboxPage/spa.png";
import jca from "@/assets/toolboxPage/jca.png";
import questions from "./components/questions.vue";
import {
	addTextbookAnnotations,
	editTextbookAnnotations,
	edlTextbookAnnotations,
	textbookById,
	knowledge,
	agentById,
	generateSummarizeProblem,
	getGenerateProblem,
} from "@/api/courseware";
import {addConversation} from "@/api/workShop";
import {debounce} from "@/utils/functions/debounce";

const message = useMessage();
const dialog = useDialog();
const router = useRouter();
const route = useRoute();
const info = infoStore();
const ToolsStore = useToolsStore();
const useTretchOut = usestretchoutStore();
const uid = route.query.id;
const main = ref<any>({});

// 视频相关配置
const timeToEndThreshold = ref(10); // 倒数几秒触发回调，可以根据需要修改

// 视频加载状态管理
const isVideoLoading = ref(true);
const videoLoadingProgress = ref(0);
const videoLoadingStageText = ref("正在初始化视频播放器...");
// 视频加载阶段描述信息数组
const videoLoadingStages = [
	{progress: 0, text: "正在初始化视频播放器..."},
	{progress: 10, text: "正在连接视频服务器..."},
	{progress: 25, text: "正在获取视频信息..."},
	{progress: 40, text: "正在缓冲视频数据..."},
	{progress: 60, text: "正在加载视频元数据..."},
	{progress: 80, text: "正在准备视频播放..."},
	{progress: 95, text: "即将完成加载..."},
	{progress: 100, text: "加载完成！"},
];
let videoProgressInterval: number | null = null;

// tab切换状态
const currentTab = ref("0");

// 视频知识点数据（格式化后）
const knowledgePoints = ref<any[]>([]);
// 是否播放完视频（用于问答标记）
const isVideoCompleted = ref(false);

// 问答组件状态管理：1-默认状态(预设问题), 2-视频完成状态(总结问题)
const questionState = ref(1);

const breadcrumb = computed(() => {
	return info.breadcrumb;
});

function jumpPage(url: any) {
	if (url) router.push(url);
	else router.push("/videoStudyHelper");
}

const show = ref(false);
const loadTap = (flag: any) => {
	show.value = flag;
};

const StepsList = ref([
	{
		name: "课件材料上传",
	},
	{
		name: "材料格式转换",
	},
	{
		name: "材料内容识别理解",
	},
	{
		name: "知识点解析抽取",
	},
	{
		name: "知识点标记展示",
	},
]);
const renderMenuIcon = (url: any) => {
	return () =>
		h("img", {
			src: url,
			width: 12,
			height: 12,
			style: {verticalAlign: "middle", marginLeft: "4px"}, // 增加样式使其对齐
		});
};

// 视频播放完成回调
const onVideoTimeToEnd = async () => {
	isVideoCompleted.value = true;
	// 检查是否已在问答tab且有用户聊天消息
	if (currentTab.value === "1" && questionsRef.value?.hasUserMessages()) {
		// 弹出确认弹窗
		dialog.warning({
			title: "确认操作",
			content: "模型即将发起提问，此操作会清除对话历史，确认执行？",
			positiveText: "确认",
			negativeText: "取消",
			onPositiveClick: async () => {
				executeVideoEndLogicYesDebounce()
			},
			onNegativeClick: () => {
				// 用户取消，不执行任何操作
				console.log("用户取消了总结问题生成");
			},
		});
	} else {
		// 不在问答tab或没有用户消息，直接执行逻辑
		await executeVideoEndLogic();
	}
};

const executeVideoEndLogicYes = async () => {
	// 停止输出
	if (questionsRef.value?.stopAllOutput) {
		await questionsRef.value.stopAllOutput();
	}
	questionState.value = 2;
	// 用户确认，继续执行生成总结问题
	await executeVideoEndLogic();
}
const executeVideoEndLogicYesDebounce = debounce(executeVideoEndLogicYes, 600);

// 执行视频完成后的逻辑
const executeVideoEndLogic = async () => {
	// 切换到问答tab并生成总结问题
	currentTab.value = "1";
	await nextTick();
	await tabsTap("1");
};

// 视频播放开始回调（从完播状态恢复播放时）
const onVideoPlay = () => {
	if (isVideoCompleted.value) {
		// 如果之前已完播，现在重新播放，则重置状态
		isVideoCompleted.value = false;
		questionState.value = 1;
		// 如果当前在问答tab，则清空聊天并重置为状态1
		if (currentTab.value === "1") {
			nextTick(() => {
				questionsRef.value?.clearAndInitState1();
			});
		}
	}
};

// 视频加载完成回调
const onVideoLoaded = () => {
	// 清除进度条定时器
	if (videoProgressInterval) {
		clearInterval(videoProgressInterval);
		videoProgressInterval = null;
	}

	// 设置加载完成
	videoLoadingProgress.value = 100;
	videoLoadingStageText.value = "加载完成！";

	// 延迟后隐藏加载动画
	setTimeout(() => {
		isVideoLoading.value = false;
		videoLoadingProgress.value = 0; // 为下次加载重置
	}, 300);
};

// 开始视频加载动画
const startVideoLoadingAnimation = () => {
	isVideoLoading.value = true;
	videoLoadingProgress.value = 0;

	// 清除之前的定时器
	if (videoProgressInterval) {
		clearInterval(videoProgressInterval);
	}

	// 设置新的定时器来更新进度
	videoProgressInterval = setInterval(() => {
		if (videoLoadingProgress.value < 95) {
			videoLoadingProgress.value += 5;
			// 根据当前进度更新加载阶段描述
			const currentStage = videoLoadingStages.find(
				(stage) => stage.progress >= videoLoadingProgress.value
			);
			if (currentStage) {
				videoLoadingStageText.value = currentStage.text;
			}
		}
	}, 200) as unknown as number;
};

// 将position时间字符串转换为秒数
const parseTimeToSeconds = (timeStr: string): number => {
	const parts = timeStr.split(":");
	if (parts.length === 3) {
		// HH:MM:SS格式
		const hours = parseInt(parts[0]);
		const minutes = parseInt(parts[1]);
		const seconds = parseInt(parts[2]);
		return hours * 3600 + minutes * 60 + seconds;
	} else if (parts.length === 2) {
		// MM:SS格式
		const minutes = parseInt(parts[0]);
		const seconds = parseInt(parts[1]);
		return minutes * 60 + seconds;
	}
	return 0;
};

// 核心知识
const contentList = ref([]);

// 控制每个知识点的展开/收起状态
const expandedItems = ref<{ [key: number]: boolean }>({});

// 控制悬停状态
const hoveredItems = ref<{ [key: number]: boolean }>({});

// VideoPlayer组件引用
const videoPlayerRef = ref();

// 设置悬停状态
const setHoverState = (index: number, isHovered: boolean) => {
	hoveredItems.value[index] = isHovered;
};

// 切换知识点展开状态
const toggleExpand = (index: number) => {
	expandedItems.value[index] = !expandedItems.value[index];
};

//点击定位原文（视频时间点）
const positionTap = (item: any) => {
	console.log(item.position);
	// 跳转到视频对应时间点
	if (videoPlayerRef.value && item.position) {
		const timeInSeconds = parseTimeToSeconds(item.position);
		videoPlayerRef.value.seekToTime(timeInSeconds);
	}
};

// 点击核心知识图标获取下拉
const popoverShow = async (flag: boolean, item: any) => {
	console.log(flag);
	if (flag) {
		item.childrenFlag = false;
		const idsStr = info.breadcrumb.map((item: any) => item.id).join(",");
		let res = await knowledge({content: item.content, textbookId: idsStr});
		item.children = res.data;
		item.childrenFlag = true;
		console.log(item.children);
	}
};

// 点击核心知识下拉
const goTap = (item: any) => {
	console.log(item);
	if (breadcrumb.value?.length >= 3) {
		message.warning("当前仅支持 3 级材料拓展哦，请专注于当前学习~");
		return;
	}
	let arr = info.breadcrumb;
	arr.push({id: item.id, title: item.title, textbookType: item.textbookType});
	info.setBreadcrumb(arr);
	if (item.textbookType == 2) {
		router.push({
			path: "/videoStudyHelperChat",
			query: {id: item.id},
		});
	} else {
		router.push({
			path: "/coursewareChat",
			query: {id: item.id},
		});
	}
};

// 点击面包屑
const breadcrumbTap = (item: any, index: any) => {
	let arr = info.breadcrumb;
	arr.splice(index + 1);
	info.setBreadcrumb(arr);
	if (item.textbookType == 2) {
		router.push({
			path: "/videoStudyHelperChat",
			query: {id: item.id},
		});
	} else {
		router.push({
			path: "/coursewareChat",
			query: {id: item.id},
		});
	}
};

// 暂存会话信息
let dialogue = ref({});
const questionsRef: any = useTemplateRef("questionsRef");

// 视频播放完成跳转到问答并生成预设问题
const generateProblemOnVideoEnd = async () => {
	if (currentTab.value !== "1") {
		currentTab.value = "1";
		await tabsTap("1");
		let res = await agentById("1950834085600997399");
		let obj = {
			summary: main.value.summary,
			id: main.value.id,
			agent: {
				modelId: res.data.modelId,
				modelTemp: res.data.modelTemp,
				maxLength: res.data.maxLength,
			},
		};
		await nextTick();
		await questionsRef.value.generateProblem(obj);
	}
};

// tabs改变 新建会话
const tabsTap = async (val: any) => {
	console.log(val);
	currentTab.value = val;
	if (val === "1") {
		const data = await addConversation({
			agentId: "1950834085600997399",
			title: "111",
			category: "1",
		});
		dialogue.value = data.data;
		await nextTick();

		// 根据视频播放状态决定问答组件状态
		if (!isVideoCompleted.value) {
			questionState.value = 1;
			await questionsRef.value.clearAndInitState1();
		} else {
			questionState.value = 2;
			await questionsRef.value.initState2();
		}
	}
};
// 标记
const options = ref<any[]>([]);
const init = async () => {
	let res = await textbookById(uid);
	main.value = res.data;
	options.value = main.value.textbookAnnotationsList;
	options.value = options.value.map((item: any) => {
		try {
			// 尝试解析position，增加错误处理防止JSON格式错误
			const parsedPosition = JSON.parse(item.position);
			return {
				...item,
				position: parsedPosition,
				text: item.content,
				title:
					item.content?.length > 50
						? item.content.substring(0, 50) + "..."
						: item.content,
			};
		} catch (error) {
			console.error(`解析position失败 for item ${item.id}:`, error);
			// 解析失败时可以选择保留原始值或做其他处理
			return {
				...item,
			};
		}
	});
	// 转换textbookKnowledgeList为VideoPlayer需要的格式
	if (main.value.textbookKnowledgeList) {
		knowledgePoints.value = main.value.textbookKnowledgeList.map(
			(item: any) => ({
				time: parseTimeToSeconds(item.position),
				title: item.content,
				description: item.description,
			})
		);
	}

	contentList.value = main.value.textbookKnowledgeList;
	console.log(main.value);
};
const filterThinkTags = (content: string) => {
  // 正则表达式匹配think标签及其内容
  // 考虑了可能的空格和不同大小写情况
  const thinkTagRegex = /<\s*think\s*>[^<]*<\s*\/\s*think\s*>/gi;

  // 替换匹配到的内容为空字符串
  return content.replace(thinkTagRegex, "");
};
/**
 * 将 Markdown 转换为纯文本
 * @param {string} text - 包含 Markdown 格式的文本
 * @returns {string} - 提取的纯文本
 */
function markdownToPlainText(text: any) {
  if (!text || typeof text !== "string") {
    return "";
  }
  text = filterThinkTags(text);

  try {
    // 使用临时DOM元素来解析HTML，然后提取纯文本
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = marked.parse(text);

    // 获取纯文本内容
    let plainText = tempDiv.textContent || tempDiv.innerText || "";

    // 清理多余的空白和换行
    plainText = plainText
      .replace(/\n{3,}/g, "\n\n")
      .replace(/^\s+|\s+$/g, "")
      .trim();

    return plainText;
  } catch (error) {
    console.error("Markdown转换失败:", error);

    // 如果解析失败，使用简化的方法
    let plainText = text
      // 移除代码块
      .replace(/```[\s\S]*?```/g, "")
      // 移除行内代码
      .replace(/`([^`]+)`/g, "$1")
      // 移除链接，保留文本
      .replace(/\[([^\]]+)\]\([^)]+\)/g, "$1")
      // 移除图片
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, "$1")
      // 移除标题标记
      .replace(/^#{1,6}\s+/gm, "")
      // 移除粗体和斜体标记
      .replace(/\*\*([^*]+)\*\*/g, "$1")
      .replace(/\*([^*]+)\*/g, "$1")
      .replace(/__([^_]+)__/g, "$1")
      .replace(/_([^_]+)_/g, "$1")
      // 移除删除线
      .replace(/~~([^~]+)~~/g, "$1")
      // 移除引用标记
      .replace(/^>\s+/gm, "")
      // 移除列表标记
      .replace(/^[\s]*[-*+]\s+/gm, "")
      .replace(/^[\s]*\d+\.\s+/gm, "")
      // 移除水平线
      .replace(/^---+$/gm, "")
      .replace(/^\*\*\*+$/gm, "")
      // 清理多余的空白
      .replace(/\n{3,}/g, "\n\n")
      .replace(/^\s+|\s+$/g, "");

    return plainText;
  }
}
onMounted(async () => {
	// 开始视频加载动画
	startVideoLoadingAnimation();

	await init();
	// console.log(info.breadcrumb)
	// info.setBreadcrumb([{id: uid, title: main.value.title}])
});
</script>

<template>
	<NSpin :show="show">
		<template #description> 正在生成PDF，请稍候...</template>
		<div class="chatbg h-full w-full bg-[#F7F9FF]">
			<header class="headers relative pr-[30px] items-center">
				<div class="left">
					<div
						class="gohome cursor-pointer"
						@click="jumpPage('/videoStudyHelper')"
					>
						<img src="@/assets/workShopPage/leftarrow.png"/>
					</div>
					{{ ToolsStore.ToolInfo.name }}
				</div>
				<n-breadcrumb>
					<n-breadcrumb-item
						v-for="(item, index) in breadcrumb"
						:key="index"
						@click="breadcrumbTap(item, index)"
					>
						{{ item.title }}
					</n-breadcrumb-item>
				</n-breadcrumb>
			</header>
			<div class="flex justify-between px-4 mt-4">
				<div class="h-full documentbg pt-4">
					<div class="flex justify-between items-center ml-[24px]">
						<NEllipsis
							style="max-width: 380px; font-size: 20px; font-weight: 500"
						>
							{{ main?.title }}
						</NEllipsis>
						<!-- 演示环境 暂时调整（隐藏智能标记、考点难点重点） -->
						<div class="flex items-center">
							<div class="flex items-center mr-[18px]">
								<!-- <n-dropdown
									:options="options"
									key-field="id"
									label-field="title"
									trigger="hover"
									@select="handleSelect"
								>
									<n-badge :value="options?.length">
										<div
											class="w-[106px] h-[36px] bg-[#125eff0d] rounded-[10px] flex items-center justify-center cursor-pointer"
										>
											<img
												alt=""
												class="w-4 h-4"
												src="@/assets/toolboxPage/bj.png"
											/>
											<p class="text-[14px] text-[#125EFF] ml-2">标记</p>
											<div class="w-[1px] h-[17px] bg-[#E3E3E3] mx-2"></div>
											<img
												alt=""
												class="w-4 h-4"
												src="@/assets/toolboxPage/bot.png"
											/>
										</div>
									</n-badge>
								</n-dropdown> -->
							</div>
						</div>
					</div>

					<!-- 视频播放器组件 -->
					<div class="relative">
						<!-- 视频加载动画 -->
						<div v-show="isVideoLoading" class="loading-container">
							<div class="progress-wrapper">
								<div class="progress-icon">
									<svg class="spinner" viewBox="0 0 50 50">
										<circle
											class="path"
											cx="25"
											cy="25"
											fill="none"
											r="20"
											stroke-width="5"
										></circle>
									</svg>
								</div>
								<div class="progress-text">
									{{ videoLoadingStageText }}
									{{ Math.round(videoLoadingProgress) }}%
								</div>
								<div class="progress-bar-container">
									<div
										:style="{ width: videoLoadingProgress + '%' }"
										class="progress-bar"
									></div>
								</div>
							</div>
						</div>

						<VideoPlayer
							ref="videoPlayerRef"
							:courseName="main?.title"
							:knowledgePoints="knowledgePoints"
							:timeToEndThreshold="timeToEndThreshold"
							:videoSrc="main?.url"
							@ended="onVideoEnded"
							@loadeddata="onVideoLoaded"
							@play="onVideoPlay"
							@timeToEnd="onVideoTimeToEnd"
						/>
					</div>
				</div>

				<div class="w-[35%] h-[87vh]">
					<n-tabs
						:value="currentTab"
						animated
						type="segment"
						@update:value="tabsTap"
					>
						<n-tab-pane name="0" tab="导学">
							<div class="flex items-center mt-[14px]">
								<img
									alt=""
									class="w-[18px] h-[12px]"
									src="@/assets/toolboxPage/tit-minicon.png"
								/>
								<p class="ml-[6px] text-[#3C3D48] font-medium">摘要总结</p>
							</div>
							<div
								class="bg-[#F1F4FE] p-[18px] w-full h-[156px] overflow-y-scroll rounded-[8px] mt-[5px]"
							>
										{{ markdownToPlainText(main?.summary) }}
							</div>
							<div class="flex items-center mt-[50px] mb-[5px]">
								<img
									alt=""
									class="w-[18px] h-[12px]"
									src="@/assets/toolboxPage/tit-minicon.png"
								/>
								<p class="ml-[6px] text-[#3C3D48] font-medium">核心知识</p>
							</div>

							<!-- 白色背景容器 -->
							<div class="bg-white rounded-[12px] p-[16px]">
								<div class="contentList overflow-y-scroll">
									<div
										v-for="(item, index) in contentList"
										:key="index"
										class="knowledge-item relative"
									>
										<!-- 左侧时间轴虚线 -->
										<div
											v-if="index !== contentList.length - 1"
											class="absolute left-[9px] top-[43px] w-[2px] h-[calc(100%-0px)] timeline-line"
										></div>

										<!-- 时间轴圆点 -->
										<div
											:class="
                        hoveredItems[index] ? 'bg-[#125EFF]' : 'bg-[#585A72]'
                      "
											class="absolute left-[5px] top-[28px] w-[10px] h-[10px] rounded-full shadow-lg border-2 border-white timeline-dot transition-colors duration-300"
										></div>

										<!-- 知识点内容区域 -->
										<div
											class="ml-[28px] cursor-pointer knowledge-content"
											@click="positionTap(item)"
											@mouseenter="() => setHoverState(index, true)"
											@mouseleave="() => setHoverState(index, false)"
										>
											<!-- 知识点标题栏 -->
											<div
												class="flex items-start justify-between py-[12px] hover-container"
											>
												<div class="flex-1">
													<div class="flex items-center justify-between">
														<p
															class="text-[14px] font-semibold text-[#3C3D48] pl-4"
														>
															{{ item.content }}
														</p>
														<div class="flex items-center relative">
															<!-- 时间显示 - 与按钮在同一位置 -->
															<span
																class="text-[12px] text-[#666] time-display absolute right-0 top-1/2 transform -translate-y-1/2"
															>
                                {{ item.position }}
                              </span>

															<!-- 右侧小图标 - 保持原有功能，悬停时显示 -->
															<n-popover
																placement="left"
																trigger="click"
																@update:show="(flag) => popoverShow(flag, item)"
																@click.stop
															>
																<template #trigger>
																	<img
																		alt=""
																		class="w-4 h-4 mr-10 invisible center-r"
																		src="@/assets/toolboxPage/center-r.png"
																		@click.stop
																	/>
																</template>
																<div
																	v-if="item.children?.length"
																	class="flex flex-col"
																>
																	<n-button
																		v-for="(i, dex) in item.children"
																		:key="dex"
																		class="children"
																		text
																		@click="goTap(i)"
																	>
																		<template #icon>
																			<img
																				:src="
                                          i.textbookType == 0
                                            ? jc
                                            : i.textbookType == 1
                                            ? kj
                                            : sp
                                        "
																				alt=""
																				class="children-img"
																			/>
																			<img
																				:src="
                                          i.textbookType == 0
                                            ? jca
                                            : i.textbookType == 1
                                            ? kja
                                            : spa
                                        "
																				alt=""
																				class="children-imga"
																			/>
																		</template>
																		{{ i.title }}
																	</n-button>
																</div>
																<div v-else class="">
																	<n-spin
																		v-if="!item.childrenFlag"
																		size="medium"
																	/>
																	<n-empty v-else description="暂无数据">
																	</n-empty>
																</div>
															</n-popover>

															<!-- 展开/收起图标 - 悬停时显示 -->
															<svg
																:class="{ 'rotate-180': expandedItems[index] }"
																class="w-4 h-4 transform transition-transform duration-200 invisible expand-icon absolute right-3"
																fill="none"
																stroke="currentColor"
																viewBox="0 0 24 24"
																@click.stop="toggleExpand(index)"
															>
																<path
																	d="M19 9l-7 7-7-7"
																	stroke-linecap="round"
																	stroke-linejoin="round"
																	stroke-width="2"
																/>
															</svg>
														</div>
													</div>

													<!-- 展开的详细内容 - 在原有块元素内撑开 -->
													<div v-if="expandedItems[index]" class="mt-2 pt-2">
														<p
															class="text-[14px] text-[#666] leading-relaxed pl-5"
														>
															{{ item.description }}
														</p>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</n-tab-pane>
						<n-tab-pane name="1" tab="问答">
							<questions
								ref="questionsRef"
								:dialogue="dialogue"
								:isVideoCompleted="isVideoCompleted"
								:main="main"
								:questionState="questionState"
								:show="false"
								:submitFlag="true"
							></questions>
						</n-tab-pane>
					</n-tabs>
				</div>
			</div>
		</div>
	</NSpin>
</template>

<style lang="less" scoped>
.contentList {
	height: calc(87vh - 340px);

	&::-webkit-scrollbar {
		display: none;
	}
}

:deep(.n-breadcrumb .n-breadcrumb-item:last-child .n-breadcrumb-item__link) {
	color: #125eff;
}

.children {
	justify-content: left;
}

.children:hover .children-img {
	display: none;
}

.children-imga {
	display: none;
}

.children:hover .children-imga {
	display: block;
}

.center-item {
	transition: 0.25s;

	p {
		transition: 0.25s;
	}

	&:hover {
		background: #fff;
	}
}

.center-item:hover p {
	color: #125eff;
}

.center-item:hover .center-r {
	visibility: visible;
	opacity: 1;
}

.center-r {
	transition: 0.25s;
	opacity: 0;
}

/* 新增知识点样式 */
.knowledge-item {
	margin-bottom: 16px;

	.knowledge-content {
		transition: all 0.25s ease;
		border-radius: 8px;

		&:hover {
			background: rgba(18, 94, 255, 0.05);

			p {
				color: #125eff;
			}
		}
	}

	.hover-container {
		&:hover {
			.center-r,
			.expand-icon {
				visibility: visible !important;
				opacity: 1;
			}

			.time-display {
				opacity: 0;
				visibility: hidden;
			}
		}
	}
}

/* 时间轴虚线样式 */
.timeline-line {
	background-image: linear-gradient(to bottom, #b2cbff 50%, transparent 50%);
	background-size: 2px 8px;
	background-repeat: repeat-y;
}

/* 时间轴点的样式 */
.timeline-dot {
	transition: all 0.3s ease;
	position: relative;
}

.center-r,
.expand-icon {
	transition: all 0.25s ease;
	opacity: 0;
	cursor: pointer;
	z-index: 10;

	&:hover {
		color: #125eff;
		transform: scale(1.1);
	}
}

.time-display {
	transition: all 0.25s ease;
	z-index: 5;
	padding-right: 15px;
}

:deep(.n-tabs .n-tabs-rail) {
	background: #f1f4fe;
}

:deep(.n-tabs-tab--active) {
	.n-tabs-tab__label {
		color: #125eff;
		font-weight: bold;
	}
}

.chatbg {
	padding-top: 30px;
	// height: 100vh;
}

.leftbox {
	width: 50%;
}

.rightbox {
	width: 50%;
}

.headers {
	width: 100%;
	height: 40px;
	display: flex;
	justify-content: space-between;
	// margin-bottom: 90px;
	.left {
		color: #323233;
		font-size: 20px;
		font-weight: 500;
		line-height: 0;
		letter-spacing: 0;
		line-height: 40px;
		display: flex;
		align-items: center;
		margin-left: 24px;

		.gohome {
			width: 40px;
			height: 40px;
			background: #fafbff;
			border: 1px solid #e9ecf3;
			border-radius: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 11px;

			img {
				width: 20px;
				height: 20px;
			}
		}

		span {
			color: #909399;
			margin-left: 8px;
		}
	}
}

.documentbg {
	// width: 796px;
	width: 63.5%;
	height: 87vh;
	background: #ffffff;
	box-shadow: 0 0 10px 0 #00000021;
	border-radius: 4px;
	//margin-left: 24px;
	//margin-right: 24px;
	//margin-top: 24px;
	//margin-bottom: 24px;
	overflow-y: auto;
}

.autoheight {
	width: 50%;
	height: 85vh;
	padding-left: 24px;
	padding-right: 32px;
	/* 允许内容溢出时滚动 */
	overflow-y: auto;
	/* 隐藏 WebKit 浏览器的滚动条 */
	scrollbar-width: none;
	/* 隐藏 Firefox 的滚动条 */
	-ms-overflow-style: none;
}

.autoheight::-webkit-scrollbar {
	display: none;
}

/* 优化后的平滑动画效果 */
.slide-fade-enter-active {
	transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-leave-active {
	transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
	transform: translateX(-20px);
	opacity: 0;
}

/* 视频加载动画样式 */
.loading-container {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	background-color: rgba(255, 255, 255, 0.95);
	z-index: 20;
	backdrop-filter: blur(5px);
	border-radius: 12px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.progress-wrapper {
	width: 70%;
	max-width: 400px;
	text-align: center;
	padding: 30px;
	background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
	border-radius: 16px;
	box-shadow: 0 8px 24px rgba(2, 100, 250, 0.15);
}

.progress-icon {
	display: flex;
	justify-content: center;
	margin-bottom: 20px;
}

.spinner {
	animation: rotate 2s linear infinite;
	width: 50px;
	height: 50px;
}

.path {
	stroke: #0264fa;
	stroke-linecap: round;
	animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
	100% {
		transform: rotate(360deg);
	}
}

@keyframes dash {
	0% {
		stroke-dasharray: 1, 150;
		stroke-dashoffset: 0;
	}
	50% {
		stroke-dasharray: 90, 150;
		stroke-dashoffset: -35;
	}
	100% {
		stroke-dasharray: 90, 150;
		stroke-dashoffset: -124;
	}
}

.progress-text {
	color: #0264fa;
	margin-bottom: 20px;
	font-size: 16px;
	font-weight: 500;
	font-family: "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
	text-shadow: 0 1px 2px rgba(2, 100, 250, 0.1);
}

.progress-bar-container {
	width: 100%;
	height: 12px;
	background-color: #e6f0ff;
	border-radius: 10px;
	overflow: hidden;
	box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
	height: 100%;
	background: linear-gradient(90deg, #0264fa 0%, #3a86ff 100%);
	border-radius: 10px;
	transition: width 0.4s cubic-bezier(0.22, 0.61, 0.36, 1);
	box-shadow: 0 2px 8px rgba(2, 100, 250, 0.3);
}
</style>
