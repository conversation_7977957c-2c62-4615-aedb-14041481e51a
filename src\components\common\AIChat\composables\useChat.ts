import { ref, reactive } from 'vue'
import type {
  Message,
  Attachment,
  MessageFeedback,
  WorkflowNode,
  StreamResponse,
  ChatAPIParams,
  ConversationRequest,
  AIChatConfig
} from '../types'
import { fetchChatAPIProcess } from '@/api/tankChat'

export function useChat(config?: AIChatConfig) {
  // 状态管理
  const messages = ref<Message[]>([])
  const isLoading = ref(false)
  const isGenerating = ref(false)
  const loadingText = ref('')
  const workflowNodes = ref<WorkflowNode[]>([])
  
  // 当前流式响应的控制器
  let currentController: AbortController | null = null

  // 添加消息
  const addMessage = (message: Omit<Message, 'id' | 'timestamp'>) => {
    const newMessage: Message = {
      ...message,
      id: generateId(),
      timestamp: Date.now()
    }
    messages.value.push(newMessage)
    return newMessage
  }

  // 更新消息
  const updateMessage = (id: string, updates: Partial<Message>) => {
    const index = messages.value.findIndex(msg => msg.id === id)
    if (index !== -1) {
      messages.value[index] = { ...messages.value[index], ...updates }
    }
  }

  // 删除消息
  const removeMessage = (id: string) => {
    const index = messages.value.findIndex(msg => msg.id === id)
    if (index !== -1) {
      messages.value.splice(index, 1)
    }
  }

  // 清空对话
  const clearMessages = () => {
    messages.value = []
    workflowNodes.value = []
  }

  // 发送消息
  const sendMessage = async (
    content: string, 
    attachments?: Attachment[],
    onStream?: (response: StreamResponse) => void
  ) => {
    // 添加用户消息
    const userMessage = addMessage({
      content,
      role: 'user',
      attachments
    })

    // 添加AI消息占位符
    const aiMessage = addMessage({
      content: '',
      role: 'assistant',
      loading: true
    })

    isGenerating.value = true
    currentController = new AbortController()

    try {
      // 使用真实的API调用
      if (config?.compatibility?.useOriginalAPI) {
        // 调用真实的API
        await sendMessageWithOriginalAPI({
          content,
          question: content,
          prompt: content,
          conversationId: config?.compatibility?.conversationId || `chat-${Date.now()}`,
          category: config?.compatibility?.category || '0',
          agentId: config?.compatibility?.agentId || '1950834085600997378',
          modelId: config?.compatibility?.modelId || 'gpt-3.5-turbo',
          modelTemp: config?.compatibility?.modelTemp || 0.7,
          maxLength: config?.compatibility?.maxLength || 2000,
          promptTemplate: config?.compatibility?.promptTemplate || '',
          multiTurnFlag: config?.compatibility?.multiTurnFlag !== false,
          attachments,
          signal: currentController?.signal
        }, (streamResponse) => {
          // 实时更新AI消息内容
          updateMessage(aiMessage.id, {
            content: streamResponse.content,
            thinking: streamResponse.thinking,
            loading: !streamResponse.finished,
            text: streamResponse.text,
            answerList: streamResponse.answerList,
            endstatus: streamResponse.endstatus,
            conversationId: streamResponse.conversationId
          })

          // 如果有流式回调，也调用它
          if (onStream) {
            onStream(streamResponse)
          }
        })
      } else {
        // 降级到模拟响应
        await simulateStreamResponse(aiMessage.id, content, onStream)
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        updateMessage(aiMessage.id, {
          content: '生成已停止',
          loading: false,
          error: true
        })
      } else {
        updateMessage(aiMessage.id, {
          content: '抱歉，发生了错误，请重试。',
          loading: false,
          error: true
        })
      }
    } finally {
      isGenerating.value = false
      currentController = null
    }

    return aiMessage
  }

  // 模拟流式响应
  const simulateStreamResponse = async (
    messageId: string,
    userInput: string,
    onStream?: (response: StreamResponse) => void
  ) => {
    const responses = [
      "我理解您的问题。",
      "让我来为您详细解答。",
      "根据我的分析，",
      "这个问题涉及到几个方面：",
      "\n\n1. 首先，我们需要考虑...",
      "\n2. 其次，还要注意...",
      "\n3. 最后，建议您...",
      "\n\n希望这个回答对您有帮助！如果还有其他问题，请随时告诉我。"
    ]

    let fullContent = ''
    let thinking = ''

    // 模拟思考过程
    if (Math.random() > 0.5) {
      thinking = '正在分析您的问题...\n思考最佳的回答方式...'
      updateMessage(messageId, { thinking })
      await delay(1000)
    }

    // 逐步生成回答
    for (let i = 0; i < responses.length; i++) {
      if (currentController?.signal.aborted) {
        throw new AbortError()
      }

      fullContent += responses[i]
      
      updateMessage(messageId, {
        content: fullContent,
        thinking: i === 0 ? '' : thinking, // 开始回答后清除思考内容
        loading: i < responses.length - 1
      })

      // 调用流式回调
      if (onStream) {
        onStream({
          content: fullContent,
          thinking: i === 0 ? '' : thinking,
          finished: i === responses.length - 1
        })
      }

      await delay(300 + Math.random() * 200)
    }

    // 完成生成
    updateMessage(messageId, {
      content: fullContent,
      loading: false,
      thinking: ''
    })
  }

  // 停止生成
  const stopGeneration = () => {
    if (currentController) {
      currentController.abort()
    }
  }

  // 重新生成
  const regenerateMessage = async (messageId: string) => {
    const messageIndex = messages.value.findIndex(msg => msg.id === messageId)
    if (messageIndex === -1) return

    const message = messages.value[messageIndex]
    if (message.role !== 'assistant') return

    // 找到对应的用户消息
    const userMessage = messages.value[messageIndex - 1]
    if (!userMessage || userMessage.role !== 'user') return

    // 清空原有内容并重置状态
    updateMessage(messageId, {
      content: '',
      loading: true,
      error: false,
      thinking: '',
      stages: undefined,
      currentStage: undefined,
      isThinkingComplete: false,
      isGenerating: true
    })

    // 设置生成状态
    isGenerating.value = true
    loadingText.value = '正在重新生成回复...'

    try {
      // 使用真实的API重新生成
      if (config?.compatibility?.useOriginalAPI) {
        // 调用真实的API
        await sendMessageWithOriginalAPI({
          content: userMessage.content,
          question: userMessage.content,
          prompt: userMessage.content,
          conversationId: config?.compatibility?.conversationId || `chat-${Date.now()}`,
          category: config?.compatibility?.category || '0',
          agentId: config?.compatibility?.agentId || '1966317284699451393',
          modelId: config?.compatibility?.modelId || 'gpt-3.5-turbo',
          modelTemp: config?.compatibility?.modelTemp || 0.7,
          maxLength: config?.compatibility?.maxLength || 2000,
          promptTemplate: config?.compatibility?.promptTemplate || '',
          multiTurnFlag: config?.compatibility?.multiTurnFlag ?? false
        }, (streamResponse) => {
          // 实时更新AI消息内容
          updateMessage(messageId, {
            content: streamResponse.content,
            thinking: streamResponse.thinking,
            loading: !streamResponse.finished,
            text: streamResponse.text,
            answerList: streamResponse.answerList,
            endstatus: streamResponse.endstatus,
            conversationId: streamResponse.conversationId
          })
        })
      } else {
        // 降级到模拟响应
        await simulateStreamResponse(messageId, userMessage.content)
      }

    } catch (error) {
      console.error('重新生成失败:', error)
      updateMessage(messageId, {
        content: '重新生成失败，请稍后再试。',
        loading: false,
        error: true,
        isGenerating: false
      })
    } finally {
      isGenerating.value = false
      loadingText.value = ''
    }
  }

  // 编辑消息
  const editMessage = async (messageId: string, newContent: string) => {
    const messageIndex = messages.value.findIndex(msg => msg.id === messageId)
    if (messageIndex === -1) return

    // 更新用户消息
    updateMessage(messageId, { content: newContent })

    // 删除后续的AI回复
    const messagesToRemove = messages.value.slice(messageIndex + 1)
    messagesToRemove.forEach(msg => removeMessage(msg.id))

    // 重新发送
    await sendMessage(newContent)
  }

  // 添加反馈
  const addFeedback = async (messageId: string, feedback: MessageFeedback) => {
    updateMessage(messageId, { feedback })

    // 调用真实的反馈API
    try {
      if (config?.compatibility?.useOriginalAPI) {
        // 使用API适配器提交反馈
        const { createAPIAdapter } = await import('../adapters/apiAdapter')
        const apiAdapter = createAPIAdapter(config)
        await apiAdapter.submitFeedback(messageId, feedback)
        console.log('反馈提交成功:', { messageId, feedback })
      } else {
        // 使用新的反馈接口
        console.log('提交反馈:', { messageId, feedback })
      }
    } catch (error) {
      console.error('提交反馈失败:', error)
      // 可以在这里添加错误提示
    }
  }

  // 工作流相关
  const updateWorkflowNodes = (nodes: WorkflowNode[]) => {
    workflowNodes.value = nodes
  }

  const updateWorkflowNode = (nodeId: string, updates: Partial<WorkflowNode>) => {
    const index = workflowNodes.value.findIndex(node => node.id === nodeId)
    if (index !== -1) {
      workflowNodes.value[index] = { ...workflowNodes.value[index], ...updates }
    }
  }

  // 导出对话
  const exportChat = () => {
    const chatData = {
      messages: messages.value,
      exportTime: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(chatData, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `chat-export-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 兼容原有chat组件的API调用
  const sendMessageWithOriginalAPI = async (
    params: ChatAPIParams,
    onStream?: (response: StreamResponse) => void
  ) => {
    // 这个函数专门处理原有API，不应该递归调用sendMessage
    if (!config?.compatibility?.useOriginalAPI) {
      throw new Error('sendMessageWithOriginalAPI should only be called when useOriginalAPI is true')
    }

    // 使用原有的API
    try {
      isGenerating.value = true
      loadingText.value = '正在生成回复...'

      // 注意：不在这里添加消息，因为调用方（sendMessage）已经添加了
      // 获取最后一条AI消息（应该是调用方刚添加的占位符）
      const aiMessage = messages.value[messages.value.length - 1]

      // 使用API适配器调用真实的SSE流式API
      const { createAPIAdapter } = await import('../adapters/apiAdapter')
      const apiAdapter = createAPIAdapter(config)

      const apiParams = {
        question: params.question || params.content || params.prompt,
        conversationId: params.conversationId || config?.compatibility?.conversationId || `chat-${Date.now()}`,
        category: params.category || config?.compatibility?.category || 'chat',
        agentId: params.agentId || config?.compatibility?.agentId || '1950834085600997378',
        modelId: params.modelId || config?.compatibility?.modelId || 'gpt-3.5-turbo',
        modelTemp: params.modelTemp || config?.compatibility?.modelTemp || 0.7,
        maxLength: params.maxLength || config?.compatibility?.maxLength || 2000,
        promptTemplate: params.promptTemplate || config?.compatibility?.promptTemplate || '',
        multiTurnFlag: params.multiTurnFlag ?? config?.compatibility?.multiTurnFlag ?? true,
        signal: params.signal,
        attachments: params.attachments
      }

      // 调用API适配器，支持真实的SSE流式响应
      const response = await apiAdapter.sendMessage(apiParams, (streamResponse) => {
        // 实时更新AI消息内容
        updateMessage(aiMessage.id, {
          content: streamResponse.content,
          thinking: streamResponse.thinking,
          loading: !streamResponse.finished,
          text: streamResponse.text,
          answerList: streamResponse.answerList,
          endstatus: streamResponse.endstatus,
          conversationId: streamResponse.conversationId
        })

        // 如果有流式回调，也调用它
        if (onStream) {
          onStream(streamResponse)
        }
      })

      // 注意：不需要最终更新，因为流式回调已经处理了所有更新
      // 包括finished状态的最终更新

    } catch (error) {
      console.error('Original API call failed:', error)
      throw error
    } finally {
      isGenerating.value = false
      loadingText.value = ''
    }
  }

  // 兼容原有消息格式的转换
  const convertLegacyMessage = (legacyMsg: any): Message => {
    return {
      id: legacyMsg.id || generateId(),
      content: legacyMsg.text || legacyMsg.content || '',
      role: legacyMsg.inversion ? 'user' : 'assistant',
      timestamp: legacyMsg.dateTime ? new Date(legacyMsg.dateTime).getTime() : Date.now(),
      loading: legacyMsg.loading,
      error: legacyMsg.error,
      // 保留原有字段
      dateTime: legacyMsg.dateTime,
      text: legacyMsg.text,
      inversion: legacyMsg.inversion,
      conversationOptions: legacyMsg.conversationOptions,
      requestOptions: legacyMsg.requestOptions,
      answerList: legacyMsg.answerList,
      endstatus: legacyMsg.endstatus,
      conversationId: legacyMsg.conversationOptions?.conversationId
    }
  }

  // 批量导入原有消息
  const importLegacyMessages = (legacyMessages: any[]) => {
    const convertedMessages = legacyMessages.map(convertLegacyMessage)
    messages.value = convertedMessages
  }

  return {
    // 状态
    messages,
    isLoading,
    isGenerating,
    loadingText,
    workflowNodes,

    // 原有方法
    addMessage,
    updateMessage,
    removeMessage,
    clearMessages,
    sendMessage,
    stopGeneration,
    regenerateMessage,
    editMessage,
    addFeedback,
    updateWorkflowNodes,
    updateWorkflowNode,
    exportChat,

    // 新增兼容方法
    sendMessageWithOriginalAPI,
    convertLegacyMessage,
    importLegacyMessages
  }
}

// 工具函数
function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

class AbortError extends Error {
  constructor() {
    super('Operation was aborted')
    this.name = 'AbortError'
  }
}
