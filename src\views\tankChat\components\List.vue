<script lang='ts' setup>
import {computed, onMounted, ref, watch} from "vue"
import {
	NInput,
	NScrollbar,
	NPopover,
	NEllipsis,
	NTooltip,
	useMessage,
	useDialog,
} from "naive-ui"
import {useRoute, useRouter} from "vue-router"
import {SvgIcon} from "@/components/common"
import {useAppStore, useChatStore} from "@/store"
import {useBasicLayout} from "@/hooks/useBasicLayout"
import {
	getHistory,
	getHistorylist,
	delHistory,
	updateHistory,
} from "@/api/index"
import {infoStore} from "@/store/modules/info"
import delIcon from "@/assets/chat/delIcon.png"
import noTopUpIcon from "@/assets/chat/noTopUpIcon.png"
import topUpIcon from "@/assets/chat/topUpIcon.png"
import renameIcon from "@/assets/chat/renameIcon.png"
import rowtopupicon from "@/assets/chat/rowtopupicon.png"
import allhistoryicon from "@/assets/chat/allhistoryicon.png"
import icon1 from '@/assets/applicationPage/icon1.png'

const router = useRouter()
const chatStore = useChatStore()
const appStore = useAppStore()
const route = useRoute()
const message = useMessage()
const dialog = useDialog()
const info = infoStore()

// 调整为从store获取
var dataSources = ref<any[]>([])

// 获取折叠状态
const collapsed = computed(() => appStore.siderCollapsed)

const emit = defineEmits(["onSelect"])
var isnewAddHistory = computed(() => chatStore.newAddHistory)

watch(isnewAddHistory, () => {
	if (isnewAddHistory.value) {
		setTimeout(() => {
			getHistoryfun()
		}, 100)
	}
})

watch(
	() => route.fullPath,
	(newVal, oldVal) => {
		if (route.name != "tankChat") {
			dataSources.value.forEach((items: any) => {
				items.ischeck = false
				items.showPopover = false
				items.showActions = false
			})
		} else {
			dataSources.value.forEach((items: any) => {
				if (items.id == chatStore.active) {
					items.ischeck = true
				}
				items.showPopover = false
				items.showActions = false
			})
		}
	},
	{immediate: true}
)

import {useToolsStore} from "@/store"

const ToolsStore = useToolsStore()

async function handleSelect(item: any) {
	dataSources.value.forEach((items: any) => {
		items.ischeck = false
	})
	item.ischeck = true

	const toolitem = {
		...item,
		name: item.agentName,
		title: item.openingWords,
		des: item.description,
		agentId: item.agentId,
		openingQuestionArr: [],
		icon: icon1,
		modelSessionId: item.modelId,
		modelTemp: item.modelTemp,
		maxLength: item.maxLength,
		promptTemplate: item.promptTemplate,
	}
	toolitem.openingQuestionArr = toolitem.openingQuestionArr.concat(JSON.parse(item.preQuestions || '[]'))
	ToolsStore.updateToolInfo(toolitem)
	chatStore.activeHistory(item.id)

	router.push({name: 'tankChat', query: {uuid: item.id}})
}

const {isMobile} = useBasicLayout()

function getHistoryfun() {
	// 获取当前助手的agentId
	// 优先从chatStore.target中获取（开放URL进入时），否则从ToolsStore中获取
	let currentAgentId: string | undefined

	if (chatStore.target?.targetFlag && chatStore.target?.id) {
		// 通过开放URL进入，使用URL参数中的id作为agentId
		currentAgentId = chatStore.target.id
		console.log('通过开放URL进入，使用URL参数中的agentId:', currentAgentId)
	} else {
		// 正常进入，使用ToolsStore中的agentId
		currentAgentId = ToolsStore.ToolInfo?.agentId
		console.log('正常进入，使用ToolsStore中的agentId:', currentAgentId)
	}

	if (!currentAgentId) {
		console.warn('没有找到当前助手的agentId，无法获取历史对话')
		dataSources.value = []
		return
	}

	getHistorylist({agentId: currentAgentId}).then((res: any) => {
		console.log('getHistorylist返回数据:', res)

		// 合并所有分组的数据
		const allHistoryData = [
			...(res.data.topUpList || []),
			...(res.data.todayList || []),
			...(res.data.lastSevenDayList || []),
			...(res.data.earlierDayList || [])
		]

		dataSources.value = allHistoryData
		dataSources.value.forEach((item: any) => {
			item.ischeck = false
			item.showPopover = false
			item.showActions = false
			if (item.id == chatStore.active) {
				item.ischeck = true
			}
		})
	}).catch((error) => {
		console.error('获取历史对话失败:', error)
		dataSources.value = []
	})
}

onMounted(() => {
	getHistoryfun()
})

// 删除历史记录
function handleDelete(item: any, event: Event) {
	event.stopPropagation()
	dialog.warning({
		title: '删除对话',
		content: '确定删除这个对话吗？',
		positiveText: '确定',
		negativeText: '取消',
		onPositiveClick: () => {
			delHistory({id: item.id}).then(() => {
				message.success('删除成功')
				getHistoryfun()
				if (chatStore.active === item.id) {
					chatStore.activeHistory('')
					router.push({name: 'tankChat'})
				}
			})
		},
	})
}

// 重命名
const editingItem = ref<any>(null)
const editingTitle = ref('')

function handleRename(item: any, event: Event) {
	event.stopPropagation()
	editingItem.value = item
	editingTitle.value = item.title
}

function saveRename() {
	if (!editingTitle.value.trim()) {
		message.error('标题不能为空')
		return
	}

	updateHistory({
		id: editingItem.value.id,
		title: editingTitle.value
	}).then(() => {
		message.success('重命名成功')
		editingItem.value.title = editingTitle.value
		editingItem.value = null
		editingTitle.value = ''
	})
}

function cancelRename() {
	editingItem.value = null
	editingTitle.value = ''
}
</script>

<template>
	<NScrollbar class="px-4">
		<div class="flex flex-col gap-2 text-sm">
			<template v-if="!dataSources.length">
				<div class="flex flex-col items-center justify-center py-4 text-center text-neutral-300">
					<SvgIcon class="mb-2 text-3xl" icon="ri:inbox-line"/>
					<span>暂无数据</span>
				</div>
			</template>
			<template v-else>
				<div
					v-for="(item, index) of dataSources"
					:key="index"
					:class="{
            'border-gray-400 bg-gray-100': item.ischeck,
          }"
					class="relative flex items-center h-11 px-4 break-all border-solid border-2 border-gray-200 rounded-xl font-medium cursor-pointer hover:bg-neutral-100 group text-gray-700"
					@click="handleSelect(item)"
				>
					<div class="flex-1 overflow-hidden">
						<NEllipsis v-if="editingItem?.id !== item.id" :tooltip="false">
							{{ item.title || '新对话' }}
						</NEllipsis>
						<NInput
							v-else
							v-model:value="editingTitle"
							size="small"
							@blur="saveRename"
							@keydown.enter="saveRename"
							@keydown.esc="cancelRename"
						/>
					</div>
					<div class="flex items-center gap-1">
						<button
							class="flex items-center justify-center w-6 h-6 text-neutral-400 hover:text-neutral-600"
							@click="handleRename(item, $event)"
						>
							<SvgIcon class="text-sm" icon="ri:edit-line"/>
						</button>
						<button
							class="flex items-center justify-center w-6 h-6 text-neutral-400 hover:text-red-500"
							@click="handleDelete(item, $event)"
						>
							<SvgIcon class="text-sm" icon="ri:delete-bin-line"/>
						</button>
					</div>
				</div>
			</template>
		</div>
	</NScrollbar>
</template>

<style scoped>
.group:hover .absolute {
	opacity: 1;
}

.absolute {
	opacity: 0;
	transition: opacity 0.2s;
}

.group:hover .absolute,
.group.border-\[\#4b9e5f\] .absolute {
	opacity: 1;
}
</style>
