<script lang='ts' setup>
import {computed, watch} from 'vue'
import {NLayout, NLayoutContent} from 'naive-ui'
import {useRoute} from 'vue-router'
import Sider from './sider/index.vue'
import Permission from './Permission.vue'
import {useBasicLayout} from '@/hooks/useBasicLayout'
import {useAuthStore, useChatStore} from '@/store'

const route = useRoute()
const chatStore = useChatStore()
const authStore = useAuthStore()
watch(() => route.fullPath, (newVal, oldVal) => {
	if (route.name != 'tankChat') {
		chatStore.activeHistory('')
	}
}, {immediate: true})
const {isMobile} = useBasicLayout()


const needPermission = computed(() => !!authStore.session?.auth && !authStore.token)

const target = computed(() => chatStore.target)

const getMobileClass = computed(() => {
	if (isMobile.value)
		return ['rounded-none', 'shadow-none']
	return ['border', 'rounded-md', 'shadow-md', 'dark:border-neutral-800']
})

const getContainerClass = computed(() => {
	return [
		'h-full',
		{},
	]
})
</script>

<template>
	<div :class="[isMobile ? 'p-0' : 'p-0']" class="h-full dark:bg-[#24272e]   transition-all">
		<div :class="getMobileClass" class="h-full overflow-hidden">
			<NLayout :class="getContainerClass" class="z-40 transition" has-sider>
				<Sider v-if="!target.targetFlag"/>
				<NLayoutContent class="h-full ">
					<RouterView v-slot="{ Component, route }">
						<component :is="Component" :key="route.fullPath"/>
					</RouterView>
				</NLayoutContent>
			</NLayout>
		</div>
		<Permission :visible="needPermission"/>
	</div>
</template>
<style lang="less" scoped>
/deep/ .n-layout {
	background-color: #F7F9FF;
	background-image: linear-gradient(180deg, #FFFFFF 0%, #F6F8FE 100%);
}
</style>
