<template>
  <div>
          <n-form-item path="config.modelConfig.model" class="setrowbottom">
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span> 模型选择
                  </div>
                </template>
                <n-select
                  v-model:value="formData.config.modelConfig.model"
                  :options="modelOptions"
                  placeholder="请选择模型"
                  filterable
                  @update:value="handleModelChange"
                />
                <img
                  @click="changemodelParameterShow(true)"
                  class="w-[20px] h-[20px] ml-[10px] cursor-pointer"
                  src="@/assets/agentOrchestration/yitupeizhi.png"
                />
              </n-form-item>
              <n-form-item
                label-placement="left"
                class="setHeight mt-[21px] mb-[13px]"
              >
                <template #label>
                  <div class="rowstit"><span class="rowicon"></span> 输入</div>
                </template>
              </n-form-item>
              <n-form-item label-placement="left" class="setHeight mb-[6px]">
                <template #label>
                  <div class="text-[#565756]">用户问题</div>
                </template>
                <div class="flex justify-end w-full text-[#C7C7C7]">文本</div>
              </n-form-item>
              <n-form-item path="config.inputValue" label-placement="left">
                <div class="flex items-center w-full h-full">
                  <div class="w-[162px] mr-[14px] h-full">
                    <n-select
                      v-model:value="formData.config.inputType"
                      :options="variableOptions"
                      @update:value="handleInputKeyTypeChange"
                    />
                  </div>
                  <div class="w-full h-full">
                    <n-input
                      v-if="formData.config.inputType == '1'"
                      v-model:value="formData.config.inputValue"
                      type="text"
                      placeholder="请输入"
                      maxlength="20"
                      show-count
                    >
                    </n-input>
                    <div class="h-full" v-else>
                      <AggregationSelector
                        v-model="formData.config.inputValue"
                        :options="aggregationOptions"
                        placeholder="请选择变量"
                        @change="handleAggregationChange"
                      />
                    </div>
                  </div>
                </div>
              </n-form-item>

              <n-form-item
                label-placement="left"
                class="setHeight mt-[24px] mb-[9px]"
              >
                <template #label>
                  <div class="rowstit">
                    <span class="rowicon"></span> 问题意图
                  </div>
                </template>
                <div class="flex justify-end w-full">
                  <img
                    @click="addcategoriesfun"
                    class="w-[16px] h-[16px] cursor-pointer"
                    src="@/assets/agentOrchestration/yituadd.png"
                  />
                </div>
              </n-form-item>

              <div
                class="knowledgelist h-[85px] py-[14px] px-[19px] flex items-center"
                v-for="(item, index) in formData.config.classes"
                :key="index"
              >
                <div class="w-[400px]">
                  <NInput
                    class="setinputbg"
                    @blur="changeEditStatus(index, false)"
                    autofocus
                    v-if="item.isEditing"
                    v-model:value="item.name"
                    placeholder="请输入"
                  ></NInput>
                  <p
                    v-show="!item.isEditing"
                    class="h-[22px] mb-[14px] text-[#000000] leading-normal flex items-center text-[16px]"
                  >
                    {{ item.name
                    }}<img
                      v-if="!item.disable"
                      @click="changeEditStatus(index, true)"
                      class="ml-[10px] w-[14px] h-[14px] cursor-pointer"
                      src="@/assets/agentOrchestration/editIcon.png"
                    />
                  </p>

                  <NInput
                    class="setinputbg"
                    v-if="!item.disable"
                    v-model:value="item.des"
                    placeholder="请输入关于此意图的描述"
                  ></NInput>
                  <p v-else class="text-[#ADB3BB]">{{ item.des }}</p>
                </div>
                <img
                  v-if="!item.disable"
                  @click="delcategoriesfun(index)"
                  class="w-[16px] h-[16px] cursor-pointer"
                  src="@/assets/agentOrchestration/delIcon2.png"
                />
              </div>
          
              <n-form-item label-placement="left" class="setHeight mt-[24px]">
                <template #label>
                  <div class="rowstit"><span class="rowicon"></span>输出</div>
                </template>
              </n-form-item>

              <n-form-item label-placement="left" class="setHeight mt-[6px]">
                <div class="w-full flex text-[#C7C7C7]">
                  <div class="w-[50%]">变量名称</div>
                  <div>数据类型</div>
                </div>
              </n-form-item>

              <n-form-item label-placement="left" class="setHeight mt-[14px]">
                <div class="w-full flex text-[#565756] items-center">
                  <div class="w-[50%]">匹配结果</div>
                  <div>文本</div>
                </div>
              </n-form-item>

  <!-- 模型配置弹窗 -->
  <NModal v-model:show="modelParameterShow" 
  	:bordered="false"
		preset="card"
		size="huge"
		style="width: 650px"
		title="模型参数">
      <NForm ref="modelParameterformRef" label-placement="left"
				label-width="auto"
				require-mark-placement="right-hanging" :model="modelParameterformData">

        	<NFormItem label="模型风格" label-placement="left" class="customcard">
					<NRadioGroup
			      v-model:value="modelParameterformData.style"
						name="radioGroup1"
						@update:value="updateRadio"
					>
						<NRadio
							v-for="song in modelParameterOptions"
							:key="song.model_style"
							:label="song.dictValue"
							:value="song.model_style"
						/>
					</NRadioGroup>
				</NFormItem>
				<NFormItem label="模型温度" label-placement="left" class="customcard">
					<n-slider v-model:value="modelParameterformData.temperature" :max="2" :min="0" :step="0.1" @update:value="modelTempUpdate"/>
					<NInputNumber
						v-model:value="modelParameterformData.temperature"
						:max="2"
						:min="0"
						:precision="1"
						:show-button="false"
						class="ml-10"
						@update:value="modelTempUpdate"
					/>
				</NFormItem>
				<NFormItem label="Top P" label-placement="left" class="customcard">
					<n-slider v-model:value="modelParameterformData.topP" :max="0.9" :min="0.1" :step="0.1" @update:value="topPUpdate"/>
					<NInputNumber
						v-model:value="modelParameterformData.topP"
						:max="0.9"
						:min="0.1"
						:precision="1"
						:show-button="false"
						class="ml-10"
						@update:value="topPUpdate"
					/>
				</NFormItem>
				<NFormItem label="最大输出长度" label-placement="left" class="customcard">
					<n-slider v-model:value="modelParameterformData.maxTokens" :max="32768" :min="1" :step="1"/>
					<NInputNumber
						v-model:value="modelParameterformData.maxTokens"
						:max="32768"
						:min="1"
						:precision="0"
						:show-button="false"
						class="ml-10"
						@update:value="(v:number | null)=>{if (v === null){modelParameterformData.maxTokens = 1}}"
					/>
				</NFormItem>

    
      </NForm>

      	<template #footer>
			<div class="flex flex-row-reverse w-full">
				<NButton type="info" @click="changemodelParameterfun"> 保存</NButton>
				<NButton class="!mr-5" @click="changemodelParameterShow(false)"> 取消</NButton>
			</div>
		</template>
    
  </NModal>

  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NSwitch,
  NSlider,
  NButton,
  NInputNumber,
  NScrollbar,
  NDropdown,
  NDynamicInput,
  useMessage,
  useDialog,
  NPopover,
  NDataTable,
  NCard,
  NDivider,
  NRadio,
  NRadioGroup,
  NModal,
  NCheckbox,
  NCheckboxGroup,
} from "naive-ui";
import VariableSelector from '../VariableSelector.vue';
import { useOrchestrationStore } from "@/store";
import { agentParam, museModels,optimize } from "@/api/workShop";
import AggregationSelector from "../AggregationSelector.vue";
import { generateId } from "@/store/modules/orchestration";

const orchestrationStore = useOrchestrationStore();

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
    node: {
    type: Object,
    required: true,
  },
  variableOptions: {
    type: Array,
    required: true,
  },
  aggregationOptions: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits([
  'update:formData'
]);
const modelParameterOptions = ref<any[]>([]);
const agentParams = ref<any>(null);
const modelOptions = ref<any[]>([]);


// 输入类型选项
const inputKeyTypeOptions = [
  { label: '文本', value: 'text' },
  { label: '变量', value: 'variable' }
];

//模型配置弹窗是否展示
var modelParameterShow = ref(false);
const modelParameterformRef = ref();
const modelParameterformData = ref({
  style: "",
  temperature: "",
  topP: "",
  maxTokens: "",
});

// 添加分类
const addcategoriesfun = () => {
  const classes = [...props.formData.config.classes];
  classes.unshift({
      id: generateId() + "-output",
    name: "新增意图",
    des: "",
  });
  emit('update:formData', {
    ...props.formData,
    config: {
      ...props.formData.config,
      classes
    }
  });
};

// 删除分类
const delcategoriesfun = (index) => {
  const classes = [...props.formData.config.classes];
  if (classes.length > 1) {
    classes.splice(index, 1);
    emit('update:formData', {
      ...props.formData,
      config: {
        ...props.formData.config,
        classes
      }
    });
  }
};

// 切换编辑状态
const changeEditStatus = (index, status) => {
  const classes = [...props.formData.config.classes];
  classes[index].isEditing = status;
  emit('update:formData', {
    ...props.formData,
    config: {
      ...props.formData.config,
      classes
    }
  });
};

// 聚合节点选择处理
const handleAggregationChange = (value: string, variable: any, group: any) => {
  console.log("选择的聚合变量:", { value, variable, group });
  props.formData.config.inputKey = value;
};
// 处理模型变化
const handleModelChange = (value: string) => {
  let model = modelOptions.value.find((item: any) => {
    return item.value === value;
  });
props.formData.config.modelConfig.modelName=model?.label || '';
};

const changemodelParameterShow = (status: boolean) => {
  if (status) {
    modelParameterformData.value.temperature =
      props.formData.config.modelConfig.completionParams.temperature;
    modelParameterformData.value.maxTokens =
      props.formData.config.modelConfig.completionParams.maxTokens;
    modelParameterformData.value.topP =
      props.formData.config.modelConfig.completionParams.topP;
    modelParameterformData.value.style =
      props.formData.config.modelConfig.completionParams.style;
  }
  modelParameterShow.value = status;
};
const changemodelParameterfun = async () => {
  await modelParameterformRef.value?.validate();
  props.formData.config.modelConfig.completionParams.temperature =
    modelParameterformData.value.temperature;
  props.formData.config.modelConfig.completionParams.maxTokens =
    modelParameterformData.value.maxTokens;
  props.formData.config.modelConfig.completionParams.topP =
    modelParameterformData.value.topP;
  props.formData.config.modelConfig.completionParams.style =
    modelParameterformData.value.style;
  changemodelParameterShow(false);
};
function updatemodelParameterfun(value: string) {
  modelParameterformData.value.temperature = value;
}
function handleInputKeyTypeChange(value: string) {
  console.log(value);
  props.formData.config.inputValue = "";
  if (value == "1") {
    props.formData.config.inputKey = generateId();
  } else {
    props.formData.config.inputKey = "";
  }
}
// 初始化数据
const initdatafun = async () => {
  const models = await museModels();
  const res = await agentParam();
  modelParameterOptions.value = res.data.model_style;
  agentParams.value = res.data;
  modelOptions.value = models.data.map((item: any) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
};
const updateRadio = (val: any) => {
	console.log(val)
	if (val === 0) {
		return
	}
	let obj: any = modelParameterOptions.value.find((item: any) => {
		return item.model_style == val
	})
	modelParameterformData.value.temperature = Number(obj.model_temp);
	modelParameterformData.value.topP = Number(obj.top_p);
};
let modelTempUpdate = (v: number | null) => {
	console.log(v)
	if (v === null) {
		modelParameterformData.value.modelTemp = 0
	}
  modelParameterformData.value.style = 0;
}
// topP数据发生变化
let topPUpdate = (v: number | null) => {
	if (v === null) {
		modelParameterformData.value.topP = 0.1
	}
  modelParameterformData.value.style = 0;
}
// 初始化数据
initdatafun();
</script>

<style scoped>
@import './nodeConfigStyle.less';
</style>