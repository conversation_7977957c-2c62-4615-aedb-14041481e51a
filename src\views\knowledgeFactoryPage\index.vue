<template>
  <div class="app p-8 pr-[37px] pl-[40px]">
    <n-spin :show="loadingshow">

    <header class="bothends flex justify-between">
      <div
        class="title h-9 font-semibold text-[26px] text-[#2f3033] leading-9 flex items-center"
      >
        <img
          class="w-[22px] h-[22px] mr-2"
          src="@/assets/toolboxPage/titicon.png"
          alt=""
        />
        知识库工厂
      </div>
      
      <div class="w-[400px] h-12">
        <n-input
          v-model:value="searchvalue"
          clearable
          round
          placeholder="搜索知识库"
          size="large"
          @update:value="searchTap"
          class="modern-search-input"
        >
          <template #prefix>
            <div class="search-prefix-icon">
              <img class="w-[18px] h-[18px] opacity-60" src="../../assets/toolboxPage/SearchOutline.png">
            </div>
          </template>
          <template #suffix>
            <div class="search-suffix-btn" >
              <img class="w-[18px] h-[18px]" @click="searchTap" src="../../assets/toolboxPage/SearchOutline.png">
            </div>
          </template>
        </n-input>
      </div>
    </header>
    <div class="collectbox flex items-center justify-between">
      
      <div class="flex flex-wrap">
        <div v-for="(item, index) in moretagarr" :key="index"
          class="filter-tag"
          :class="{ 'filter-tag-active': item.ischeck }"
          @click="changmoretagfun(index)">
          {{ item.name }}
        </div>
      </div>
      <div class="header-right flex items-center gap-4">
        <div class="view-switcher">
          <div class="switch-btn" 
               :class="{ active: viewMode === 'list' }" 
               @click="viewMode = 'list'"
               title="列表视图">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="2" y="3" width="12" height="2" rx="0.5" fill="currentColor"/>
              <rect x="2" y="7" width="12" height="2" rx="0.5" fill="currentColor"/>
              <rect x="2" y="11" width="12" height="2" rx="0.5" fill="currentColor"/>
            </svg>
          </div>
          <div class="switch-btn" 
               :class="{ active: viewMode === 'card' }" 
               @click="viewMode = 'card'"
               title="卡片视图">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="2" y="3" width="5" height="4" rx="0.5" fill="currentColor"/>
              <rect x="9" y="3" width="5" height="4" rx="0.5" fill="currentColor"/>
              <rect x="2" y="9" width="5" height="4" rx="0.5" fill="currentColor"/>
              <rect x="9" y="9" width="5" height="4" rx="0.5" fill="currentColor"/>
            </svg>
          </div>
        </div>
        <div>
        <n-button class="creatapp" @click="jumpPage('/creatknowledgePage')"
          >创建知识库</n-button
        >
      </div>
      </div>
      
    </div>
    <div class="applicationrow" :class="{ 'card-view': viewMode === 'card' }">
      <div
        class="applicationbox"
        :class="{ 'card-layout': viewMode === 'card' }"
        v-for="(item, index) in applicationarr"
        :key="index"
        @click="handleItemClick(item)"
      >
        <img class="rowimg" :src="img1" />
        <div class="centerbox">
          <div class="boxrow">
            <div class="boxrowtit">{{ item.storeName }}</div>
            <div class="boxrowbumen">责任部门：{{ item.departName }}</div>
          </div>
          <div class="title-divider"></div>
          <div class="boxrowdes">
            {{ item.storeDesc }}
          </div>
          <div class="boxrow">
            <div class="tagsrow">
              <div class="tagbox"><img :src="tag1" />{{ item.fileNum }}个</div>
              <div class="tagbox">
                <img :src="tag2" />{{ bytetoGB(item.fileSize) }}MB
              </div>
              <div class="tagbox">
                <img :src="tag3" />{{ item.searchTimes }}次
              </div>
              <div class="tagbox">
                <img :src="tag4" />{{ item.visitTimes }}次
              </div>
              <div class="tagbox">
                <img :src="tag5" />{{ item.downloadTimes }}次
              </div>
            </div>
            <div class="boxrowbumen">更新时间：{{ item.createTime }}</div>
          </div>
        </div>
        <div class="btnrow">
          <p>
            <n-button @click.stop="jumpPage('/fileUploadPage', { id: item.id })"
              ><img class="btnicon" :src="upload" />上传</n-button
            >
          </p>
          <p>
            <n-button @click.stop="jumpPage('/fileManagementPage', { id: item.id })"
              ><img class="btnicon" :src="manage" />管理</n-button
            >
          </p>
          <p>
            <n-button
              @click.stop="
                jumpPage('/creatknowledgePage', {
                  storeName: item.storeName,
                  storeDesc: item.storeDesc,
                  id: item.id,
                })
              "
              ><img class="btnicon" :src="editicon" />编辑</n-button
            >
          </p>
          <p class="delbtn">
            <n-button @click.stop="handleDelete(item)"
              ><img class="btnicon" :src="delicon" />删除</n-button
            >
          </p>
        </div>
      </div>
      <div v-if="!applicationarr.length" class="empty-state" :class="{ 'card-view-empty': viewMode === 'card' }">
      <span class="empty-svg">
        <svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="12" y="28" width="72" height="48" rx="16" fill="#eaf1ff"/>
          <rect x="24" y="40" width="48" height="24" rx="8" fill="#b6d0ff"/>
          <circle cx="36" cy="52" r="4" fill="#fff"/>
          <circle cx="60" cy="52" r="4" fill="#fff"/>
          <rect x="44" y="64" width="8" height="4" rx="2" fill="#b6d0ff"/>
          <rect x="40" y="20" width="16" height="12" rx="6" fill="#b6d0ff"/>
          <rect x="46" y="12" width="4" height="8" rx="2" fill="#b6d0ff"/>
        </svg>
      </span>
      <div class="empty-text">暂无知识库！</div>
    </div>
    </div>
 
    <div class="paginationbox">
      <div class="pagination-controls">
        <n-pagination
          v-model:page="paginationdata.pageNum"
          v-model:page-size="paginationdata.pageSize"
          :item-count="paginationdata.pagecount"
          :on-update:page="paginationfun"
        />
        <div class="page-size-selector">
          <span class="page-size-label">每页显示：</span>
          <n-select
            v-model:value="paginationdata.pageSize"
            :options="pageSizeOptions"
            @update:value="handlePageSizeChange"
            size="small"
            style="width: 80px;"
          />
        </div>
      </div>
    </div>
  </n-spin>

  </div>
</template>
  
  <script setup>
import { ref, computed, onMounted } from "vue";
import {
  NInput,
  NIcon,
  NDivider,
  NButton,
  NPagination,
  NSelect,
  useDialog,
  useMessage,
  NSpin
} from "naive-ui";
import { getknowledgelistApi, delknowledgeApi,synchronousKnowledgeApi } from "@/api/knowledgeFactory";
import {getlinksApi} from '@/api/tools'
import { debounce } from '@/utils/functions/debounce'

import img1 from "@/assets/knowledgeFactoryPage/img1.png";
import editicon from "@/assets/knowledgeFactoryPage/editicon.png";
import delicon from "@/assets/knowledgeFactoryPage/delicon.png";
import manage from "@/assets/knowledgeFactoryPage/manage.png";
import upload from "@/assets/knowledgeFactoryPage/upload.png";

import tag1 from "@/assets/knowledgeFactoryPage/tag1.png";
import tag2 from "@/assets/knowledgeFactoryPage/tag2.png";
import tag3 from "@/assets/knowledgeFactoryPage/tag3.png";
import tag4 from "@/assets/knowledgeFactoryPage/tag4.png";
import tag5 from "@/assets/knowledgeFactoryPage/tag5.png";
import icon1 from "@/assets/applicationPage/icon1.png";
import icon2 from "@/assets/applicationPage/icon2.png";
import icon3 from "@/assets/applicationPage/icon3.png";
import icon4 from "@/assets/applicationPage/icon4.png";
import degreeofheat from "@/assets/toolboxPage/degreeofheat.png";
import notbookmarked from "@/assets/toolboxPage/notbookmarked.png";
import collected from "@/assets/toolboxPage/collected.png";
import { useRouter } from "vue-router";

var message = useMessage();

var loadingshow=ref(false)

var searchvalue = ref("");
var orderColumn = ref("updatedAt");
var paginationdata = ref({
  pageNum: 1,
  pageSize: 10,
  pagecount: 0,
});

// 每页显示数量选项
var pageSizeOptions = ref([
  { label: '5条', value: 5 },
  { label: '10条', value: 10 },
  { label: '20条', value: 20 },
  { label: '50条', value: 50 }
]);

// 处理每页显示数量变化
const handlePageSizeChange = (value) => {
  paginationdata.value.pageSize = value;
  paginationdata.value.pageNum = 1; // 重置到第一页
  getknowledgelisfun();
};
var moretagarr = ref([
  { name: "更新时间", ischeck: true, value: "updatedAt" },
  { name: "检索量", ischeck: false, value: 'searchTimes' },
  { name: "预览量", ischeck: false, value: 'visitTimes' },
  { name: "下载量", ischeck: false, value: 'downloadTimes' },
  { name: "文件个数", ischeck: false, value: 'fileNum' },
  { name: "空间占用", ischeck: false, value: 'fileSize' },
]);
var applicationarr = ref([]);
// 视图模式：'list' 列表模式，'card' 卡片模式
var viewMode = ref('list');

const router = useRouter();
function bytetoGB(bytes) {
  return (bytes / ( 1024 * 1024)).toFixed(2);
}
const jumpPage = (url, parameter) => {
  router.push({
    path: url,
    query: {
      ...parameter,
    },
  });
};

// 处理列表项点击事件，跳转到管理页面
const handleItemClick = (item) => {
  jumpPage('/fileManagementPage', { id: item.id });
};

const changmoretagfun = (index) => {
  console.log(index);
  moretagarr.value.forEach((item, i) => {
    item.ischeck = i === index;
  });
  paginationdata.value.pageNum = 1;
  orderColumn.value = moretagarr.value[index].value;
  getknowledgelisfun();
};
function paginationfun(page) {
  paginationdata.value.pageNum = page;
  getknowledgelisfun();
}
async function synchronousKnowledgefun() {
  var res= await synchronousKnowledgeApi()
  console.log(res);
}
function getknowledgelisfun() {
  loadingshow.value = true;
  var param = {
    pageNum: paginationdata.value.pageNum,
    pageSize: paginationdata.value.pageSize,
    sortfield: orderColumn.value,
    orderSort: 'desc',
    storeName: searchvalue.value,
    permissionFlag:true,
		source:0
  };
  console.log(param, "param");
  getknowledgelistApi(param).then((res) => {
    loadingshow.value = false;
    console.log(res);
    if (res.code == 0) {

      paginationdata.value.pagecount = res.data.total;
      applicationarr.value = res.data.items;      
    } else {
      message.error(res.message || res.msg);
    }
  }).catch(err => {
    loadingshow.value = false;
  })
}
const searchTap = debounce(()=>{
  paginationdata.value.pageNum = 1;
  getknowledgelisfun()
}, 500)
const dialog = useDialog();

const handleDelete = (item) => {
  dialog.warning({
    title: "删除确认",
    content: `确定要删除知识库 "${item.storeName}" 吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: () => {
      loadingshow.value=true;
      delknowledgeApi({ id: item.id }).then((res) => {
      loadingshow.value=false;
        if (res.code == "0") {
          paginationdata.value.pageNum = 1;
          getknowledgelisfun();
        } else {
          message.error(res.message);
        }
      });
    },
  });
};

onMounted(async () => {
 await synchronousKnowledgefun();
  getknowledgelisfun();
});
</script>
  
  <style scoped lang="less">
.app {
  background: url("@/assets/topbg.png") no-repeat;
  background-size: 90% 220px;
  background-position-x: 5%;
}

// 现代化搜索框样式
.modern-search-input {
  :deep(.n-input-wrapper) {
    padding-right: 4px;
    padding-left: 16px;
    border: none;
    background: transparent;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border-radius: 3rem;
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    &.n-input-wrapper--focus {
      box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1);
    }
  }

  :deep(.n-input__input-el) {
    font-size: 15px;
    color: #2f3033;

    &::placeholder {
      color: #9ca3af;
      font-weight: 400;
    }
  }
}

.search-prefix-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

// 视图切换器样式
.view-switcher {
  display: flex;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.switch-btn {
  width: 40px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  background: #ffffff;
  transition: all 0.2s ease;
  
  &:hover {
    color: #125EFF;
    background: rgba(18, 94, 255, 0.05);
  }
  
  &.active {
    color: #ffffff;
    background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
  }
}

.search-suffix-btn {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
  }

  &:active {
    transform: scale(0.98);
  }

  img {
    filter: brightness(0) invert(1);
  }
}
// 过滤标签样式
.filter-tag {
  width: 128px;
  height: 40px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  font-weight: 500;
  font-size: 14px;
  color: #6b7280;
  line-height: 40px;
  text-align: center;
  margin-right: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #125EFF;
    color: #125EFF;
    box-shadow: 0 2px 8px rgba(18, 94, 255, 0.1);
  }

  &.filter-tag-active {
    background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
    border-color: #125EFF;
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(18, 94, 255, 0.25);
  }
}
/deep/ .searchbox .n-input-wrapper {
  padding-right: 2px;
}
.collectbox {
  margin-top: 34px;
}
.applicationrow {
  margin-top: 32px;
  padding: 24px 0;
  overflow-x: auto;
  min-height: 400px;
}

// 卡片视图布局样式
.applicationrow.card-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  overflow-x: visible;
  justify-content: space-between;
  align-content: flex-start;
}

// 卡片布局样式
.applicationbox.card-layout {
  min-width: auto;
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 320px;
  padding: 20px;
  margin-bottom: 0;
  
  .rowimg {
    width: 100%;
    height: 160px;
    margin-right: 0;
    margin-bottom: 16px;
    border-radius: 12px;
  }
  
  .centerbox {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .boxrow {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 12px;
  }
  
  .boxrowtit {
    font-size: 18px;
    line-height: 24px;
    white-space: normal;
    overflow: visible;
  }
  
  .boxrowbumen {
    font-size: 14px;
    line-height: 16px;
  }
  
  .boxrowdes {
    height: auto;
    margin-bottom: 16px;
    -webkit-line-clamp: 2;
  }
  
  .tagsrow {
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
    
    .tagbox {
      min-width: auto;
      padding: 0 8px;
      font-size: 12px;
    }
  }
  
  .btnrow {
    margin-left: 0;
    margin-right: 0;
    display: flex;
    justify-content: space-between;
    gap: 8px;
    
    p {
      flex: 1;
      margin: 0;
      
      button {
        width: 100%;
        margin: 0;
      }
    }
  transition: all 0.3s ease;
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .applicationrow.card-view {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 1200px) {
  .applicationrow.card-view {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 18px;
  }
}

@media (max-width: 992px) {
  .applicationrow.card-view {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }
  
  .applicationbox.card-layout {
    min-height: 300px;
    
    .rowimg {
      height: 140px;
    }
  }
}

@media (max-width: 768px) {
  .header-right {
    flex-direction: column;
    gap: 12px;
  }
  
  .header-right > div:nth-child(2) {
    width: 100%;
  }
  
  .applicationrow.card-view {
    grid-template-columns: 1fr;
    gap: 16px;
    justify-content: center;
  }
  
  .applicationbox.card-layout {
    min-height: auto;
    
    .btnrow {
      flex-direction: column;
      
      p {
        button {
          padding: 0 12px;
        }
      }
    }
  }
  
  .applicationbox:not(.card-layout) {
    min-width: 100%;
    padding: 16px;
    height: auto;
    flex-direction: column;
    
    .rowimg {
      width: 100%;
      height: 160px;
      margin-right: 0;
      margin-bottom: 16px;
    }
    
    .centerbox {
      width: 100%;
    }
    
    .btnrow {
      margin-left: 0;
      margin-right: 0;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 8px;
      
      p {
// 卡片视图下的空状态样式
.empty-state.card-view-empty {
  grid-column: 1 / -1;
  margin: 0;
}

        flex: 1;
        min-width: calc(50% - 4px);
        margin: 0;
      }
    }
  }
}

@media (max-width: 480px) {
  .app {
    padding: 16px 20px;
  }
  
  .title {
    font-size: 22px;
  }
  
  .collectbox {
    margin-top: 24px;
  }
  
  .applicationrow {
    margin-top: 24px;
  }
  
  .applicationbox.card-layout {
    padding: 16px;
    
    .btnrow {
      flex-direction: column;
      
      p {
        width: 100%;
      }
    }
  }
  
  .applicationbox:not(.card-layout) {
    .btnrow {
      p {
        min-width: 100%;
      }
    }
  }
}
.applicationbox {
  min-width: 1300px;
  display: flex;
  height: 220px;
  align-items: center;
  padding-left: 24px;
  padding-right: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border: 1px solid #e8ecf0;
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}
/* 精致的悬浮效果 */
.applicationbox:hover {
  background: #ffffff;
  border-color: #125EFF;
  box-shadow: 0 8px 32px rgba(18, 94, 255, 0.12);
  transform: translateY(-2px);
}
.rowimg {
  width: 200px;
  height: 160px;
  margin-right: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  .applicationbox:hover & {
    box-shadow: 0 4px 16px rgba(18, 94, 255, 0.15);
    transform: scale(1.02);
  }
}
.btnrow {
  margin-left: 20px;
  margin-right: 20px;
  button {
    margin-top: 6px;
    margin-bottom: 6px;
    width: 84px;
    height: 36px;
    background: linear-gradient(135deg, rgba(18, 94, 255, 0.08) 0%, rgba(18, 94, 255, 0.12) 100%);
    border: 1px solid rgba(18, 94, 255, 0.2);
    border-radius: 8px;
    font-weight: 500;
    font-size: 13px;
    color: #125eff;
    transition: all 0.2s ease;

    &:hover {
      background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
    }
  }
}
.delbtn {
  button {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.08) 0%, rgba(239, 68, 68, 0.12) 100%) !important;
    border: 1px solid rgba(239, 68, 68, 0.2) !important;
    color: #ef4444 !important;

    &:hover {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
      color: white !important;
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3) !important;
    }
  }
}
.btnicon {
  width: 13.2px;
  height: 12.77px;
  margin-right: 6.5px;
}
.centerbox {
  flex: 1;
}
.boxrow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.boxrowtit {
  height: 48px;
  font-weight: 600;
  font-size: 20px;
  color: #1f2937;
  line-height: 24px;
  transition: color 0.2s ease;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-wrap: break-word;
  word-break: break-all;

  .applicationbox:hover & {
    color: #125EFF;
  }
}
.boxrowbumen {
  height: 18px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 15px;
  color: #909399;
  letter-spacing: 0;
  line-height: 17.07px;
}
.boxrowdes {
  height: 60px;
  background: transparent;
  border: none;
  border-radius: 0;
  margin-top: 8px;
  font-weight: 400;
  font-size: 13px;
  color: #6b7280;
  line-height: 18px;
  padding: 0;
  margin-bottom: 16px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  transition: all 0.2s ease;

  .applicationbox:hover & {
    color: #4b5563;
  }
}

.title-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #e5e7eb 20%, #e5e7eb 80%, transparent 100%);
  margin: 12px 0 8px 0;
  transition: all 0.2s ease;

  .applicationbox:hover & {
    background: linear-gradient(90deg, transparent 0%, #125EFF 20%, #125EFF 80%, transparent 100%);
    opacity: 0.3;
  }
}
.tagsrow {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;

  .tagbox {
    min-width: 100px;
    height: 32px;
    background: rgba(18, 94, 255, 0.08);
    border: 1px solid rgba(18, 94, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    font-size: 13px;
    color: #125EFF;
    font-weight: 500;
    transition: all 0.2s ease;

    img {
      width: 16px;
      height: 16px;
      margin-right: 6px;
      opacity: 0.8;
    }

    &:hover {
      background: rgba(18, 94, 255, 0.15);
      border-color: #125EFF;
      transform: translateY(-1px);

      img {
        opacity: 1;
      }
    }
  }
}
.creatapp {
  width: 120px;
  height: 40px;
  background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
  color: #ffffff !important;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    background: linear-gradient(135deg, #0052cc 0%, #1a6bcc 100%);
    color: #ffffff !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
  }
}



.paginationbox {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 32px;
  padding: 20px 0;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 800px;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-label {
  font-size: 14px;
  color: #6b7280;
  white-space: nowrap;
}
.empty-state {
  width: 100%;
  min-height: 320px;
  padding: 48px 0;
  margin: 40px auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8faff 0%, #eef4ff 100%);
  border-radius: 24px;
  box-shadow: 0 4px 16px rgba(18, 94, 255, 0.08);
  .empty-svg {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    svg {
      display: block;
      width: 96px;
      height: 96px;
      opacity: 0.85;
    }
  }
  .empty-text {
    color: #7a8ca3;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    letter-spacing: 1px;
  }
}
</style>
  