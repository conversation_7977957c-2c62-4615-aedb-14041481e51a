<script setup>
import { computed, ref, onMounted, watch } from "vue";
import { NDivider, NInput,NSpin } from "naive-ui";
import { getlinksApi } from "@/api/tools";
import {getlistApi,setcollectionApi,delcollectionApi,setheatApi} from "@/api/applicationPage"
import { debounce } from '@/utils/functions/debounce'

import icon1 from "@/assets/applicationPage/icon1.png";
import icon2 from "@/assets/applicationPage/icon2.png";
import icon3 from "@/assets/applicationPage/icon3.png";
import icon4 from "@/assets/applicationPage/icon4.png";
import iconhui4 from "@/assets/applicationPage/iconhui4.png";
import degreeofheat from "@/assets/toolboxPage/degreeofheat.png";
import huidegreeofheat from "@/assets/applicationPage/huidegreeofheat.png";
import notbookmarked from "@/assets/toolboxPage/notbookmarked.png";
import collected from "@/assets/toolboxPage/collected.png";
var imglist={
  icon1:icon1,
  icon2:icon2,
  icon3:icon3,
  icon4:icon4,
  iconhui4:iconhui4,
}
const searchvalue = ref("");
var loadingshow=ref(false)
const moretagarr = ref([
  { name: "全部", ischeck: true, value: 0 },
]);
var applicationarr = ref([]);
const openApplicationLink = (item) => {
  if (item.status=='1') return;

  setheatApi({mainBodyId:item.id,category:'1',id:item.heatScaleId}).then(res=>{
    console.log(item.name);
  switch (item.name) {
    case "AI 伴学":
      window.$Eucp.navigation.openInNewTab("eaide_student");
      break;
    case "AI 校务助手":
      window.$Eucp.navigation.openInNewTab("eaide_xiaowu");
      break;
      case "AI 教研教学辅助":
      window.$Eucp.navigation.openInNewTab("eaide_eta");
      break;
    default:
      window.open(item.link, "_blank");
  }
  getlistfun();
  })

};

const changecollectfun = (record) => {
  if (record.status=='1') return;
  loadingshow.value=true;

  if(record.isCollection){
    delcollectionApi({collectionId:record.collectionId}).then(res=>{
     getlistfun();
    })
  }else{
  setcollectionApi({mainBodyId:record.id,category:'1'}).then(res=>{
    getlistfun();
  })
  }
};

const changmoretagfun = (index) => {
  moretagarr.value.forEach((item, i) => {
    item.ischeck = i === index;
  });
};
function getlistfun(isSave = false) {
  console.log("获取应用汇列表接口");
  if (!loadingshow.value) {
    loadingshow.value = true;
  }
  getlistApi({ name: searchvalue.value }).then(res => {
    loadingshow.value = false;
    res.data.forEach(item => {
      item.icon = imglist[item.icon];
    });
    applicationarr.value = res.data;
  }).catch(() => {
    loadingshow.value = false;
  })
}
const searchTap = debounce(getlistfun, 500)
onMounted(() => {
  getlistfun();
});
</script>

<template>
  <div class="app p-8 pr-[37px] pl-[40px]">
     <n-spin :show="loadingshow">
    <header class="bothends flex justify-between items-center">
      <div class="title h-9 font-semibold text-[26px] text-[#2f3033] leading-9 flex items-center">
        <img class="w-[22px] h-[22px] mr-2" src="@/assets/toolboxPage/titicon.png" alt="">
        智教应用汇
      </div>
      <div class="w-[400px] h-12">
        <NInput
          v-model:value="searchvalue"
          clearable
          round
          placeholder="搜索应用"
          size="large"
          class="modern-search-input"
          @update:value="getlistfun"
        >
          <template #prefix>
            <div class="search-prefix-icon">
              <img class="w-[18px] h-[18px] opacity-60" src="../../assets/toolboxPage/SearchOutline.png">
            </div>
          </template>
          <template #suffix>
            <div class="search-suffix-btn" @click="searchTap">
              <img class="w-[18px] h-[18px]" src="../../assets/toolboxPage/SearchOutline.png">
            </div>
          </template>
        </NInput>
      </div>
    </header>

    <div class="section-title">
      <img class="w-6 h-6 mr-3" src="@/assets/toolboxPage/collecticon.png">
      我的收藏
    </div>

   	<div class="collectbox flex flex-wrap">
				<div
					v-for="(item, index) in applicationarr.filter(
						(item) => item.isCollection
					)"
					:key="index"
					class="collect-tag"
					@click="jumpfun(item)"
				>
					<img class="collect-icon" :src="item.icon" />
					<p>{{ item.name }}</p>
				</div>
			</div>

    <div class="section-title section-title-top">
      <img class="w-6 h-6 mr-3" src="@/assets/toolboxPage/moreicon.png">
      发现更多
    </div>

    <div class="collectbox flex flex-wrap">
      <div v-for="(item, index) in moretagarr" :key="index"
        class="category-tag"
        :class="{ 'category-tag-active': item.ischeck }"
        @click="changmoretagfun(index)">
        {{ item.name }}
      </div>
    </div>
    <div class="applicationrow" v-if="applicationarr.length">
        <div v-for="(item, index) in applicationarr" :key="index"
          class="application-card"
          :class="{ 'application-card-disabled': item.status=='1' }"
          @click="openApplicationLink(item)">
          <div class="card-content">
            <div class="app-icon-wrapper">
              <img class="app-icon" :src="item.icon">
            </div>
            <div class="app-info">
              <h3 class="app-name">{{ item.name }}</h3>
              <p class="app-description">{{ item.des }}</p>
            </div>
          </div>
          <div class="card-divider"></div>
          <div class="app-meta">
            <div class="heat-info">
              <img class="meta-icon" :src="item.status=='1' ? huidegreeofheat : degreeofheat">
              <span>热度 {{ item.heatScaleNum || 0 }}</span>
            </div>
            <div class="collect-action" @click.stop="changecollectfun(item)">
              <img class="meta-icon" :src="item.isCollection ? collected : notbookmarked">
              <span>{{ item.isCollection ? '已收藏' : '收藏' }}</span>
            </div>
          </div>
        </div>
      </div>
     <div v-else class="empty-state">
       <span class="empty-svg">
         <svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
           <rect x="12" y="28" width="72" height="48" rx="16" fill="#eaf1ff"/>
           <rect x="24" y="40" width="48" height="24" rx="8" fill="#b6d0ff"/>
           <circle cx="36" cy="52" r="4" fill="#fff"/>
           <circle cx="60" cy="52" r="4" fill="#fff"/>
           <rect x="44" y="64" width="8" height="4" rx="2" fill="#b6d0ff"/>
           <rect x="40" y="20" width="16" height="12" rx="6" fill="#b6d0ff"/>
           <rect x="46" y="12" width="4" height="8" rx="2" fill="#b6d0ff"/>
         </svg>
       </span>
       <div class="empty-text">暂无应用！</div>
     </div>
    </n-spin>
  </div>
</template>

<style scoped lang="less">
  // 现代化搜索框样式
  .modern-search-input {
    :deep(.n-input-wrapper) {
      padding-right: 4px;
      padding-left: 16px;
      border: none;
      background: transparent;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      border-radius: 3rem;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      &.n-input-wrapper--focus {
        box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1);
      }
    }

    :deep(.n-input__input-el) {
      font-size: 15px;
      color: #2f3033;

      &::placeholder {
        color: #9ca3af;
        font-weight: 400;
      }
    }
  }

  .search-prefix-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
  }

  .search-suffix-btn {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
    }

    &:active {
      transform: scale(0.98);
    }

    img {
      filter: brightness(0) invert(1);
    }
  }

  // 章节标题样式
  .section-title {
    height: 36px;
    font-weight: 600;
    font-size: 20px;
    color: #1f2937;
    line-height: 36px;
    display: flex;
    align-items: center;
    margin-top: 32px;
    margin-bottom: 16px;

    &.section-title-top {
      margin-top: 40px;
    }
  }

  // 收藏标签样式
.collect-tag {
	min-width: 120px;
	height: 40px;
	background: #125eff14;
	border: 1px solid #125eff26;
	border-radius: 10px;

	line-height: 40px;
	text-align: center;
	margin-right: 12px;
	margin-bottom: 12px;
	padding: 0 16px;
	cursor: pointer;
	transition: all 0.2s ease;
	display: flex;
	justify-content: space-evenly;
	.collect-icon {
		width: 22px;
		height: 22px;
		margin-top: 8px;
		margin-right: 4px;
	}
	> p {
		font-weight: 500;
		font-size: 14px;
	}
	&:hover {
		background: linear-gradient(135deg, #125eff 0%, #1e7fff 100%);
		color: #ffffff;
		transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(18, 94, 255, 0.25);
	}
}
  // 分类标签样式
  .category-tag {
    width: 128px;
    height: 40px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    font-weight: 500;
    font-size: 14px;
    color: #6b7280;
    line-height: 40px;
    text-align: center;
    margin-right: 12px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: #125EFF;
      color: #125EFF;
      box-shadow: 0 2px 8px rgba(18, 94, 255, 0.1);
    }

    &.category-tag-active {
      background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
      border-color: #125EFF;
      color: #ffffff;
      box-shadow: 0 4px 12px rgba(18, 94, 255, 0.25);
    }
  }

  // 应用卡片网格布局
  .applicationrow {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(330px, 1fr));
    gap: 20px;
    margin-top: 32px;
  }

  // 现代化应用卡片设计
  .application-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border: 1px solid #e8ecf0;
    border-radius: 16px;
    padding: 24px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    &:hover {
      background: #ffffff;
      border-color: #125EFF;
      box-shadow: 0 8px 32px rgba(18, 94, 255, 0.12);
      transform: translateY(-2px);
    }

    &.application-card-disabled {
      background: #f7f7f7;
      opacity: 0.7;
      cursor: not-allowed;

      &:hover {
        transform: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border-color: #e8ecf0;
      }
    }
  }

  .card-content {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
  }

  .app-icon-wrapper {
    position: relative;
    margin-right: 20px;

    // 默认状态的微妙背景
    &::before {
      content: '';
      position: absolute;
      top: -6px;
      left: -6px;
      right: -6px;
      bottom: -6px;
      background: linear-gradient(135deg, rgba(18, 94, 255, 0.05) 0%, rgba(30, 127, 255, 0.02) 100%);
      border-radius: 18px;
      opacity: 1;
      transition: all 0.3s ease;
    }

    .application-card:hover &::before {
      background: linear-gradient(135deg, rgba(18, 94, 255, 0.15) 0%, rgba(30, 127, 255, 0.08) 100%);
      transform: scale(1.05);
    }
  }

  .app-icon {
    width: 64px;
    height: 64px;
    border-radius: 12px;
    position: relative;
    z-index: 1;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    .application-card:hover & {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      transform: scale(1.02);
    }
  }

  .app-info {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .app-name {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    line-height: 24px;
    margin: 0 0 8px 0;
    transition: color 0.2s ease;

    .application-card:hover & {
      color: #125EFF;
    }
  }

  .app-description {
    font-size: 14px;
    color: #6b7280;
    line-height: 20px;
    margin: 0;
  }

  .card-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #e5e7eb 20%, #e5e7eb 80%, transparent 100%);
    margin: 16px 0;
  }

  .app-meta {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .heat-info,
  .collect-action {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #6b7280;
    transition: color 0.2s ease;

    .meta-icon {
      width: 14px;
      height: 14px;
      margin-right: 6px;
      opacity: 0.8;
    }
  }

  .collect-action {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(18, 94, 255, 0.08);
      color: #125EFF;

      .meta-icon {
        opacity: 1;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .modern-search-input {
      width: 350px !important;
    }
  }

  @media (max-width: 768px) {
    .app {
      padding: 16px 20px;
    }

    .bothends {
      flex-direction: column;
      gap: 20px;
      align-items: flex-start;
    }

    .modern-search-input {
      width: 100% !important;
    }

    .applicationrow {
      grid-template-columns: 1fr;
    }

    .card-content {
      padding: 20px;
    }

    .section-title {
      font-size: 18px;
      margin-top: 24px;
    }
  }
  .empty-state {
    width: 100%;
    min-height: 320px;
    padding: 48px 0;
    margin: 40px auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8faff 0%, #eef4ff 100%);
    border-radius: 24px;
    box-shadow: 0 4px 16px rgba(18, 94, 255, 0.08);
    .empty-svg {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;
      svg {
        display: block;
        width: 96px;
        height: 96px;
        opacity: 0.85;
      }
    }
    .empty-text {
      color: #7a8ca3;
      font-size: 18px;
      font-weight: 500;
      text-align: center;
      letter-spacing: 1px;
    }
  }
</style>
