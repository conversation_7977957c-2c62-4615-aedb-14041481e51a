<template>
  <div class="node-panel">
    <!-- 空状态提示 -->
    <div
      v-if="filteredCategories.length === 0"
      class="empty-state text-center py-8"
    >
      <div class="text-4xl text-gray-300 mb-3">🔍</div>
      <p class="text-sm text-gray-500">未找到匹配的节点</p>
      <p class="text-xs text-gray-400 mt-1">尝试调整搜索关键词</p>
    </div>

    <!-- 节点分类列表 -->
    <div v-else class="node-categories">
      <div
        v-for="category in filteredCategories"
        :key="category.name"
        class="category-section mb-6"
      >
        <h4
          class="category-title text-sm font-semibold text-[#2f3033] mb-3 flex items-center bg-[#ffffff]"
        >
          {{ category.name }}
        </h4>

        <div class="node-list space-y-3">
          <div
            v-for="nodeType in category.nodes"
            :key="nodeType.type"
            class="node-item h-[50px] group relative rounded-xl cursor-grab hover:border-[#125EFF] hover:shadow-lg transition-all duration-200"
            :class="{
              'dragging': isDragging && draggedNodeType === nodeType.type,
              'hover:-translate-y-1': !(isDragging && draggedNodeType === nodeType.type),
              'nodeitemdisabled': nodeType.disabled
            }"
            :draggable="!nodeType.disabled"
            @dragstart="handleDragStart($event, nodeType)"
            @dragend="handleDragEnd"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <img class="w-[18px] h-[18px]" :src="nodeType.icon" />

                <div
                  class="node-name text-sm font-semibold text-[#2f3033] truncate"
                >
                  {{ nodeType.label }}
                </div>
                <NPopover trigger="click">
                  <template #trigger>
                    <img
                      class="w-[14px] h-[14px]"
                      src="@/assets/agentOrchestration/promptIcon.png"
                    />
                  </template>
                  <span> {{ nodeType.description }}</span>
                </NPopover>
              </div>
              <div>
                <img
                  class="w-[12px] h-[12px]"
                  src="@/assets/agentOrchestration/nodeAddIcon.png"
                />
              </div>
            </div>
            <!-- <div class="flex items-center">
              <div
                class="node-icon w-10 h-10 rounded-xl flex items-center justify-center mr-3 shadow-sm"
              >
                <span class="text-lg font-bold">{{ nodeType.icon }}</span>
                <div class="node-name text-sm font-semibold text-[#2f3033] truncate">
                  {{ nodeType.label }}
                </div>
                
                <span class="text-lg font-bold">{{ nodeType.icon }}</span>
              </div>



            </div> -->

            <!-- 悬浮时的额外信息 -->
            <!-- <div class="node-extra opacity-0 group-hover:opacity-100 transition-opacity duration-200 mt-2 pt-2 border-t border-gray-100">
              <div class="flex items-center justify-between text-xs text-[#64646D]">
                <span class="flex items-center">
                  <span class="w-2 h-2 rounded-full mr-1" :style="{ backgroundColor: nodeType.color }"></span>
                  {{ nodeType.category || '通用' }}
                </span>
                <span class="drag-hint">拖拽到画布</span>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>

    <!-- 拖拽遮罩 -->
    <div
      v-if="isDragging"
      class="drag-overlay fixed inset-0 pointer-events-none z-50 bg-blue-50 bg-opacity-30"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { NodeType } from "@/store/modules/orchestration";
import { NPopover } from "naive-ui";
import endIcon from "@/assets/agentOrchestration/endIcon.png";
import aggregationIcon from "@/assets/agentOrchestration/aggregationIcon.png";
import conditionalBranchIcon from "@/assets/agentOrchestration/conditionalBranchIcon.png";
import cycleIcon from "@/assets/agentOrchestration/cycleIcon.png";
import DirectReplyIcon from "@/assets/agentOrchestration/DirectReplyIcon.png";
import knowledgeRetrievalIcon from "@/assets/agentOrchestration/knowledgeRetrievalIcon.png";
import textGenerationIcon from "@/assets/agentOrchestration/textGenerationIcon.png";
import toolIcon from "@/assets/agentOrchestration/toolIcon.png";
import variableIcon from "@/assets/agentOrchestration/variableIcon.png";
import intentionRecognitionIcon from "@/assets/agentOrchestration/intentionRecognitionIcon.png";
import startIcon from "@/assets/agentOrchestration/startIcon.png";

interface NodeTypeInfo {
  type: NodeType;
  label: string;
  description: string;
  icon: string;
  color: string;
  category?: string;
  tags?: string[];
}

interface NodeCategory {
  name: string;
  icon: string;
  color: string;
  nodes: NodeTypeInfo[];
}

const props = defineProps<{
  searchValue?: string;
}>();

const emit = defineEmits<{
  nodeDragStart: [nodeType: string];
}>();

// 拖拽状态
const isDragging = ref(false);
const draggedNodeType = ref<string>("");

// 节点类型定义
const nodeCategories: NodeCategory[] = [
  {
    name: "基础节点",
    icon: "⚡",
    color: "#52c41a",
    nodes: [
      {
        type: NodeType.INTENTION_RECOGNITION,
        label: "意图识别",
        description: "识别用户的意图和目标",
        icon: intentionRecognitionIcon,
        color: "#2f54eb",
        category: "AI",
        tags: ["识别", "知识"],
      },
      {
        type: NodeType.LLM,
        label: "文本生成",
        description: "文本生成的描述部分",
        icon: textGenerationIcon,
        color: "#2f54eb",
        category: "AI",
        tags: ["生成", "知识"],
      },
      {
        type: NodeType.CONDITION,
        label: "条件分支",
        description: "根据条件进行分支控制",
        icon: conditionalBranchIcon,
        color: "#fa8c16",
        category: "控制",
        tags: ["分支", "逻辑"],
        disabled: true,
      },
      {
        type: NodeType.CYCLE,
        label: "循环",
        description: "重复执行指定的操作",
        icon: cycleIcon,
        color: "#faad14",
        category: "控制",
        tags: ["循环", "重复"],
        disabled: true,

      },
      {
        type: NodeType.AGGREGATION,
        label: "聚合",
        description: "聚合的描述",
        icon: aggregationIcon,
        color: "#faad14",
        category: "合并",
        tags: ["合并", "重复"],
        disabled: true,

      },
      {
        type: NodeType.ASSIGNER,
        label: "变量赋值",
        description: "变量赋值的描述",
        icon: variableIcon,
        color: "#faad14",
        category: "合并",
        tags: ["合并", "赋值"],
        disabled: true,

      },
      {
        type: NodeType.KNOWLEDGE,
        label: "知识检索",
        description: "从知识库中检索相关信息",
        icon: knowledgeRetrievalIcon,
        color: "#2f54eb",
        category: "AI",
        tags: ["检索", "知识"],
        disabled: true,

      },
      {
        type: NodeType.DIRECTREPLY,
        label: "直接回复",
        description: "直接回复的描述",
        icon: DirectReplyIcon,
        color: "#faad14",
        category: "回复",
        tags: ["回复"],
        disabled: true,

      },
      {
        type: NodeType.TOOL,
        label: "工具",
        description: "工具的描述",
        icon: toolIcon,
        color: "#faad14",
        category: "工具",
        tags: ["工具"],
        disabled: true,

      },
  
    ],
  },
];

// 过滤节点类型
const filteredCategories = computed(() => {
  if (!props.searchValue) {
    return nodeCategories;
  }

  const searchLower = props.searchValue.toLowerCase();
  return nodeCategories
    .map((category) => ({
      ...category,
      nodes: category.nodes.filter(
        (node) =>
          node.label.toLowerCase().includes(searchLower) ||
          node.description.toLowerCase().includes(searchLower) ||
          (node.tags &&
            node.tags.some((tag) => tag.toLowerCase().includes(searchLower))) ||
          (node.category && node.category.toLowerCase().includes(searchLower))
      ),
    }))
    .filter((category) => category.nodes.length > 0);
});

// 拖拽开始处理
const handleDragStart = (event: DragEvent, nodeType: NodeTypeInfo) => {
  isDragging.value = true;
  draggedNodeType.value = nodeType.type;

  if (event.dataTransfer) {
    // 设置拖拽数据
    event.dataTransfer.setData(
      "application/json",
      JSON.stringify({
        type: nodeType.type,
        label: nodeType.label,
        color: nodeType.color,
        icon: nodeType.icon,
        description: nodeType.description,
      })
    );
    event.dataTransfer.effectAllowed = "copy";

    // 创建自定义拖拽图像
    const dragImage = createDragImage(nodeType);
    event.dataTransfer.setDragImage(dragImage, 50, 25);
  }

  emit("nodeDragStart", nodeType.type);
};

// 拖拽结束处理
const handleDragEnd = () => {
  isDragging.value = false;
  draggedNodeType.value = "";
};

// 创建拖拽图像
const createDragImage = (nodeType: NodeTypeInfo) => {
  const dragElement = document.createElement("div");
  dragElement.className = "drag-preview";
  dragElement.style.cssText = `
    position: absolute;
    top: -1000px;
    left: -1000px;
    width: 200px;
    height: 50px;
    background: white;
    border: 2px solid ${nodeType.color};
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `;
dragElement.innerHTML = `
    <div style="
      flex: 1;
      font-size: 14px;
      font-weight: 600;
      color: #2f3033;
    ">${nodeType.label}</div>
  `;
  // dragElement.innerHTML = `
  //   <div style="
  //     width: 32px;
  //     height: 32px;
  //     background: ${nodeType.color}20;
  //     color: ${nodeType.color};
  //     border-radius: 8px;
  //     display: flex;
  //     align-items: center;
  //     justify-content: center;
  //     margin-right: 8px;
  //     font-size: 16px;
  //     font-weight: bold;
  //   ">${nodeType.icon}</div>
  //   <div style="
  //     flex: 1;
  //     font-size: 14px;
  //     font-weight: 600;
  //     color: #2f3033;
  //   ">${nodeType.label}</div>
  // `;

  document.body.appendChild(dragElement);

  // 清理函数
  setTimeout(() => {
    document.body.removeChild(dragElement);
  }, 0);

  return dragElement;
};
</script>

<style scoped lang="less">
.node-panel {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    opacity: 0;
    transition: opacity 0.3s ease;

    &:hover {
      background: #a8a8a8;
    }
  }

  &:hover::-webkit-scrollbar-thumb {
    opacity: 1;
  }

  .empty-state {
    padding: 40px 20px;

    .text-4xl {
      font-size: 2.5rem;
    }
  }

  .category-section {
    .category-title {
      position: sticky;
      top: 0;
      // background: #F7F9FF;
      padding: 8px 0;
      z-index: 2;
      margin: 0 -4px;
      padding-left: 4px;
      padding-right: 4px;
      border-radius: 6px;
      height: 22px;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #323233;
      letter-spacing: 0;
      padding-top: 17px;
      margin-bottom: 14px;
      .category-icon {
        font-weight: bold;
      }

      .category-count {
        font-size: 10px;
        min-width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  .node-name {
    transition: color 0.2s ease;
    height: 22px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 16px;
    color: #323233;
    letter-spacing: 0;
    margin-right: 5px;
    margin-left: 14px;
  }

  .node-item {
    user-select: none;
    position: relative;
    background: white;
    padding: 14px 16px;
    background: #FCFDFD;
    border: 1px solid #E5EBF1;
    border-radius: 6px;

    &:hover {
      cursor: grab;
      border-color: #125eff !important;
      box-shadow: 0 2px 8px rgba(18, 94, 255, 0.15) !important;

      .drag-indicator {
        opacity: 1;
      }
    }

    &:active {
      cursor: grabbing;
    }

    &.dragging {
      opacity: 0.5;
      border-color: #125eff;
    }

    .drag-indicator {
      transition: opacity 0.2s ease;

      svg {
        transform: rotate(90deg);
      }
    }

    .node-icon {
      transition: all 0.2s ease;
      position: relative;
    }

    .node-info {
      .node-name {
        transition: color 0.2s ease;
      }

      .node-desc {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
      }

      .node-tags {
        .tag {
          transition: all 0.2s ease;

          &:hover {
            background: #e6f7ff;
            color: #1890ff;
          }
        }
      }
    }

    .node-extra {
      .drag-hint {
        font-style: italic;
        opacity: 0.8;
      }
    }
  }

  .drag-overlay {
    background: radial-gradient(
      circle at center,
      rgba(18, 94, 255, 0.1) 0%,
      rgba(18, 94, 255, 0.05) 50%,
      transparent 100%
    );
    backdrop-filter: blur(1px);
  }

  // 拖拽状态下的特殊样式 - 简化处理，只改变透明度
  .node-item.dragging {
    opacity: 0.6;
    // 保持其他样式不变，确保拖拽功能正常
  }
}
.nodeitemdisabled{
  background-color: #F3F4F6 !important;
  opacity: 0.5;
}
// 全局拖拽预览样式
:global(.drag-preview) {
  pointer-events: none;
  z-index: 9999;
}

// 响应式设计
@media (max-width: 768px) {
  .node-panel {
    .node-item {
      padding: 12px;

      .node-icon {
        width: 36px;
        height: 36px;
        margin-right: 12px;
      }

      .node-info {
        .node-name {
          font-size: 13px;
        }

        .node-desc {
          font-size: 11px;
        }
      }
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .node-panel {
    .node-item {
      background: linear-gradient(135deg, #1f1f1f 0%, #2a2a2a 100%);
      border-color: #404040;

      .node-name {
        color: #ffffff;
      }

      .node-desc {
        color: #cccccc;
      }

      &:hover {
        border-color: #125eff;
        box-shadow: 0 8px 25px rgba(18, 94, 255, 0.25);
      }
    }

    .category-title {
      background: #1a1a1a;
      color: #ffffff;
    }
  }
}
</style>
