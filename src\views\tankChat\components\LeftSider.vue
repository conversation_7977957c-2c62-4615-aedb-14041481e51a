<script setup lang='ts'>
import type { CSSProperties } from 'vue'
import { computed, ref, watch } from 'vue'
import { NInput, NLayoutSider, NPopconfirm } from 'naive-ui'
import { useRouter } from 'vue-router'
import { logout } from '@/utils/microFrontEnd'
import List from './List.vue'
import Footer from './Footer.vue'
import logo2 from '@/assets/logotit.png'

import jia2 from '@/assets/sider/jia2.png'
import loginoutIcon from '@/assets/loginout.png'
import userIcon from '@/assets/usericon.png'
import { useAppStore, useChatStore } from '@/store'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { PromptStore } from '@/components/common'
import { infoStore } from '@/store/modules/info'

const appStore = useAppStore()
const chatStore = useChatStore()
const info = infoStore()
const router = useRouter()



const { isMobile } = useBasicLayout()
const show = ref(false)

const collapsed = computed(() => appStore.siderCollapsed)

async function handleAdd() {
  router.push({ name: 'Chat' })
  chatStore.activeHistory('')
  return
  await chatStore.newAddHistory()
  if (isMobile.value)
    appStore.setSiderCollapsed(true)
}

function handleUpdateCollapsed() {
  appStore.setSiderCollapsed(!collapsed.value)
}

function handlePositiveClick() {
  // 调用微前端退出登录
  logout()
}

function handleNegativeClick() {
  // 取消退出登录
  console.log('取消退出登录')
}





const getMobileClass = computed<CSSProperties>(() => {
  if (isMobile.value) {
    return {
      position: 'fixed',
      zIndex: 50,
    }
  }
  return {}
})

const mobileSafeArea = computed(() => {
  if (isMobile.value) {
    return {
      paddingBottom: 'env(safe-area-inset-bottom)',
    }
  }
  return {}
})

watch(
  isMobile,
  (val) => {
    appStore.setSiderCollapsed(val)
  },
  {
    immediate: true,
    flush: 'post',
  },
)
</script>

<template>
  <NLayoutSider
    :collapsed="collapsed"
    :collapsed-width="0"
    :width="260"
    :show-trigger="isMobile ? false : 'arrow-circle'"
    collapse-mode="transform"
    position="absolute"
    bordered
    :style="getMobileClass"
    @update-collapsed="handleUpdateCollapsed"
    class="!h-screen"
    style="background-color: rgb(247 249 255 / var(--tw-bg-opacity, 1))"
  >
    <div class="flex flex-col h-screen" :style="{...mobileSafeArea, backgroundColor: 'rgb(247 249 255 / var(--tw-bg-opacity, 1))'}">
      <main class="flex flex-col flex-1 min-h-0">
        <div>
          <img :src="logo2" alt="" class="m-auto mt-7 w-[202px] h-[46px] mb-4">
        </div>
        <!-- <div class="pl-4 pr-4">
          <div class="w-full bg-[#E4D4D4] h-[1px] " />
        </div> -->

        <div class="p-4">
          <div class="mt-2 flex items-center h-11 border-solid border-2  rounded-xl font-medium cursor-pointer" @click="handleAdd">
            <img class="w-4 h-4 mr-2 ml-4" :src="jia2" alt="">
            开启新对话
          </div>
        </div>

        <div class="mt-4 px-4 text-sm mb-1">
          7天内
        </div>
        <div class="flex-1 min-h-0 pb-4 overflow-hidden">
          <List />
        </div>
      </main>
      <div class="bg-[#F9FBFF] z-10">
        <div class="loginout">
          <n-popconfirm
            @positive-click="handlePositiveClick"
            @negative-click="handleNegativeClick"
          >
            <template #trigger>
              <img class="closelogin" :src="loginoutIcon">
            </template>
            确定退出登录吗？
          </n-popconfirm>
          <div class="radiobg">
            <img :src="userIcon">
          </div>
        </div>
      </div>
      <Footer />
    </div>
  </NLayoutSider>
  <template v-if="isMobile">
    <div v-show="!collapsed" class="fixed inset-0 z-40 w-full h-full bg-white/90" @click="handleUpdateCollapsed" />
  </template>
  <PromptStore v-model:visible="show" />
</template>

<style scoped lang="less">
:deep(.bug .n-input__input-el){
	 height:2.75rem !important;
}
.loginout{
  width: 142px;
  height: 64px;
  background: #FCFCFC;
  background-image: linear-gradient(114deg, #FDE6FE 0%, #C3F9FF 100%);
  box-shadow: 0 2px 4px 0 #e6e6e680;
  border-radius: 33px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  .radiobg{
    width: 44px;
    height: 44px;
    opacity: 0.8;
    background: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;
    margin-left: 30px;
  }
  img{
    width: 22px;
    height: 24px;
  }
}
.closelogin:hover{
  cursor: pointer;
}
.logotit{
  width: 204px;
  height: 40px;
}
.imgbox{
  height: 40px;
  display: flex;
  align-items: center;
  margin-left: 10px;
}
</style>