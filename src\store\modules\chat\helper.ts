const LOCAL_NAME = 'chatStorage_active'
import {ss} from '@/utils/storage'

export function defaultState(): Chat.ChatState {
	const uuid = null
	return {
		active: uuid,
		newAddHistory: false,
		refreshHistory: false,
		usingContext: true,
		history: [],
		chat: [],
		netFlag: false,
		toolboxInfo: {},
		target: {}
	}
}

export function getLocalState(): Chat.ChatState {
	const active = ss.get(LOCAL_NAME)
	return {...defaultState(), active}
}

export function setLocalState(state: Chat.ChatState) {
	ss.set(LOCAL_NAME, state.active)
}
