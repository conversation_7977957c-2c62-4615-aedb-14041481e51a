<template>
  <div class="aggregation-selector" ref="selectorRef">
    <div
      class="selector-trigger"
      @click="toggleDropdown"
    >
      <span class="selected-text" :class="{ 'has-selection': selectedText }">
        {{ selectedText || placeholder }}
      </span>
      <svg 
        class="dropdown-arrow" 
        :class="{ 'arrow-up': showDropdown }"
        width="12" 
        height="12" 
        viewBox="0 0 12 12"
      >
        <path d="M3 4.5L6 7.5L9 4.5" stroke="currentColor" stroke-width="1.5" fill="none"/>
      </svg>
    </div>
    
    <div 
      v-show="showDropdown" 
      class="selector-dropdown"
    >
      <!-- 空状态提示 -->
      <div v-if="!hasOptions" class="empty-state">
        <div class="empty-icon">📋</div>
        <div class="empty-text">暂无数据，请连接至少一个前序节点</div>
      </div>
      
      <!-- 选项列表 -->
      <div v-else>
        <div 
          v-for="group in options" 
          :key="group.value"
          class="selector-group"
        >
          <div class="group-header">
            <span class="group-title">{{ group.label }}</span>
          </div>
           <div class="group-header">
            <span class="group-type">变量名称</span>
            <span class="group-type">数据类型</span>
          </div>
          <div class="group-variables">
            <div 
              v-for="variable in group.variables" 
              :key="`${variable.id}`"
              class="variable-option"
              :class="[{ 'selected': selectedValue === `${variable.id}` },{ 'disabled': group.disabled }]"
              @click="selectVariable(group, variable)"
            >
              <span class="variable-name">{{ variable.name }}</span>
              <span class="variable-type">{{ variable.type }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 定义组件名称
defineOptions({
  name: 'AggregationSelector'
})

interface Variable {
  name: string
  type: string
  id: string
}

interface OptionGroup {
  label: string
  value: string
  disabled?: boolean
  variables: Variable[]
}

interface Props {
  modelValue?: string
  placeholder?: string
  options: OptionGroup[]
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string, variable: Variable, group: OptionGroup): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择变量'
})

const emit = defineEmits<Emits>()

const showDropdown = ref(false)

const selectedValue = computed({
  get: () => props.modelValue || '',
  set: (value) => emit('update:modelValue', value)
})

const selectedText = computed(() => {
  if (!selectedValue.value) return ''

  for (const group of props.options) {
    for (const variable of group.variables) {
      if (selectedValue.value === `${variable.id}`) {
        return `${variable.name}`
      }
    }
  }
  return ''
})

const hasOptions = computed(() => {
  return props.options.some(group => group.variables && group.variables.length > 0)
})

const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

const selectVariable = (group: OptionGroup, variable: Variable) => {
  if(group.disabled){
    return
  }
  const value = `${variable.id}`
  selectedValue.value = value
  showDropdown.value = false
  emit('change', value, variable, group)
}

// 点击外部关闭下拉框
const selectorRef = ref<HTMLElement>()

const handleClickOutside = (event: Event) => {
  if (!selectorRef.value) return

  const target = event.target as HTMLElement
  // 检查点击的元素是否在组件内部
  if (!selectorRef.value.contains(target)) {
    showDropdown.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside, true)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside, true)
})
</script>

<style scoped>
.aggregation-selector {
  position: relative;
  width: 100%;
  height: 100%;
}

.selector-trigger {
  /* height: 34px; */
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  /* border: 1px solid #d9d9d9; */
  border-radius: 8px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #F5F5F6;
}

.selector-trigger:hover {
  border-color: #125EFF;
}

.selected-text {
  font-size: 14px;
  color: #C7C7C7;
  padding: 2px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  line-height: 16px;
  min-height: 26px;
  display: flex;
  align-items: center;
}

.selected-text.has-selection {
  background: #125EFF;
  color: #FFFFFF;
}

.dropdown-arrow {
  color: #999;
  transition: transform 0.2s ease;
}

.dropdown-arrow.arrow-up {
  transform: rotate(180deg);
}

.selector-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
  margin-top: 4px;
  min-width: 250px;
}

.selector-group {
  /* border-bottom: 1px solid #f0f0f0; */
}

.selector-group:last-child {
  border-bottom: none;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 28px 8px;
}

.group-title {
  font-size: 14px;
  font-weight: 500;
  color: #1f1f1f;
}

.group-type {
  font-size: 12px;
  color: #999;
}

.group-variables {
  padding: 0 16px 8px;
}

.variable-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 4px;
  margin: 2px 0;
}

.variable-option:hover {
  background-color: #f5f5f5;
}

.variable-option.selected {
  background-color: #f8f9fa;
  color: #125EFF;
}

.variable-option.disabled {
  background-color: #FAFAFC;
}

.variable-name {
  font-size: 13px;
  color: #565756;
}

.variable-option.selected .variable-name {
  color: #125EFF;
}

.variable-type {
  font-size: 12px;
  color: #c7c7c7;
}

.variable-option.selected .variable-type {
  color: #125EFF;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.6;
}

.empty-text {
  font-size: 14px;
  color: #999;
  line-height: 1.4;
}
</style>
