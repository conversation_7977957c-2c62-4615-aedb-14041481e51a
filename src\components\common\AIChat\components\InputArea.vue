<template>
  <div class="input-area">
    <!-- 浮动快捷提示按钮 -->
    <div
      v-if="quickPrompts?.length && !hasMessages"
      class="floating-prompts"
      :class="{ 'floating-prompts-top': promptPosition === 'top', 'floating-prompts-bottom': promptPosition === 'bottom' }"
    >
      <button
        v-for="prompt in quickPrompts"
        :key="prompt.id"
        class="quick-prompt-btn"
        @click="selectQuickPrompt(prompt.text)"
      >
        {{ prompt.text }}
      </button>
    </div>

    <!-- 上下文信息 -->
    <div v-if="selectedKnowledgeBase || attachments?.length" class="context-info">
      <div v-if="selectedKnowledgeBase" class="knowledge-base-info">
        <img src="/src/assets/knowledgeFactoryPage/img1.png" alt="知识库" class="context-icon-img" />
        <span class="context-text">{{ selectedKnowledgeBase.name }}</span>
        <button class="context-remove" @click="removeKnowledgeBase">×</button>
      </div>
      <div v-for="attachment in attachments" :key="attachment.id" class="attachment-info">
        <img src="/src/assets/knowledgeFactoryPage/upload.png" alt="附件" class="context-icon-img" />
        <span class="context-text">{{ attachment.name }}</span>
        <button class="context-remove" @click="removeAttachment(attachment.id)">×</button>
      </div>
    </div>

    <!-- 主输入区域 -->
    <div class="main-input-container">
      <!-- 停止生成按钮 -->
      <div v-if="isGenerating" class="stop-generation">
        <button class="stop-btn" @click="handleStopGeneration">
          <span class="stop-icon">⏹</span>
          <span>停止生成</span>
        </button>
      </div>

      <!-- 输入框 -->
      <div v-else class="input-wrapper">
        <!-- 左侧工具按钮 -->
        <div class="input-tools">
          <!-- 默认工具按钮（始终显示） -->
          <button
            v-if="config.features?.fileUpload"
            class="tool-btn"
            @click="triggerFileUpload"
            title="上传文件"
          >
            <img src="/src/assets/uoload.png" alt="上传文件" class="tool-icon" />
          </button>
          <button
            v-if="config.features?.knowledgeBase"
            class="tool-btn"
            @click="showKnowledgeSelector = true"
            title="选择知识库"
          >
            📚
          </button>

          <!-- 额外的自定义工具按钮插槽 -->
          <slot
            name="input-tools"
            :config="config"
            :triggerFileUpload="triggerFileUpload"
            :showKnowledgeSelector="showKnowledgeSelector"
          />
        </div>

        <!-- 文本输入框 -->
        <textarea
          ref="textareaRef"
          v-model="inputText"
          :placeholder="config.placeholders?.input || '请输入您的问题...'"
          class="input-textarea"
          :disabled="isGenerating"
          @keydown="handleKeydown"
          @input="adjustTextareaHeight"
        ></textarea>

        <!-- 右侧按钮 -->
        <div class="input-actions">
          <!-- 语音输入 -->
          <button
            v-if="config.features?.voiceInput"
            class="voice-btn"
            :class="{ 'recording': isRecording }"
            @mousedown="startRecording"
            @mouseup="stopRecording"
            @mouseleave="stopRecording"
            @touchstart="startRecording"
            @touchend="stopRecording"
            title="按住说话"
          >
            <img src="/src/assets/chat/voiced.png" alt="语音输入" class="tool-icon" />
          </button>

          <!-- 发送按钮 -->
          <button
            class="send-btn"
            :disabled="!canSend"
            @click="handleSend"
            title="发送"
          >
            <img v-if="!isGenerating" src="/src/assets/sendicon.png" alt="发送" class="send-icon" />
            <img v-else src="/src/assets/loadingicon.png" alt="发送中" class="send-icon loading-spin" />
          </button>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input 
      ref="fileInputRef"
      type="file" 
      multiple 
      accept="image/*,.pdf,.doc,.docx,.txt"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 知识库选择器 -->
    <div v-if="showKnowledgeSelector" class="knowledge-selector-overlay" @click="showKnowledgeSelector = false">
      <div class="knowledge-selector" @click.stop>
        <h3>选择知识库</h3>
        <div class="knowledge-list">
          <div 
            v-for="kb in knowledgeBases" 
            :key="kb.id" 
            class="knowledge-item"
            @click="selectKnowledgeBase(kb)"
          >
            <span class="knowledge-icon">{{ kb.icon || '📚' }}</span>
            <div class="knowledge-info">
              <div class="knowledge-name">{{ kb.name }}</div>
              <div class="knowledge-desc">{{ kb.description }}</div>
            </div>
          </div>
        </div>
        <button class="close-btn" @click="showKnowledgeSelector = false">关闭</button>
      </div>
    </div>

    <!-- 语音录制提示 -->
    <div v-if="isRecording" class="recording-indicator">
      <div class="recording-animation">
        <div class="recording-circle"></div>
      </div>
      <p>正在录音，松开发送</p>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue'
import type { QuickPrompt, KnowledgeBase, Attachment, AIChatConfig } from '../types'

interface Props {
  config: AIChatConfig
  quickPrompts?: QuickPrompt[]
  knowledgeBases?: KnowledgeBase[]
  selectedKnowledgeBase?: KnowledgeBase | null
  attachments?: Attachment[]
  isGenerating?: boolean
  hasMessages?: boolean
  promptPosition?: 'top' | 'bottom'
}

interface Emits {
  (e: 'send', content: string): void
  (e: 'upload-file', file: File): void
  (e: 'select-knowledge-base', kb: KnowledgeBase): void
  (e: 'remove-knowledge-base'): void
  (e: 'remove-attachment', id: string): void
  (e: 'stop-generation'): void
  (e: 'voice-input', text: string): void
}

const props = withDefaults(defineProps<Props>(), {
  quickPrompts: () => [],
  knowledgeBases: () => [],
  attachments: () => [],
  isGenerating: false,
  hasMessages: false,
  promptPosition: 'top'
})

const emit = defineEmits<Emits>()

// 输入相关
const inputText = ref('')
const textareaRef = ref<HTMLTextAreaElement>()
const fileInputRef = ref<HTMLInputElement>()

// 界面状态
const showKnowledgeSelector = ref(false)
const isRecording = ref(false)

// 计算属性
const canSend = computed(() => {
  return inputText.value.trim().length > 0 && !props.isGenerating
})

// 自动调整输入框高度
const adjustTextareaHeight = () => {
  nextTick(() => {
    if (textareaRef.value) {
      textareaRef.value.style.height = 'auto'
      textareaRef.value.style.height = Math.min(textareaRef.value.scrollHeight, 120) + 'px'
    }
  })
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleSend()
  }
}

// 发送消息
const handleSend = () => {
  if (canSend.value) {
    emit('send', inputText.value.trim())
    inputText.value = ''
    adjustTextareaHeight()
  }
}

// 快捷输入
const selectQuickPrompt = (text: string) => {
  inputText.value = text
  handleSend()
}

// 文件上传
const triggerFileUpload = () => {
  fileInputRef.value?.click()
}

const handleFileSelect = (event: Event) => {
  const files = (event.target as HTMLInputElement).files
  if (files) {
    Array.from(files).forEach(file => {
      emit('upload-file', file)
    })
  }
}

// 知识库选择
const selectKnowledgeBase = (kb: KnowledgeBase) => {
  emit('select-knowledge-base', kb)
  showKnowledgeSelector.value = false
}

const removeKnowledgeBase = () => {
  emit('remove-knowledge-base')
}

// 附件管理
const removeAttachment = (id: string) => {
  emit('remove-attachment', id)
}

// 停止生成
const handleStopGeneration = () => {
  emit('stop-generation')
}

// 语音输入
const startRecording = () => {
  if (props.config.features?.voiceInput) {
    isRecording.value = true
    // 这里应该调用语音识别API
    console.log('开始录音')
  }
}

const stopRecording = () => {
  if (isRecording.value) {
    isRecording.value = false
    // 这里应该停止录音并处理结果
    console.log('停止录音')
    // 模拟语音识别结果
    setTimeout(() => {
      const mockText = '这是语音识别的结果'
      inputText.value = mockText
      emit('voice-input', mockText)
    }, 500)
  }
}

onMounted(() => {
  adjustTextareaHeight()
})
</script>

<style scoped>
.input-area {
  border-top: 1px solid rgba(18, 94, 255, 0.08);
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  padding: 24px 28px;
  backdrop-filter: blur(20px);
  position: relative;
  min-height: 120px;
}

.input-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 28px;
  right: 28px;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(18, 94, 255, 0.1) 50%, transparent 100%);
}

/* 浮动快捷提示容器 */
.floating-prompts {
  position: absolute;
  left: 28px;
  right: 28px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  z-index: 20;
  pointer-events: none;
}

.floating-prompts-top {
  top: -60px;
}

.floating-prompts-bottom {
  bottom: -60px;
}

.floating-prompts .quick-prompt-btn {
  pointer-events: auto;
}

.quick-prompt-btn {
  background: linear-gradient(135deg, rgba(248, 250, 255, 0.95) 0%, rgba(238, 244, 255, 0.95) 100%);
  border: 1px solid rgba(18, 94, 255, 0.15);
  border-radius: 18px;
  padding: 6px 14px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  color: #125EFF;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.01em;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  max-width: 180px;
  text-overflow: ellipsis;
  backdrop-filter: blur(10px);
  box-shadow:
    0 4px 16px rgba(18, 94, 255, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.04);
}

.quick-prompt-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.04) 0%, rgba(18, 94, 255, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.quick-prompt-btn:hover {
  background: linear-gradient(135deg, rgba(238, 244, 255, 0.98) 0%, rgba(219, 234, 254, 0.98) 100%);
  border-color: rgba(18, 94, 255, 0.25);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(18, 94, 255, 0.2),
    0 4px 12px rgba(18, 94, 255, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.06);
}

.quick-prompt-btn:hover::before {
  opacity: 1;
}

.quick-prompt-btn:active {
  transform: translateY(-1px);
  box-shadow:
    0 4px 16px rgba(18, 94, 255, 0.15),
    0 2px 8px rgba(18, 94, 255, 0.08);
}

.context-info {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
}

.knowledge-base-info,
.attachment-info {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0ecff 100%);
  border: 1px solid rgba(18, 94, 255, 0.15);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #125EFF;
  box-shadow: 0 2px 6px rgba(18, 94, 255, 0.1);
}

.context-remove {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #125EFF;
  margin-left: 6px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.context-remove:hover {
  opacity: 1;
}

.main-input-container {
  position: relative;
}

.stop-generation {
  display: flex;
  justify-content: center;
  padding: 16px;
}

.stop-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 28px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.stop-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 16px;
  border: 1px solid rgba(18, 94, 255, 0.12);
  border-radius: 20px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  box-shadow:
    0 4px 16px rgba(18, 94, 255, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.02);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.input-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.02) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: rgba(18, 94, 255, 0.4);
  box-shadow:
    0 0 0 4px rgba(18, 94, 255, 0.12),
    0 8px 24px rgba(18, 94, 255, 0.15),
    0 4px 12px rgba(0, 0, 0, 0.04);
  transform: translateY(-1px);
}

.input-wrapper:focus-within::before {
  opacity: 1;
}

.input-tools {
  display: flex;
  gap: 12px;
  z-index: 1;
}

.tool-btn {
  background: linear-gradient(135deg, #f8faff 0%, #eef4ff 100%);
  border: 1px solid rgba(18, 94, 255, 0.15);
  cursor: pointer;
  font-size: 18px;
  padding: 12px;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #125EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  position: relative;
  overflow: hidden;
}

.tool-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.05) 0%, rgba(18, 94, 255, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tool-btn:hover {
  background: linear-gradient(135deg, #eef4ff 0%, #dbeafe 100%);
  border-color: rgba(18, 94, 255, 0.25);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(18, 94, 255, 0.2),
    0 4px 12px rgba(18, 94, 255, 0.1);
}

.tool-btn:hover::before {
  opacity: 1;
}

.tool-btn:hover .tool-icon {
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(120%) contrast(101%);
  transform: scale(1.15);
}

.input-textarea {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  font-size: 15px;
  line-height: 1.6;
  min-height: 24px;
  max-height: 120px;
  padding: 10px 12px;
  color: #1f2937;
  background: transparent;
  position: relative;
  z-index: 10;
}

.input-textarea::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.input-actions {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.voice-btn {
  background: linear-gradient(135deg, #f8faff 0%, #eef4ff 100%);
  border: 1px solid rgba(18, 94, 255, 0.12);
  border-radius: 12px;
  padding: 10px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #125EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.voice-btn:hover {
  background: linear-gradient(135deg, #eef4ff 0%, #dbeafe 100%);
  border-color: rgba(18, 94, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(18, 94, 255, 0.15);
}

.voice-btn:hover .tool-icon {
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(120%) contrast(101%);
  transform: scale(1.1);
}

.voice-btn.recording {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3), 0 0 0 0 rgba(239, 68, 68, 0.4);
  }
  50% {
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3), 0 0 0 8px rgba(239, 68, 68, 0);
  }
}

.send-btn {
  background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
  color: white;
  border: none;
  border-radius: 16px;
  padding: 12px 20px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 52px;
  height: 44px;
  box-shadow:
    0 8px 24px rgba(18, 94, 255, 0.3),
    0 4px 12px rgba(18, 94, 255, 0.2);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.send-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.send-btn:disabled {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.send-btn:not(:disabled):hover {
  background: linear-gradient(135deg, #1e7fff 0%, #3b82f6 100%);
  transform: translateY(-2px);
  box-shadow:
    0 12px 32px rgba(18, 94, 255, 0.4),
    0 6px 16px rgba(18, 94, 255, 0.3);
}

.send-btn:not(:disabled):hover::before {
  opacity: 1;
}

.send-btn:active {
  transform: translateY(-1px);
  box-shadow:
    0 8px 24px rgba(18, 94, 255, 0.3),
    0 4px 12px rgba(18, 94, 255, 0.2);
}

/* 图标样式 */
.context-icon-img {
  width: 16px;
  height: 16px;
  object-fit: contain;
  margin-right: 4px;
}

.tool-icon {
  width: 18px;
  height: 18px;
  object-fit: contain;
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(97%) contrast(101%);
  transition: all 0.3s ease;
}

.send-icon {
  width: 18px;
  height: 18px;
  object-fit: contain;
  filter: brightness(0) saturate(100%) invert(100%);
}

.loading-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.knowledge-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.knowledge-selector {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.knowledge-list {
  margin: 16px 0;
}

.knowledge-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s;
}

.knowledge-item:hover {
  background: #f8f9fa;
}

.knowledge-info {
  flex: 1;
}

.knowledge-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.knowledge-desc {
  font-size: 12px;
  color: #6c757d;
}

.close-btn {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  width: 100%;
}

.recording-indicator {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 24px;
  border-radius: 12px;
  text-align: center;
  z-index: 1001;
}

.recording-animation {
  margin-bottom: 12px;
}

.recording-circle {
  width: 40px;
  height: 40px;
  background: #dc3545;
  border-radius: 50%;
  margin: 0 auto;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .input-area {
    padding: 16px;
    border-radius: 0;
  }

  .quick-prompts {
    gap: 8px;
    margin-bottom: 12px;
  }

  .quick-prompt-btn {
    padding: 8px 12px;
    font-size: 13px;
    border-radius: 16px;
    min-height: 32px;
  }

  .input-container {
    gap: 8px;
  }

  .input-wrapper {
    border-radius: 20px;
    padding: 12px 16px;
  }

  .input-textarea {
    font-size: 14px;
    line-height: 1.4;
    min-height: 20px;
    max-height: 120px;
  }

  .input-actions {
    gap: 6px;
  }

  .input-btn {
    width: 36px;
    height: 36px;
    min-width: 36px;
    border-radius: 18px;
    padding: 8px;
  }

  .input-icon {
    width: 18px;
    height: 18px;
  }

  .send-btn {
    width: 40px;
    height: 40px;
    min-width: 40px;
    border-radius: 20px;
  }

  .send-icon {
    width: 20px;
    height: 20px;
  }

  .file-upload-area {
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 12px;
  }

  .upload-text {
    font-size: 14px;
  }

  .upload-hint {
    font-size: 12px;
  }

  .file-list {
    gap: 8px;
    margin-top: 12px;
  }

  .file-item {
    padding: 8px 12px;
    border-radius: 8px;
  }

  .file-info {
    gap: 8px;
  }

  .file-icon {
    width: 16px;
    height: 16px;
  }

  .file-name {
    font-size: 13px;
  }

  .file-size {
    font-size: 11px;
  }

  .file-remove {
    width: 20px;
    height: 20px;
    min-width: 20px;
  }

  .knowledge-base-selector {
    margin-bottom: 12px;
  }

  .kb-select {
    padding: 10px 12px;
    font-size: 14px;
    border-radius: 12px;
  }

  .voice-recording {
    padding: 16px;
    border-radius: 12px;
    margin-bottom: 12px;
  }

  .recording-text {
    font-size: 14px;
  }

  .recording-time {
    font-size: 16px;
    margin: 8px 0;
  }

  .recording-actions {
    gap: 12px;
    margin-top: 12px;
  }

  .recording-btn {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 8px;
  }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
  .input-area {
    padding: 12px;
  }

  .quick-prompts {
    gap: 6px;
    margin-bottom: 10px;
  }

  .quick-prompt-btn {
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 14px;
    min-height: 28px;
  }

  .input-container {
    gap: 6px;
  }

  .input-wrapper {
    border-radius: 18px;
    padding: 10px 14px;
  }

  .input-textarea {
    font-size: 13px;
    min-height: 18px;
    max-height: 100px;
  }

  .input-actions {
    gap: 4px;
  }

  .input-btn {
    width: 32px;
    height: 32px;
    min-width: 32px;
    border-radius: 16px;
    padding: 6px;
  }

  .input-icon {
    width: 16px;
    height: 16px;
  }

  .send-btn {
    width: 36px;
    height: 36px;
    min-width: 36px;
    border-radius: 18px;
  }

  .send-icon {
    width: 18px;
    height: 18px;
  }

  .file-upload-area {
    padding: 16px;
    border-radius: 10px;
    margin-bottom: 10px;
  }

  .upload-text {
    font-size: 13px;
  }

  .upload-hint {
    font-size: 11px;
  }

  .file-list {
    gap: 6px;
    margin-top: 10px;
  }

  .file-item {
    padding: 6px 10px;
    border-radius: 6px;
  }

  .file-info {
    gap: 6px;
  }

  .file-icon {
    width: 14px;
    height: 14px;
  }

  .file-name {
    font-size: 12px;
  }

  .file-size {
    font-size: 10px;
  }

  .file-remove {
    width: 18px;
    height: 18px;
    min-width: 18px;
  }

  .kb-select {
    padding: 8px 10px;
    font-size: 13px;
    border-radius: 10px;
  }

  .voice-recording {
    padding: 12px;
    border-radius: 10px;
    margin-bottom: 10px;
  }

  .recording-text {
    font-size: 13px;
  }

  .recording-time {
    font-size: 14px;
    margin: 6px 0;
  }

  .recording-actions {
    gap: 8px;
    margin-top: 10px;
  }

  .recording-btn {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .input-btn,
  .send-btn,
  .quick-prompt-btn,
  .file-remove,
  .recording-btn {
    min-height: 44px; /* iOS推荐的最小触摸目标 */
  }

  .input-btn:active,
  .send-btn:active,
  .quick-prompt-btn:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  .input-textarea {
    -webkit-appearance: none;
    appearance: none;
    border-radius: 20px;
  }

  /* 防止iOS缩放 */
  .input-textarea,
  .kb-select {
    font-size: 16px !important;
  }
}
</style>
