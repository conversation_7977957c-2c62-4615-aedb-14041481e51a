<template>
  <div>
    <n-form-item label-placement="left" class="setHeight mb-[9px]">
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span> 回复内容
        </div>
      </template>
    </n-form-item>
    <n-form-item path="config.huifuneirong" class="seteditable">
      <div class="relative w-full">
        <div
          ref="editableDiv"
          contenteditable="true"
          class="editable-div"
          @keydown="handleKeydown"
          @input="handleInput"
        ></div>
        <button
          class="add-variable-btn"
          @click="showVariableSelector = true"
          type="button"
        >
          + 添加变量
        </button>
      </div>
    </n-form-item>

    <n-form-item
      path="config.wentijianyi"
      label-placement="left"
      class="setHeight mt-[24px] mb-[17px]"
    >
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span> 问题建议
        </div>
      </template>
      <div class="flex justify-end w-full">
        <n-switch
          v-model:value="formData.config.wentijianyi"
          @update:value="handleChangewentijianyi"
        />
      </div>
    </n-form-item>
    <div v-if="formData.config.wentijianyi">
      <n-form-item
        path="config.yindaoci"
        class="setrowbottom mb-[16px]"
      >
        <template #label>
          <div class="rowstit">引导词</div>
        </template>
        <n-input
          v-model:value="formData.config.yindaoci"
          type="text"
          placeholder="请输入引导词"
          maxlength="20"
          show-count
          default-value="试试这样问"
        >
        </n-input>
      </n-form-item>

      <n-form-item path="config.jianyitype" class="setrowbottom">
        <template #label>
          <div class="rowstit">问题建议</div>
        </template>
        <NRadioGroup
          v-model:value="formData.config.jianyitype"
          name="radiogroup"
          default-checked="0"
        >
          <div class="flex items-center">
            <div
              @click="changejianyitypefun('0')"
              class="w-[236px] h-[38px] bg-[#F5F5F6] flex items-center pl-[12px] rounded-lg mr-[16px]"
            >
              <NRadio key="0" value="0"> 默认 </NRadio>
            </div>
            <div
              @click="changejianyitypefun('1')"
              class="w-[236px] h-[38px] bg-[#F5F5F6] flex items-center pl-[12px] rounded-lg"
            >
              <NRadio key="1" value="1"> 自定义 </NRadio>
            </div>
          </div>
        </NRadioGroup>
      </n-form-item>
      <div v-if="formData.config.jianyitype == '1'">
        <div class="mb-[8px]">
          <n-form-item
            v-for="(item, index) in formData.config.jianyiwenti"
            :key="index"
            label-placement="left"
            class="mt-[12px]"
          >
            <div class="flex items-center h-[38px]">
              <div class="w-[114px] mr-[14px] h-full">
                <NSelect
                  v-model:value="item.type"
                  :options="variableOptions"
                  filterable
                />
              </div>
              <div class="w-[336px] mr-[6px] h-full">
                <AggregationSelector
                  v-if="item.type == '0'"
                  v-model="item.value"
                  :options="aggregationOptions"
                  placeholder="请选择变量"
                  @change="handleAggregationChange"
                />

                <NInput
                  type="text"
                  placeholder="请输入"
                  maxlength="20"
                  show-count
                  v-if="item.type == '1'"
                  v-model:value="item.value"
                ></NInput>
              </div>
              <img
                @click="delproblemfun(index)"
                class="w-[16px] h-[16px] cursor-pointer"
                src="@/assets/agentOrchestration/delIcon2.png"
              />
            </div>
          </n-form-item>
        </div>

        <div class="w-[488px] btnparent mt-[12px]">
          <NButton @click="addproblemfun" dashed>
            <img
              class="w-[9px] mr-[6px]"
              src="@/assets/agentOrchestration/addIcon.png"
            />
            添加问题建议
          </NButton>
        </div>
      </div>
    </div>
  </div>
    <!-- 变量选择器弹窗 -->
  <n-modal v-model:show="showVariableSelector">
    <n-card style="width: 600px" title="选择变量" :bordered="false" size="huge">
      <div class="variable-selector-content">
        <div class="selector-description">
          选择要插入到提示词中的变量，变量将以
          <code>{{ 变量名 }}</code> 的格式插入。
        </div>
            <AggregationSelector
             v-model="selectedVariable"
                  :options="aggregationOptions"
                  placeholder="请选择变量"
                  @change="handleVariableSelect"
                />
        <div v-if="selectedVariableForInsert" class="insert-preview">
          <div class="preview-title">插入预览：</div>
          <div class="preview-content">
            <code>{{ selectedVariableForInsert }}</code>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="modal-footer">
          <n-button @click="closeVariableSelector">取消</n-button>
          <n-button
            type="primary"
            @click="insertVariableToPrompt"
            :disabled="!selectedVariableForInsert"
          >
            插入变量
          </n-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref,nextTick,onMounted } from 'vue';
import {
  NFormItem,
  NInput,
  NSelect,
  NSwitch,
  NButton,
  NScrollbar,
  useMessage,
  NPopover,
  NDataTable,
  NCard,
  NRadio,
  NRadioGroup,
  NModal,
  SelectGroupOption,
  SelectOption,
} from "naive-ui";
import AggregationSelector from '../AggregationSelector.vue';
import VariableSelector from "../VariableSelector.vue";
import { useOrchestrationStore } from "@/store";

// 修复：将defineProps的结果赋值给props变量
const props = defineProps({
  formData: {
    type: Object,
    required: true,
  },
  node: {
    type: Object,
    required: true,
  },
  variableOptions: {
    type: Array,
    required: true,
  },
  aggregationOptions: {
    type: Array,
    required: true,
  },
});
onMounted(()=>{
  if(props.formData.config.huifuneirong){
    rendereditableDiv(props.formData.config.huifuneirong);
  }
})
// 修复：将defineEmits的结果赋值给emit变量
const emit = defineEmits(['open-variable-management', 'update:formData']);
const message = useMessage();
const orchestrationStore = useOrchestrationStore();

const selectedVariableForInsert = ref("");
const selectedVariable = ref("");
const selectedVariableObj = ref({});


const editableDiv = ref<HTMLDivElement | null>(null);
// 记录光标位置的变量
const savedRange = ref<Range | null>(null);
const showVariableSelector = ref(false);

const changeEditableDiv=()=>{
  // 创建临时div元素，不影响原始页面显示
  const tempDiv = document.createElement('div');
  
  // 复制原始editableDiv的内容到临时div
  if (editableDiv.value) {
    tempDiv.innerHTML = editableDiv.value.innerHTML;
    
    // 在临时div中查找并处理按钮元素
    const buttons = tempDiv.querySelectorAll('div[contenteditable="false"]');
    
    // 遍历按钮并处理
    buttons.forEach(btn => {
      const btnContent = btn.dataset.id;
      // 创建文本节点，将按钮内容用{{}}包含
      const textNode = document.createTextNode(`{{${btnContent}}}`);
      // 在临时div中替换按钮元素
      btn.parentNode?.replaceChild(textNode, btn);
    });
    
    // 获取处理后的完整内容
    const processedContent = tempDiv.innerHTML;
    console.log(processedContent);
    console.log(props.node.id);
    
     const variables = orchestrationStore.getVariablesByNodeId(props.node.id)[0] || [];
    orchestrationStore.updateVariable(variables.id, {
        name: variables.name,
        code: variables.code,
        valueType: variables.valueType,
        value:processedContent
      })

      emit('update:formData', {
    ...props.formData,
    config: {
      ...props.formData.config,
      huifuneirong: processedContent,
    },
  });
    // 如果需要返回处理后的内容，可以添加return语句
    // return processedContent;
  }
}
const closeVariableSelector=()=>{
  selectedVariable.value = "";
  showVariableSelector.value = false;
  selectedVariableForInsert.value = "";
  selectedVariableObj.value = {};
}
// 处理变量选择
const handleVariableSelect = (variable: any) => {
  console.log("选中变量:", variable);
  let variableobj=orchestrationStore.getVariableById(variable);
  selectedVariableForInsert.value=variableobj.name;
  selectedVariableObj.value = variableobj;
};
// 插入变量到提示词
const insertVariableToPrompt =async () => {
  if (!selectedVariableForInsert.value) return;
  const variableSyntax = selectedVariableForInsert.value;

  // 根据当前节点类型插入到对应的字段
    nextTick(() => {
      focusEditableDiv();
      // 调用insertVariable函数，传入选中的变量名,变量id
      insertVariable(variableSyntax,selectedVariableObj.value.id);
      
  // 关闭弹窗并清空选择
  showVariableSelector.value = false;
  selectedVariable.value = "";
  selectedVariableForInsert.value = "";
  selectedVariableObj.value = {};

  message.success("变量已插入");
    });

};


function handleKeydown(e: KeyboardEvent) {
  console.log(e.key);
  // contenteditable 默认支持删除，无需特殊处理
}

// 处理输入事件，检测{}
function handleInput(e: Event) {
  const target = e.target as HTMLElement;
  const text = target.textContent || "";

  // 检查是否包含{}
  if (text.includes("{}")) {
    // 延迟执行，确保DOM更新完成
    nextTick(() => {
      // 获取当前光标位置
      const selection = window.getSelection();
      if (!selection || !selection.rangeCount) return;

      const range = selection.getRangeAt(0);
      const startOffset = range.startOffset;

      // 只删除光标位置附近的{}，而不是整个文本内容
      const textNode = range.startContainer;
      if (textNode.nodeType === Node.TEXT_NODE) {
        const textContent = textNode.textContent || "";
        const beforeCursor = textContent.substring(0, startOffset);
        const afterCursor = textContent.substring(startOffset);

        // 检查光标前后是否有{}
        let newBeforeCursor = beforeCursor;
        let newAfterCursor = afterCursor;
        let hasReplacement = false;

        // 检查光标前是否有{}
        if (beforeCursor.endsWith("{}")) {
          newBeforeCursor = beforeCursor.slice(0, -2);
          hasReplacement = true;
        }

        // 检查光标后是否有{}
        if (afterCursor.startsWith("{}")) {
          newAfterCursor = afterCursor.slice(2);
          hasReplacement = true;
        }

        // 检查光标位置是否有{}
        if (beforeCursor.endsWith("{") && afterCursor.startsWith("}")) {
          newBeforeCursor = beforeCursor.slice(0, -1);
          newAfterCursor = afterCursor.slice(1);
          hasReplacement = true;
        }

        if (hasReplacement) {
          // 更新文本节点内容
          textNode.textContent = newBeforeCursor + newAfterCursor;

          // 重新设置光标位置
          const newRange = document.createRange();
          newRange.setStart(textNode, newBeforeCursor.length);
          newRange.collapse(true);
          selection.removeAllRanges();
          selection.addRange(newRange);

          // 保存当前光标位置，用于后续插入变量
          saveCursorPosition();

          // 打开变量选择器
          showVariableSelector.value = true;
        }
      }
    });
  }else{
    changeEditableDiv();
  }
}
function rendereditableDiv(content: string) {
  if (!content || !editableDiv.value) return;
  
  // 清空editableDiv
  editableDiv.value.innerHTML = '';
  focusEditableDiv();
  
  // 创建文档片段，用于暂存所有元素，确保顺序正确
  const fragment = document.createDocumentFragment();
  
  // 处理{{}}包裹的内容
  const regex = /\{\{([^{}]+)\}\}/g;
  let lastIndex = 0;
  let match;
  
  while ((match = regex.exec(content)) !== null) {
    // 添加{{}}前面的文本
    if (match.index > lastIndex) {
      const textNode = document.createTextNode(content.substring(lastIndex, match.index));
      fragment.appendChild(textNode);
    }
    
    // 添加{{}}内容作为div
    const variableId = match[1].trim();
    let variableobj = orchestrationStore.getVariableById(variableId);
    // 创建变量div但不直接插入，而是添加到文档片段
    const variableDiv = createVariableDiv(variableobj?.name,variableId);
    fragment.appendChild(variableDiv);
    
    lastIndex = match.index + match[0].length;
  }
  
  // 添加剩余文本
  if (lastIndex < content.length) {
    const textNode = document.createTextNode(content.substring(lastIndex));
    fragment.appendChild(textNode);
  }
  
  // 一次性将所有元素添加到editableDiv，确保顺序正确
  editableDiv.value.appendChild(fragment);
      // 添加取消光标逻辑：移除焦点
  if (editableDiv.value) {
    editableDiv.value.blur();
  }
}
// 保存光标位置
function saveCursorPosition() {
  const selection = window.getSelection();
  if (selection && selection.rangeCount > 0) {
    savedRange.value = selection.getRangeAt(0).cloneRange();
  }
}
// 获取焦点的辅助函数
function focusEditableDiv() {
  if (editableDiv.value) {
    editableDiv.value.focus();
    // 确保光标在内容末尾
    const selection = window.getSelection();
    const range = document.createRange();
    range.selectNodeContents(editableDiv.value);
    range.collapse(false); // false表示光标在末尾
    selection?.removeAllRanges();
    selection?.addRange(range);
  }
}
// 新增：创建变量div的辅助函数
function createVariableDiv(variableName?: string,variableId?:string) {
  // 创建一个div元素
  const btn = document.createElement("div");
  btn.innerText = variableName || "变量名称"; // 使用传入的变量名或默认值
  btn.setAttribute("contenteditable", "false");
  btn.setAttribute("data-id",variableId);

  // 参考图片优化样式
  btn.style.display = "inline-block";
  btn.style.padding = "2px 14px";
  btn.style.margin = "0 4px";
  btn.style.background = "#125EFF";
  btn.style.border = "none";
  btn.style.color = "#fff";
  btn.style.borderRadius = "8px";
  btn.style.fontSize = "15px";
  btn.style.fontWeight = "500";
  btn.style.cursor = "pointer";
  btn.style.outline = "none";
  btn.style.lineHeight = "22px";
  btn.style.height = "28px";
  btn.style.boxShadow = "none";
  btn.style.transition = "background 0.2s";

  // 悬浮时略微加深
  btn.onmouseenter = () => {
    btn.style.background = "#0d47a1";
  };
  btn.onmouseleave = () => {
    btn.style.background = "#125EFF";
  };
  
  return btn;
}
function insertVariable(variableName?: string,variableId?:string) {
  const el = editableDiv.value;
  if (!el) return;
  // 使用辅助函数创建变量div
  const btn = createVariableDiv(variableName,variableId);

  // 悬浮时略微加深
  btn.onmouseenter = () => {
    btn.style.background = "#0d47a1";
  };
  btn.onmouseleave = () => {
    btn.style.background = "#125EFF";
  };

  // 如果有保存的光标位置，使用保存的位置；否则使用当前光标位置
  if (savedRange.value) {
    insertNodeAtSavedPosition(btn);
  } else {
    insertNodeAtCursor(btn);
  }
  changeEditableDiv()
}

function insertNodeAtSavedPosition(node: Node) {
  if (!savedRange.value) return;

  // 恢复保存的光标位置
  const selection = window.getSelection();
  if (selection) {
    selection.removeAllRanges();
    selection.addRange(savedRange.value);
  }

  // 在保存的位置插入节点
  savedRange.value.insertNode(node);

  // 将光标移到插入的节点后面
  savedRange.value.setStartAfter(node);
  savedRange.value.collapse(true);

  // 更新选择
  if (selection) {
    selection.removeAllRanges();
    selection.addRange(savedRange.value);
  }

  // 清空保存的位置
  savedRange.value = null;

  // 让 contenteditable div 保持 focus
  if (editableDiv.value) editableDiv.value.focus();
  // 某些浏览器下异步 focus 更稳妥
  setTimeout(() => {
    editableDiv.value && editableDiv.value.focus();
  }, 0);
}

function insertNodeAtCursor(node: Node) {
  let sel = window.getSelection();
  if (!sel || !sel.rangeCount) return;
  let range = sel.getRangeAt(0);
  range.collapse(false);
  range.insertNode(node);
  // 关键：将光标移到按钮后面
  range.setStartAfter(node);
  range.collapse(true);
  sel.removeAllRanges();
  sel.addRange(range);
  // 让 contenteditable div 保持 focus
  if (editableDiv.value) editableDiv.value.focus();
  // 某些浏览器下异步 focus 更稳妥
  setTimeout(() => {
    editableDiv.value && editableDiv.value.focus();
  }, 0);
}
const handleChangewentijianyi = (value: any) => {
  emit('update:formData', {
    ...props.formData,
    config: {
      ...props.formData.config,
      wentijianyi: value,
      jianyitype: value ? '0' : props.formData.config.jianyitype,
    },
  });
};

const changejianyitypefun = (type: string) => {
  emit('update:formData', {
    ...props.formData,
    config: {
      ...props.formData.config,
      jianyitype: type,
    },
  });
};

const delproblemfun = (index: number) => {
  if (props.formData.config.jianyiwenti) {
    const newJianyiwenti = [...props.formData.config.jianyiwenti];
    newJianyiwenti.splice(index, 1);
    emit('update:formData', {
      ...props.formData,
      config: {
        ...props.formData.config,
        jianyiwenti: newJianyiwenti,
      },
    });
  }
};

const addproblemfun = () => {
  const newJianyiwenti = props.formData.config.jianyiwenti
    ? [...props.formData.config.jianyiwenti]
    : [];
  newJianyiwenti.push({
    type: '0',
    value: '',
  });
  emit('update:formData', {
    ...props.formData,
    config: {
      ...props.formData.config,
      jianyiwenti: newJianyiwenti,
    },
  });
};

const handleAggregationChange = (value: any) => {
  // 处理聚合选择器变化
};
</script>
<style scoped lang="less">
@import './nodeConfigStyle.less';
</style>
