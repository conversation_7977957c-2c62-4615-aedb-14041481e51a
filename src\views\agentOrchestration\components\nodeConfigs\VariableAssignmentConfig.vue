<template>
  <div class="mt-[20px]">
    <n-form-item label-placement="left" class="setHeight mb-[17px]">
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span>变量赋值
        </div>
      </template>
    </n-form-item>
    <div class="flex items-center mb-[12px] text-[#565756]">
      <div class="w-[114px]">变量名</div>
      <div class="w-[114px] ml-[12px] mr-[12px]">操作</div>
      <div class="w-[212px] mr-[6px]">变量值</div>
    </div>
    <div
      class="flex items-center mb-[12px] text-[#565756]"
      v-for="(item, index) in formData.config.items"
      :key="index"
    >
      <div class="w-[114px] h-[38px] outputrow">
        <AggregationSelector
          v-model="item.targetKey"
          :options="aggregationOptions"
          placeholder="变量名"
        @change="(value, variable, group) => changeitemsfun(value, variable, group, index)"
        />
        <!-- <n-input
          v-model:value="item.key"
          type="text"
          placeholder="变量名"
          @update:value="updateFormData"
        >
        </n-input> -->
      </div>
      <div class="w-[114px] ml-[12px] mr-[12px] h-[38px]">
        <n-select
          v-model:value="item.writeMode"
          :options="operateOptions"
          default-value="0"
          filterable
          @update:value="() => handleOperateChange(item, index)"
        />
      </div>
      <div class="w-[212px] mr-[6px] h-[38px] outputrow">
        <AggregationSelector
          v-if="item.writeMode == '0'"
          v-model="item.input"
          :options="aggregationOptions"
          placeholder="请选择变量"
          @change="handleAggregationChange"
        />
        <n-input
          v-else
          :disabled="item.writeMode == '2'"
          v-model:value="item.input"
          type="text"
          placeholder="变量值"
          @update:value="updateFormData"
        >
        </n-input>
      </div>
      <img
        @click="delvariablefun(index)"
        class="w-[16px] h-[16px] cursor-pointer"
        src="@/assets/agentOrchestration/delIcon2.png"
      />
    </div>
    <div class="w-[488px] mt-[12px] btnparent">
      <NButton @click="addvariablefun" dashed> + 添加变量 </NButton>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NSwitch,
  NSlider,
  NButton,
  NInputNumber,
  NScrollbar,
  NDropdown,
  NDynamicInput,
  useMessage,
  useDialog,
  NPopover,
  NDataTable,
  NCard,
  NDivider,
  NRadio,
  NRadioGroup,
  NModal,
  NCheckbox,
  NCheckboxGroup,
  SelectGroupOption,
  SelectOption,
} from "naive-ui";
import AggregationSelector from '../AggregationSelector.vue';

interface VariableItem {
  targetKey: string;
  input: string;
  writeMode: string;
}

interface FormData {
  config: {
    items?: VariableItem[];
    [targetKey: string]: any;
  };
  [targetKey: string]: any;
}

interface Props {
  formData: FormData;
  variableOptions: Array<{ label: string; value: string }>;
  aggregationOptions: Array<any>;
}

const props = withDefaults(defineProps<Props>(), {
  node: {
    type: Object,
    required: true,
  },
  variableOptions: () => [],
  aggregationOptions: () => []
});

const emit = defineEmits<{
  'update:formData': [value: FormData];
  'handleAggregationChange': [value: string, variable: any, group: any];
}>();

// 操作选项
const operateOptions = [
  { label: "覆盖", value: "0" },
  { label: "设置", value: "1" },
  { label: "清空", value: "2" },
];

// 添加变量
const addvariablefun = () => {
  const newFormData = { ...props.formData };
  if (!newFormData.config.items) {
    newFormData.config.items = [];
  }
  newFormData.config.items.push({
    targetKey: "",
    input: "",
    writeMode: "",
  });
  emit('update:formData', newFormData);
};

// 删除变量
const delvariablefun = (index: number) => {
  const newFormData = { ...props.formData };
  if (newFormData.config.items) {
    newFormData.config.items.splice(index, 1);
    emit('update:formData', newFormData);
  }
};

// 处理操作变更
const handleOperateChange = (item: VariableItem, index: number) => {
  const newFormData = { ...props.formData };
  if (newFormData.config.items) {
    newFormData.config.items[index].input = "";
  }
  emit('update:formData', newFormData);
};

// 更新表单数据
const updateFormData = () => {
  emit('update:formData', props.formData);
};
const changeitemsfun = (value: string, variable: any, group: any,index: number) => {

  const newFormData = { ...props.formData };
    newFormData.config.items[index].input_type = variable.valueType;
  emit('update:formData', newFormData);
}
// 处理聚合变量变更
const handleAggregationChange = (value: string, variable: any, group: any) => {
  console.log(value, variable, group);
  
  // emit('handleAggregationChange', value, variable, group);
};
</script>

<style scoped>
@import './nodeConfigStyle.less';
</style>