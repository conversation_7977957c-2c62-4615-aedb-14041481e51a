<template>
	<div
		v-if="visible"
		:style="tooltipStyle"
		class="annotation-tooltip fixed z-50 bg-white border border-gray-200 rounded-lg shadow-xl w-[384px]"
		@click.stop
	>
		<!-- 箭头指示器 -->
		<div
			:class="[
				'absolute w-3 h-3 bg-white border transform rotate-45',
				arrowPosition === 'top'
					? 'border-b-0 border-r-0 -top-1.5 left-1/2 -translate-x-1/2'
					: arrowPosition === 'bottom'
					? 'border-t-0 border-l-0 -bottom-1.5 left-1/2 -translate-x-1/2'
					: arrowPosition === 'left'
					? 'border-t-0 border-r-0 -left-1.5 top-1/2 -translate-y-1/2'
					: 'border-b-0 border-l-0 -right-1.5 top-1/2 -translate-y-1/2',
			]"
		></div>

		<!-- 头部 -->
		<div class="flex items-center justify-between p-3 border-b border-gray-100">
			<div class="flex items-center gap-2">
				<div
					:style="{ backgroundColor: annotation?.color || '#FDE047' }"
					class="w-3 h-3 rounded-full"
				></div>
				<span class="text-sm font-medium text-gray-700">疑难点标记</span>
			</div>
			<div class="flex items-center gap-2">
				<!-- 编辑按钮 -->
				<button
					v-if="!annotation?.readonly && !isEditing"
					class="text-gray-400 hover:text-blue-600 transition-colors p-1 rounded"
					title="编辑标注"
					@click="startEditing"
				>
					<svg class="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
						<path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
					</svg>
				</button>
				<!-- 删除按钮 -->
				<n-popconfirm @positive-click="deleteAnnotation">
					<template #trigger>
						<button
							v-if="!annotation?.readonly"
							class="text-gray-400 hover:text-red-600 transition-colors p-1 rounded"
							title="删除标注"
						>
							<svg class="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
								<path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
							</svg>
						</button>
					</template>
					确定要删除这个标注吗？
				</n-popconfirm>
				<!-- 关闭按钮 -->
				<button
					class="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded"
					title="关闭"
					@click="close"
				>
					<svg
						class="w-4 h-4"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							d="M6 18L18 6M6 6l12 12"
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
						/>
					</svg>
				</button>
			</div>
		</div>

		<!-- 内容 -->
		<div class="p-3">
			<!-- 原始文本 -->
			<div class="mb-3">
				<label class="text-xs font-medium text-gray-500 uppercase tracking-wide"
					>选中文本</label
				>
				<div
					class="mt-1 p-2 bg-gray-50 rounded text-sm text-gray-700 border-l-3 border-blue-400 max-h-[78px] overflow-y-scroll"
				>
					"{{ annotation?.text || "" }}"
				</div>
			</div>

			<!-- 注释内容 -->
			<div class="mb-3">
				<label class="text-xs font-medium text-gray-500 uppercase tracking-wide"
					>疑难问题</label
				>
				<div v-if="!isEditing" class="mt-1">
					<div
						class="text-sm text-gray-800 leading-relaxed max-h-[78px] overflow-y-scroll"
					>
						{{ annotation?.note || "暂无疑难问题" }}
					</div>
					<div
						v-if="annotation?.readonly"
						class="mt-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded"
					>
						📌 这是只读标注，无法编辑
					</div>
					<!-- <button
						v-else-if="annotation?.note"
						class="mt-2 text-xs text-blue-600 hover:text-blue-800 transition-colors"
						@click="startEditing"
					>
						编辑注释
					</button> -->
				</div>
				<div v-else class="mt-1">
					<textarea
						ref="editTextarea"
						v-model="editingNote"
						class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-all duration-200"
						placeholder="请描述你的疑难问题..."
						rows="4"
						@keydown.esc="cancelEditing"
						@keydown.ctrl.enter="saveEdit"
					></textarea>
					<div class="flex justify-between items-center mt-2">
						<p class="text-xs text-gray-500">
							<!-- 支持 Ctrl+Enter 快速保存，ESC 取消 -->
						</p>
						<p class="text-xs text-gray-400">{{ editingNote.length }}/500</p>
					</div>
					<div class="flex gap-3 mt-3">
						<button
							class="flex-1 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
							@click="cancelEditing"
						>
							取消
						</button>
						<button
							:class="[
								'flex-1 px-4 py-2 text-white rounded-lg transition-all duration-200',
								editingNote.trim()
									? 'bg-blue-600 hover:bg-blue-700 hover:shadow-md'
									: 'bg-gray-400 cursor-not-allowed',
							]"
							:disabled="!editingNote.trim()"
							@click="saveEdit"
						>
							<span class="flex items-center justify-center gap-2">
								<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
									<path
										clip-rule="evenodd"
										d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
										fill-rule="evenodd"
									/>
								</svg>
								保存
							</span>
						</button>
					</div>
				</div>
			</div>

			<!-- 元信息 -->
			<!-- <div class="text-xs text-gray-500 space-y-1">
				<div class="flex justify-between">
					<span>页码:</span>
					<span>第 {{ annotation?.page || 0 }} 页</span>
				</div>
				<div class="flex justify-between">
					<span>创建时间:</span>
					<span>{{ annotation?.createdAt }}</span>
				</div>
				<div
					v-if="
            annotation?.updatedAt &&
            annotation.updatedAt !== annotation.createdAt
          "
					class="flex justify-between"
				>
					<span>更新时间:</span>
					<span>{{ annotation?.updatedAt }}</span>
				</div>
			</div> -->
		</div>



		<!-- 只读标注的底部 -->
		<div
			v-if="annotation?.readonly"
			class="p-3 border-t border-gray-100 bg-gray-50 rounded-b-lg text-center"
		>
			<span class="text-xs text-gray-500">只读标注</span>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from "vue";
import { NPopconfirm } from "naive-ui";

interface Annotation {
	id: string;
	text: string;
	note: string;
	color: string;
	page: number;
	position: {
		startX: number;
		startY: number;
		endX: number;
		endY: number;
	};
	createdAt: Date;
	updatedAt: Date;
	readonly?: boolean; // 只读标注，不可编辑删除
}

interface Props {
	visible: boolean;
	annotation: Annotation | null;
	position: { x: number; y: number };
}

const props = defineProps<Props>();

const emit = defineEmits<{
	(e: "close"): void;
	(e: "update", annotation: Annotation): void;
	(e: "delete", id: string): void;
}>();

const isEditing = ref(false);
const editingNote = ref("");
const editTextarea = ref<HTMLTextAreaElement>();

// 计算tooltip位置和箭头方向
const tooltipStyle = computed(() => {
	if (!props.visible || !props.position) return {};

	const { x, y } = props.position;
	const tooltipWidth = 320; // max-w-sm 约等于 320px
	const tooltipHeight = 200; // 估算高度
	const margin = 12;

	// 获取视窗尺寸
	const viewportWidth = window.innerWidth;
	const viewportHeight = window.innerHeight;

	let left = x;
	let top = y + margin;
	let arrowPos = "top";

	// 水平位置调整
	if (left + tooltipWidth > viewportWidth - margin) {
		left = viewportWidth - tooltipWidth - margin;
	}
	if (left < margin) {
		left = margin;
	}

	// 垂直位置调整
	if (top + tooltipHeight > viewportHeight - margin) {
		top = y - tooltipHeight - margin;
		arrowPos = "bottom";
	}

	return {
		left: `${left}px`,
		top: `${top}px`,
		transform: "translateZ(0)", // 硬件加速
		animation: props.visible ? "tooltipFadeIn 0.2s ease-out" : "",
	};
});

const arrowPosition = computed(() => {
	const { y } = props.position;
	const viewportHeight = window.innerHeight;
	return y > viewportHeight / 2 ? "bottom" : "top";
});

// 格式化时间
const formatTime = (date?: Date) => {
	if (!date) return "";
	return new Intl.DateTimeFormat("zh-CN", {
		month: "short",
		day: "numeric",
		hour: "2-digit",
		minute: "2-digit",
	}).format(date);
};

// 开始编辑
const startEditing = () => {
	editingNote.value = props.annotation?.note || "";
	isEditing.value = true;
	nextTick(() => {
		editTextarea.value?.focus();
	});
};

// 保存编辑
const saveEdit = () => {
	if (props.annotation && editingNote.value.trim()) {
		const updatedAnnotation = {
			...props.annotation,
			note: editingNote.value.trim(),
			updatedAt: new Date(),
		};
		emit("update", updatedAnnotation);
	}
	isEditing.value = false;
};

// 取消编辑
const cancelEditing = () => {
	isEditing.value = false;
	editingNote.value = "";
};

// 删除注释
const deleteAnnotation = () => {
	// if (props.annotation && confirm("确定要删除这个注释吗？")) {
	// 	emit("delete", props.annotation.id);
	// }
	if (props.annotation) {
		emit("delete", props.annotation.id);
	}
};

// 关闭tooltip
const close = () => {
	if (isEditing.value) {
		cancelEditing();
	}
	emit("close");
};

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
	if (event.key === "Escape" && props.visible) {
		close();
	}
};

watch(
	() => props.visible,
	(newVal, oldVal) => {
		// 在这里可以执行相应的逻辑
		if (!newVal) {
			close();
		}
	}
);

onMounted(() => {
	document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
	document.removeEventListener("keydown", handleKeydown);
});
</script>

<style scoped>
.annotation-tooltip {
	backdrop-filter: blur(8px);
	background-color: rgba(255, 255, 255, 0.98);
}

@keyframes tooltipFadeIn {
	from {
		opacity: 0;
		transform: translateY(-8px) scale(0.95);
	}
	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

/* 自定义滚动条 */
textarea::-webkit-scrollbar {
	width: 4px;
}

textarea::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 2px;
}

textarea::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 2px;
}

textarea::-webkit-scrollbar-thumb:hover {
	background: #a8a8a8;
}
</style>
