import type { AxiosProgressEvent, GenericAbortSignal } from 'axios'
import { get, post } from '@/utils/request'

export const getUrl = '/resource/files/upload'
export const getsubjectUrl = '/emind/subject_knowledge_base/upload'

export function getAudio<T>(
  params: {
    msg?: string
    conversationId?: string
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void },
) {
  const data: Record<string, any> = {
    msg: params.msg,
    conversationId: params.conversationId,
  }
  return post<T>({
    url: '/sse_api/text_to_speach',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
    onDownloadProgress: params.onDownloadProgress,
  })
}

export function fetchChatAPI<T = any>(
  prompt: string,
  options?: { conversationId?: string; parentMessageId?: string },
  signal?: GenericAbortSignal,
) {
  return post<T>({
    url: '/chat',
    data: { prompt, options },
    signal,
  })
}

export function fetchChatConfig<T = any>() {
  return post<T>({
    url: '/config',
  })
}

export function fetchChatAPIProcess<T = any>(
  params: {
    conversationId?: any
    prompt: string
    // networkingStatus?: string
    // options?: { conversationId?: string; parentMessageId?: string }
    signal?: GenericAbortSignal
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
    url?: string
    capacityCode?: string
  },
) {
  // const settingStore = useSettingStore()
  // const authStore = useAuthStore()
  console.log(params)
  const data: Record<string, any> = {
    conversationId: params.conversationId,
    content: params.prompt,
    capacityCode: params.capacityCode,
    question: params.prompt,
    // userId: infoStore().userInfo.userId,
    // msgType: '0',
    // networkingStatus: params.networkingStatus,
    // options: params.options,
  }
  console.log(data)
  // if (authStore.isChatGPTAPI) {
  //   data = {
  //     ...data,
  //     systemMessage: settingStore.systemMessage,
  //     temperature: settingStore.temperature,
  //     top_p: settingStore.top_p,
  //   }
  // }

  return post<T>({
    url: params.url as string,
    data,
    signal: params.signal,
    onDownloadProgress: params.onDownloadProgress,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function fetchSession<T>() {
  return post<T>({
    url: '/session',
  })
}

export function fetchVerify<T>(token: string) {
  return post<T>({
    url: '/verify',
    data: { token },
  })
}

/**********************/
export function textToVoice<T = any>(
  params: {
    prompt: string
    options?: { conversationId?: string; parentMessageId?: string }
    signal?: GenericAbortSignal
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
  },
) {
  // const settingStore = useSettingStore()
  // const authStore = useAuthStore()

  const data: Record<string, any> = {
    msg: params.prompt,
    conversationId: params.conversationId,

  }

  return post<T>({
    url: '/sse_api/session_initiation',
    data,
    signal: params.signal,
    onDownloadProgress: params.onDownloadProgress,
  })
}

export function voiceToText<T = any>(
  params: {
    data: any
    options?: { conversationId?: string; parentMessageId?: string }
    signal?: GenericAbortSignal
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
  },
) {
  return post<T>({
    url: '/sse_api/speech_recognition',
    data: params.data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    signal: params.signal,
    onDownloadProgress: params.onDownloadProgress,
  })
}

export function recognition_stop<T>(data: any) {
  return get<T>({
    url: '/sse_api/recognition_stop',
    data,
  })
}

export function setup<T>(data: any) {
  return get<T>({
    url: '/intelligent/setup',
    data,
  })
}

export function fetchWeaknessAnalysisProcess<T = any>(
  params: {
    examQuestion?: any
    conversationContentId: string
    examAnswer?: any
    question?: any
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
  },
) {
  console.log(params)
  const data: Record<string, any> = {
    conversationContentId: params.conversationContentId,
    examQuestion: params.examQuestion,
    examAnswer: params.examAnswer,
    question: params.question,
  }
  return post<T>({
    url: '/eaide/summary/exam_guides/analyze/exam_test_weak',
    data,
    onDownloadProgress: params.onDownloadProgress,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function getHistory<T>(data: any) {
  return get<T>({
    url: '/emind/conversation/all',
    data,
  })
}

export function addHistory<T>(data: any) {
  return post<T>({
    url: '/eaide/agent_conversation',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function delHistory<T>(data: any) {
  return post<T>({
    url: `/emind/conversation/${data.id}/delete`,
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function updateHistory<T>(data: any) {
  return post<T>({
    url: `/emind/conversation/${data.id}/update`,
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function getHistoryMsg<T>(data: any) {
  return get<T>({
    url: '/emind/conversationContent/all',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function getHistorylist<T>(data: any) {
  return get<T>({
    url: '/emind/conversation/by_agent_id',
    data,
  })
}
