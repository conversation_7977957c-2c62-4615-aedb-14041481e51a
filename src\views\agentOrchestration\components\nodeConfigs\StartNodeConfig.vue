<template>
  <div>
    <n-form-item
      path="config.duolun"
      label-placement="left"
      class="setHeight mb-[9px]"
    >
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span> 多轮对话
          <NPopover trigger="click">
            <template #trigger>
              <img src="@/assets/agentOrchestration/promptIcon.png" />
            </template>
            <span>多轮对话的描述</span>
          </NPopover>
        </div>
      </template>
      <div class="flex justify-end w-full">
        <n-switch v-model:value="formData.config.duolun" @update:value="() => handleChangeStart(formData.config.duolun, '0')" />
      </div>
    </n-form-item>

    <n-form-item
      class="h-[38px] mb-[12px]"
      label=""
      path="config.lunshu"
      v-if="formData.config.duolun"
      label-placement="left"
    >
      <template #label>
        <div class="histit">携带历史对话轮数</div>
      </template>
      <div class="flex justify-end w-full h-[38px]">
        <div class="w-[114px]">
          <n-input-number
            min="0"
            v-model:value="formData.config.lunshu"
            placeholder="轮数"
          />
        </div>
      </div>
    </n-form-item>

    <n-form-item
      label-placement="left"
      class="setHeight mt-[12px] mb-[6px]"
    >
      <template #label>
        <div class="rowstit"><span class="rowicon"></span>输入</div>
      </template>
    </n-form-item>

    <NDataTable
      :bordered="false"
      single-column
      :columns="startcolumns"
      :data="startdata"
    />
    <div class="divider mt-[16px] mb-[16px]"></div>
    <NDataTable
      :bordered="false"
      single-column
      :columns="environmentVariableColumns"
      :data="environmentVariableData"
    />
    <n-form-item
      path="config.duolun"
      label-placement="left"
      class="setHeight mt-[17px] mb-[6px]"
    >
      <template #label>
        <div class="rowstit">
          <span class="rowicon"></span> 文件上传
          <NPopover trigger="click">
            <template #trigger>
              <img src="@/assets/agentOrchestration/promptIcon.png" />
            </template>
            <span>文件上传的描述</span>
          </NPopover>
        </div>
      </template>
      <div class="flex justify-end w-full">
        <n-switch v-model:value="formData.config.shangchuan" @update:value="() => handleChangeStart(formData.config.shangchuan, '1')" />
      </div>
    </n-form-item>

    <NDataTable
      v-if="formData.config.shangchuan"
      :bordered="false"
      single-column
      :columns="fileColumns"
      :data="fileData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';
import { NDataTable, NFormItem, NInputNumber, NPopover, NSwitch } from 'naive-ui';
import { useOrchestrationStore } from '@/store';
import { generateId } from '@/store/modules/orchestration';

const props = defineProps<{
  formData: any;
  node: any;
}>();

const emit = defineEmits<{
  'update:formData': [value: any];
}>();

const orchestrationStore = useOrchestrationStore();

// 表格列定义
const startcolumns = ref([
  {
    title: "会话数据变量",
    key: "name",
  },
  {
    title: "数据类型",
    key: "type",
  },
]);

const environmentVariableColumns = ref([
  {
    title: "环境变量",
    key: "name",
  },
  {
    title: "数据类型",
    key: "type",
  },
]);

const fileColumns = ref([
  {
    title: "会话数据变量",
    key: "name",
  },
  {
    title: "数据类型",
    key: "type",
  },
]);

// 计算属性 - 开始节点数据
const startdata = computed(() => {
  let data = [
    {
      name: "历史对话信息",
      type: "数组 [结构化数据]",
      isreturn: props.formData.config.duolun ? true : false,
    },
    {
      name: "当前对话信息",
      type: "文本",
      isreturn: true,
    },
    {
      name: "当前对话文件",
      type: "数组[文件]",
      isreturn: props.formData.config.shangchuan ? true : false,
    },
    {
      name: "用户ID",
      type: "文本",
      isreturn: true,
    },
    {
      name: "会话ID",
      type: "文本",
      isreturn: props.formData.config.duolun ? true : false,
    },
    {
      name: "对话轮次",
      type: "数值",
      isreturn: props.formData.config.duolun ? true : false,
    },
  ];

  return data.filter((item) => item.isreturn);
});

// 环境变量数据
const environmentVariableData = ref([
  {
    name: "智能体ID",
    type: "文本",
  },
  {
    name: "触发时间",
    type: "文本",
  },
]);

// 文件数据
const fileData = ref([
  {
    name: "文件信息",
    type: "结构化数据",
  },
]);

// 处理开始节点配置变更
const handleChangeStart = (value: boolean, type: string) => {
  let multiwheelVariable = [
    { name: "历史对话信息", type: "array" },
    { name: "会话ID", type: "string" },
    { name: "对话轮次", type: "number" },
  ];
  let uploadVariable = [{ name: "当前对话文件", type: "file" }];

  if (value) {
    if (type === "0") {
      multiwheelVariable.forEach((item: any) => {
        orchestrationStore.addVariable({
          id: generateId(),
          name: item.name,
          type: "nodeVariable",
          valueType: item.type,
          value: "",
          readonly: true,
          nodeId: props.node.id,
          nodeName: props.node.data.label,
          nodeType: props.node.type,
        });
      });
    } else {
      uploadVariable.forEach((item: any) => {
        orchestrationStore.addVariable({
          id: generateId(),
          name: item.name,
          type: "nodeVariable",
          valueType: item.type,
          value: "",
          readonly: true,
          nodeId: props.node.id,
          nodeName: props.node.data.label,
          nodeType: props.node.type,
        });
      });
    }
  } else {
    if (type === "0") {
      orchestrationStore
        .getVariablesByType("nodeVariable")
        .filter((variable) => variable.nodeType === "start")
        .forEach((item: any) => {
          if (
            item.name === "历史对话信息" ||
            item.name === "会话ID" ||
            item.name === "对话轮次"
          ) {
            orchestrationStore.deleteVariable(item.id);
          }
        });
    } else {
      orchestrationStore
        .getVariablesByType("nodeVariable")
        .filter((variable) => variable.nodeType === "start")
        .forEach((item: any) => {
          if (item.name === "当前对话文件") {
            orchestrationStore.deleteVariable(item.id);
          }
        });
    }
  }

  // 触发更新事件
  emit('update:formData', props.formData);
};
</script>

<style scoped lang="less">
@import './nodeConfigStyle.less';
</style>