import type { AxiosProgressEvent, GenericAbortSignal } from 'axios'

import { get, post } from '@/utils/request'
//流程运行测试接口
export function testRunApiProcess<T = any>(
  params: {
    signal?: GenericAbortSignal
    query: any
    id: any
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
  },
) {
  const data: Record<string, any> = {
    query: params.query,
    id: params.id,
  }
  return post<T>({
    url: '/emind/workflow/run_test',
    data,
    signal: params.signal,
    onDownloadProgress: params.onDownloadProgress,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
export function creatWorkflowApi<T = any>(data: any) {
    return post<T>({
      url: `/emind/workflow`,
      data,
      headers: {
        'Content-Type': 'application/json',
    },
    })
  }

  
  export function workflowDetailApi<T = any>(data: any) {
	return get<T>({
		url: `/emind/workflow/${data.id}`,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function updateWorkflowApi<T = any>(data: any,id:any) {
	return post<T>({
		url: `/emind/workflow/${id}/update`,
    data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}
