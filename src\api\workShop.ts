import {get, post} from '@/utils/request'
import type {AxiosProgressEvent} from "axios";

export function getagentlistApi<T = any>(data: any) {
	return get<T>({
		url: '/eaide/intelligent_agent/list',
		data,
	})
}


export function delagentlistApi<T = any>(data: any) {
	return get<T>({
		url: '/eaide/intelligent_agent/delete',
		data,
	})
}

export function setReleaseStatusApi<T = any>(data: any, id: any) {
	return post<T>({
		url: `/eaide/agent/${id}/update`,
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function addShop<T = any>(data: any) {
	return post<T>({
		url: '/emind/agent_snapshot',
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function addConversation<T = any>(data: any) {
	return post<T>({
		url: '/emind/conversation',
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function editShop<T = any>(data: any, id: string, status?: number) {
	if (status == 1) {
		//取消发布
		return post<T>({
			url: `/emind/agent_snapshot/${id}/unpublish`,
			data,
			headers: {
				'Content-Type': 'application/json',
			},
		})
	} else {
		//发布
		return post<T>({
			url: `/emind/agent_snapshot/${id}/release`,
			data,
			headers: {
				'Content-Type': 'application/json',
			},
		})
	}

}

export function updateAgent<T = any>(data: any, id: string, status?: number) {
		return post<T>({
			url: `/emind/agent_snapshot/${id}/update`,
			data,
			headers: {
				'Content-Type': 'application/json',
			},
		})

}

export function agentKnowledge<T = any>(data?: any) {
	return get<T>({
		url: '/emind/agentKnowledge/sync',
		data,
	})
}

export function agentParam<T = any>(data?: any) {
	return get<T>({
		url: '/emind/agent/param',
		data,
	})
}

export function getAgentKnowledge<T = any>(data?: any) {
	return get<T>({
		url: '/emind/agentKnowledge',
		data
	})
}
export function getRolesTree<T = any>() {
    return get<T>({
        url: '/platform/roles/tree',
        data: {
            column: 'id',
            order: 'desc',
        }
    })
}

export function museModels<T = any>(data?: any) {
	const params = {
		category: 0,
		status: 1,
		...data
	};
	return get<T>({
		url: '/muse/models/all',
		data: params,
	})
}

export function getAgentlist<T = any>(data: any) {
	return get<T>({
		url: '/emind/agent_snapshot',
		data,
	})
}

export function delModel<T = any>(id: any) {
	return post<T>({
		url: `/emind/agent_snapshot/${id}/delete`,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function getAgentlistId<T = any>(id: any) {
	return get<T>({
		url: `/emind/agent_snapshot/${id}`,
	})
}

export function getAgentlistAll<T = any>(data: any) {
	return get<T>({
		url: '/emind/agent/all',
		data,
	})
}

// export function optimize<T = any>(data: any) {
// 	return get<T>({
// 		url: '/emind/agent/prompt_template_optimize',
// 		data,
// 	})
// }

export function optimize<T = any>(
	params: {
		modelTemp?: any
		promptTemplate: string
		modelId?: any
		maxLength?: any
		onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
	},
) {

	const data: Record<string, any> = {
		modelTemp: params.modelTemp,
		promptTemplate: params.promptTemplate,
		modelId: params.modelId,
		maxLength: params.maxLength,
	}
	return post<T>({
		url: '/emind/agent/prompt_template_optimize',
		data,
		onDownloadProgress: params.onDownloadProgress,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function findVisitConfAgentId<T = any>(id: any) {
	return get<T>({
		url: `/emind/agent_visit_conf/find_visit_conf_by_agentId/${id}`,
	})
}

export function agentVisitConf<T = any>(data: any) {
	return post<T>({
		url: '/emind/agent_visit_key_conf',
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function delAgentVisitConf<T = any>(id: any) {
	return post<T>({
		url: `/emind/agent_visit_key_conf/${id}/delete`,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

export function agentVisitConfUpdate<T = any>(id: any, data: any) {
	return post<T>({
		url: `/emind/agent_visit_conf/${id}/update`,
		data,
		headers: {
			'Content-Type': 'application/json',
		},
	})
}

// 获取会话管理列表
export function getManagerList<T = any>(data: any) {
	return get<T>({
		url: '/agent_snapshot/manager_list',
		data,
	})
}

// 获取会话列表
export function getConversationList<T = any>(data: any) {
	return get<T>({
		url: '/emind/conversation/conversation_list',
		data,
	})
}

// 获取会话详情
export function getConversationDetail<T = any>(data: any) {
	return get<T>({
		url: '/emind/conversation',
		data,
	})
}

export function getUseAnalysis<T = any>(data: any) {
	return get<T>({
		url: '/emind/conversation/use_analysis',
		data,
	})
}

export function getResourceAnalysis<T = any>(data: any) {
	return get<T>({
		url: '/emind/conversation/resource_analysis',
		data,
	})
}
