<template>
  <div>
    <template v-for="(item, index) in formData.config.conditionBranchArr" :key="index">
      <n-form-item label-placement="left">
        <div class="flex justify-between items-center w-full">
          <div class="flex items-center">
            <span class="text-[#000000] text-[16px]">{{
              index == 0
                ? "如果"
                : index ==
                  formData.config.conditionBranchArr.length - 1
                  ? "否则"
                  : "否则如果"
            }}</span>
            <span class="condition-branch-label">
              {{ item.name }}
            </span>
          </div>
          <div
            v-if="
              formData.config.conditionBranchArr.length > 2 &&
              index != formData.config.conditionBranchArr.length - 1
            "
            class="ml-[10px]"
          >
            <img
              @click="removeElseBranch(index)"
              class="w-[16px] cursor-pointer"
              src="@/assets/agentOrchestration/delIcon2.png"
              title="删除"
            />
          </div>
        </div>
      </n-form-item>

      <div class="condition-section">
        <!-- 条件组容器 -->
        <div
          class="condition-group"
          v-if="item.conditionArr && item.conditionArr.length > 0"
        >
          <!-- 左侧花括号和逻辑操作符 -->
          <div
            class="condition-bracket-container"
            v-if="item.conditionArr.length > 1"
          >
            <div
              class="condition-bracket"
              :style="{
                height:
                  (item.conditionArr.length - 2) * 116 +
                  (item.conditionArr.length - 1) * 12 +
                  116 +
                  'px',
              }"
            >
              <div class="bracket-top"></div>
              <div class="bracket-middle">
                <n-select
                  v-model:value="item.conditionLogic"
                  :options="logicOperatorOptions"
                  placeholder="且"
                  size="small"
                  class="group-logic-selector"
                  @update:value="handleLogicOperatorChange"
                />
              </div>
              <div class="bracket-bottom"></div>
            </div>
          </div>

          <!-- 条件列表 -->
          <div class="condition-list">
            <div
              v-for="(condition, childrenindex) in item.conditionArr"
              :key="childrenindex"
              class="condition-item-wrapper"
            >
              <!-- 条件配置行 -->
              <div class="flex items-center">
                <div class="flex-1">
                  <div class="flex h-[38px]">
                    <div class="flex-1">
                      <AggregationSelector
                        v-model="condition.variable"
                        :options="aggregationOptions"
                        placeholder="请选择变量"
                        @change="$emit('handleAggregationChange', $event)"
                      />
                    </div>
                    <div class="w-[114px] ml-[12px]">
                      <n-select
                        v-model:value="condition.operator"
                        :options="conditionOperatorOptions"
                        placeholder="等于"
                        size="small"
                      />
                    </div>
                  </div>
                  <div class="flex mt-[12px] h-[38px]">
                    <div class="w-[114px]">
                      <n-select
                        v-model:value="condition.field"
                        :options="variableOptions"
                        placeholder="0"
                        size="small"
                      />
                    </div>
                    <div class="flex-1 ml-[12px] h-[38px] outputrow">
                      <AggregationSelector
                        v-if="condition.field == '0'"
                        v-model="condition.value"
                        :options="aggregationOptions"
                        placeholder="请选择变量"
                        @change="$emit('handleAggregationChange', $event)"
                      />
                      <n-input
                        v-else
                        v-model:value="condition.value"
                        type="text"
                        placeholder="变量值"
                      >
                      </n-input>
                    </div>
                  </div>
                </div>
                <div
                  v-if="item.conditionArr.length > 1"
                  class="ml-[10px]"
                >
                  <img
                    @click="removeCondition(index, childrenindex)"
                    class="action-icon"
                    src="@/assets/agentOrchestration/delIcon2.png"
                    title="删除"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 添加条件按钮 -->
        <div
          class="add-condition-wrapper"
          v-if="
            index != formData.config.conditionBranchArr.length - 1
          "
        >
          <n-button
            @click="addCondition(index)"
            dashed
            class="add-condition-button"
          >
            + 添加条件
          </n-button>
        </div>
      </div>
      <!-- 添加否则分支按钮 -->
      <div
        class="add-else-branch-wrapper"
        v-if="index == formData.config.conditionBranchArr.length - 1"
      >
        <n-button
          @click="addElseBranch"
          type="info"
          color="#125EFF"
          class="add-else-branch-button"
        >
          + 添加分支
        </n-button>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { NFormItem, NInput, NSelect, NButton } from 'naive-ui';
import AggregationSelector from '../AggregationSelector.vue';
import { generateId } from "@/store/modules/orchestration";

interface Props {
  formData: {
    config: {
      conditionBranchArr: Array<{
        id: string;
        name: string;
        disabled: boolean;
        conditionArr: Array<{
          variable: string;
          operator: string;
          field: string;
          value: string;
        }>;
        conditionLogic: string;
      }>;
      conditionLogic: string;
    };
  };
  node: {
    type: Object,
    required: true,
  },
  variableOptions: Array<{
    label: string;
    value: string;
  }>;
  aggregationOptions: Array<any>;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:formData': [value: Props['formData']];
  'handleAggregationChange': [value: any];
}>();

// 条件操作符选项
const conditionOperatorOptions = [
 { label: "包含", value: "contains" },
{ label: "不包含", value: "not_contains" },
{ label: "以开始", value: "start_with" },
{ label: "以结束", value: "end_with" },
{ label: "是", value: "is" },
{ label: "不是", value: "is_not" },
{ label: "内容为空", value: "empty" },
{ label: "内容不为空", value: "not_empty" },
{ label: "在...中", value: "in" },
{ label: "不在...中", value: "not_in" },
{ label: "所有", value: "all_of" },
{ label: "等于", value: "equal" },
{ label: "不等于", value: "not_equal" },
{ label: "大于", value: "greater_than" },
{ label: "小于", value: "less_than" },
{ label: "大于等于", value: "not_less_than" },
{ label: "小于等于", value: "not_greater_than" },
{ label: "长度大于", value: "length_greater_than" },
{ label: "长度小于", value: "length_less_than" },
{ label: "长度大于等于", value: "length_not_less_than" },
{ label: "长度小于等于", value: "length_not_greater_than" },
{ label: "为空", value: "null" },
{ label: "不为空", value: "not_null" }
];

// 逻辑操作符选项
const logicOperatorOptions = [
  { label: "且", value: "且" },
  { label: "或", value: "或" },
  { label: "非", value: "非" },
];

// 条件节点相关方法
const addCondition = (index: number) => {
  const newFormData = { ...props.formData };
  newFormData.config.conditionBranchArr[index].conditionArr.push({
    variable: "",
    operator: "等于",
    field: "0",
    value: "",
  });
  emit('update:formData', newFormData);
};

const removeElseBranch = (index: number) => {
  const newFormData = { ...props.formData };
  if (newFormData.config.conditionBranchArr) {
    newFormData.config.conditionBranchArr.splice(index, 1);
  }
  emit('update:formData', newFormData);
};

const removeCondition = (index: number, childrenindex: number) => {
  const newFormData = { ...props.formData };
  if (newFormData.config.conditionBranchArr) {
    newFormData.config.conditionBranchArr[index].conditionArr.splice(
      childrenindex,
      1
    );
  }
  emit('update:formData', newFormData);
};

// 处理逻辑操作符变更
const handleLogicOperatorChange = (value: string) => {
  console.log("主条件逻辑操作符变更为:", value);
  // 这里可以添加额外的逻辑处理
};

const handleElseIfLogicOperatorChange = (value: string) => {
  console.log("否则如果逻辑操作符变更为:", value);
  // 这里可以添加额外的逻辑处理
};

const addElseBranch = () => {
  const newFormData = { ...props.formData };
  newFormData.config.conditionBranchArr.splice(
    newFormData.config.conditionBranchArr.length - 1,
    0,
    {
      id: generateId() + "-output",
      name: "分支" + newFormData.config.conditionBranchArr.length,
      disabled: false,
      conditionArr: [{ variable: "", operator: "等于", field: "0", value: "" }],
      conditionLogic: "",
    }
  );
  emit('update:formData', newFormData);
};
</script>

<style scoped>
@import './nodeConfigStyle.less';
</style>