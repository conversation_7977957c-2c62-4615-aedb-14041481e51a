<template>
  <div class="knowledge-node-config">
    <n-form-item label-placement="left" class="setHeight mb-[13px]">
      <template #label>
        <div class="rowstit"><span class="rowicon"></span> 输入</div>
      </template>
    </n-form-item>
    <n-form-item label-placement="left" class="setHeight mb-[8px]">
      <template #label>
        <div class="text-[#565756]">查询变量</div>
      </template>
      <div class="flex justify-end w-full text-[#C7C7C7]">文本</div>
    </n-form-item>
    <n-form-item path="config.jiansuoType" label-placement="left">
      <div class="w-[162px] mr-[14px] h-[38px]">
        <n-select
          v-model:value="formData.config.jiansuoType"
          :options="variableOptions"
          default-value="0"
          filterable
        />
      </div>
      <div class="w-full h-[38px]">
        <n-input
          v-if="formData.config.jiansuoType == '1'"
          v-model:value="formData.config.jiansuotypevalue"
          type="text"
          placeholder="请输入变量"
          maxlength="20"
          show-count
        >
        </n-input>
        <AggregationSelector
          v-else
          v-model="formData.config.jiansuotypevalue"
          :options="aggregationOptions"
          placeholder="请选择变量"
          @change="handleAggregationChange"
        />
      </div>
    </n-form-item>
    <n-form-item
      label-placement="left"
      class="setHeight mt-[24px] mb-[12px]"
    >
      <template #label>
        <div class="rowstit">知识库</div>
      </template>
      <div class="flex justify-end w-full">
        <img
          class="w-[16px] h-[16px] cursor-pointer"
          src="@/assets/agentOrchestration/yitupeizhi.png"
        />
        <img
          class="w-[16px] h-[16px] ml-[10px] cursor-pointer"
          src="@/assets/agentOrchestration/yituzhishi.png"
          @click="knowledgeBaseShow = true"
        />
      </div>
    </n-form-item>

    <div
      class="knowledgelist flex justify-between"
      v-for="(item, index) in formData.config.databases"
      :key="index"
    >
      {{ item.name }}
      <img
        class="w-[12px]"
        src="@/assets/agentOrchestration/yituzhishidel.png"
        @click="delknowledgefun(index)"
      />
    </div>
    <n-form-item label-placement="left" class="setHeight mt-[12px]">
      <template #label>
        <div class="rowstit"><span class="rowicon"></span>输出</div>
      </template>
    </n-form-item>

    <n-form-item label-placement="left">
      <div class="w-full flex text-[#C7C7C7]">
        <div class="w-[50%]">名称</div>
        <div>数据类型</div>
      </div>
    </n-form-item>

    <n-form-item path="config.wenbenshuchu" label-placement="left">
      <div class="w-full flex text-[#565756] items-center">
        <div class="w-[50%]">
          <div class="w-[234px] h-[38px]">
            <n-input
              v-model:value="formData.config.wenbenshuchu"
              type="text"
              placeholder="请输入输出名称"
               @input="handleWenbenShuchuChange"
            >
            </n-input>
          </div>
        </div>
        <div>文本</div>
      </div>
    </n-form-item>

    <!-- 知识库添加弹窗 -->
    <NModal v-model:show="knowledgeBaseShow">
      <NCard
        class="modelParametercard"
        style="width: 864px"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <div
          class="flex items-center justify-between h-[70px] text-[17px] font-medium text-[#1F1F1F] pl-[26px] pr-[23px] border-b-[1px] border-[#E5E5E5]"
        >
          <span>添加知识库</span><span>×</span>
        </div>
        <div
          class="pl-[26px] pr-[23px] flex h-[38px] items-center mb-[20px] mt-[25.83px]"
        >
          <div class="w-[678px]">
            <NInput
              v-model:value="searchknowledgeBase"
              placeholder="搜索知识库"
            ></NInput>
          </div>
          <div class="btnparent w-[112px] h-[38px] ml-[16px]">
            <NButton type="info" color="#125EFF">新建知识库</NButton>
          </div>
        </div>
        <NCheckboxGroup v-model:value="knowledgeBasechecklist">
          <div
            class="flex justify-between bg-[#F6F6F6] w-[814px] mx-auto rounded-lg h-[38px] items-center pl-[20px] pr-[25px] mb-[10px]"
            v-for="(item, index) in knowledgeBasesList"
            :key="index"
          >
            <NCheckbox :value="item.value" :label="item.name" />
            <div class="text-[#A4A6AB] text-[13px]">{{ item.size }}个文件</div>
          </div>
        </NCheckboxGroup>
        <template #footer>
          <div
            class="flex w-full justify-between items-center pl-[26px] pr-[23px] pb-[27px] mt-[147px]"
          >
            <div class="text-[13px] text-[#A4A6AB]">
              已选 {{ knowledgeBaseCheckNum }} 个
            </div>
            <div class="flex">
              <div class="btnparent w-[80px] h-[36px]">
                <NButton @click="knowledgeBaseShow = false">取消</NButton>
              </div>
              <div class="btnparent w-[80px] h-[36px] ml-[16px]">
                <NButton @click="saveknowledgeBasefun" type="info" color="#125EFF"
                  >保存</NButton
                >
              </div>
            </div>
          </div>
        </template>
      </NCard>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { NFormItem, NInput, NSelect, NCard, NModal, NCheckbox, NCheckboxGroup, NButton } from 'naive-ui';
import AggregationSelector from '../AggregationSelector.vue';
import { useOrchestrationStore } from "@/store";

const props = defineProps<{
  formData: any;
    node: {
    type: Object,
    required: true,
  },
  variableOptions: any[];
  aggregationOptions: any[];
}>();
const orchestrationStore = useOrchestrationStore();

const emit = defineEmits<{
  'update:formData': [value: any];
  handleAggregationChange: [value: string, variable: any, group: any];
}>();

// 知识库弹窗是否展示
const knowledgeBaseShow = ref(false);
// 知识库已选中列表数据
const knowledgeBasechecklist = ref([]);
// 知识库列表搜索
const searchknowledgeBase = ref('');
// 知识库列表
const knowledgeBaseList = ref([
  { name: '知识库名称1', size: '34', value: '001' },
  { name: '知识库名称2', size: '23', value: '002' },
]);
// 知识库展示列表
const knowledgeBasesList = computed(() => {
  if (searchknowledgeBase.value) {
    return knowledgeBaseList.value.filter((item) => {
      return item.name.includes(searchknowledgeBase.value);
    });
  } else {
    return knowledgeBaseList.value;
  }
});
// 知识库展示已选中数量
const knowledgeBaseCheckNum = computed(() => {
  return knowledgeBasechecklist.value.length;
});

// 删除知识库
const delknowledgefun = (index: number) => {
  console.log('意图识别节点删除知识库函数+');
  if (props.formData.config.databases) {
    const newFormData = { ...props.formData };
    newFormData.config.databases.splice(index, 1);
    emit('update:formData', newFormData);
  }
};

// 保存知识库选择
const saveknowledgeBasefun = () => {
  const newFormData = { ...props.formData };
  newFormData.config.databases = [];
  knowledgeBasechecklist.value.forEach((item: any) => {
    console.log(item);
    let data = knowledgeBaseList.value.find((items) => {
      return items.value === item;
    });
    newFormData.config.databases.push(data);
  });
  knowledgeBaseShow.value = false;
  emit('update:formData', newFormData);
};

// 处理聚合变量变化
const handleAggregationChange = (value: string, variable: any, group: any) => {
  emit('handleAggregationChange', value, variable, group);
};
const handleWenbenShuchuChange=(val:any)=>{
    const variables = orchestrationStore.getVariablesByNodeId(props.node.id)[0] || [];
    orchestrationStore.updateVariable(variables.id, {
        name: props.formData.config.wenbenshuchu,
        code: variables.code,
        valueType: variables.type,
        value: variables.value
      })
}
</script>

<style lang="less" scoped>
@import './nodeConfigStyle.less';
</style>