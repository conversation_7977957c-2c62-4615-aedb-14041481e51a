<script setup lang='ts'>
import type { CSSProperties } from 'vue'
import { computed, ref, watch } from 'vue'
import { NLayoutSider,NPopconfirm } from 'naive-ui'
import toolList from './toolList.vue'
import List from './List.vue'
import { useAppStore, useChatStore } from '@/store'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { PromptStore } from '@/components/common'
import { infoStore } from '@/store/modules/info'
import {logout } from '@/utils/microFrontEnd'

const appStore = useAppStore()
const chatStore = useChatStore()
const info = infoStore()

const serch = ref()


const { isMobile } = useBasicLayout()
const show = ref(false)
const toolListdom = ref(null)
const collapsed = computed(() => appStore.siderCollapsed)


function handleUpdateCollapsed() {
  appStore.setSiderCollapsed(!collapsed.value)
}

function serchTap() {
  chatStore.setState({ title: serch.value, userId: info.userInfo.userId })
}
function handlePositiveClick() {
  console.log('shide');
        logoutfun()
      }
      function    handleNegativeClick() {
      console.log('取消');
      
      }


const getMobileClass = computed<CSSProperties>(() => {
  if (isMobile.value) {
    return {
      position: 'fixed',
      zIndex: 50,
    }
  }
  return {}
})

const mobileSafeArea = computed(() => {
  if (isMobile.value) {
    return {
      paddingBottom: 'env(safe-area-inset-bottom)',
    }
  }
  return {}
})

watch(
  isMobile,
  (val) => {
    // 只在移动端时强制收缩，桌面端保持用户偏好
    if (val) {
      appStore.setSiderCollapsed(true)
    }
    // 桌面端不自动修改用户的折叠偏好，保持当前状态
  },
  {
    immediate: true,
    flush: 'post',
  },
)

const handleListSelect = () => {
  if (toolListdom.value)
    toolListdom.value.clearactiveKey()
}
function logoutfun(){
  logout()
}
</script>

<template>
  <NLayoutSider
    :collapsed="collapsed"
    :collapsed-width="80"
    :width="280"
    :show-trigger="false"
    :show-collapsed-content="true"
    collapse-mode="width"
    bordered
    :style="getMobileClass"
    @update-collapsed="handleUpdateCollapsed"
  >
    <!-- 展开状态 -->
    <div v-if="!collapsed" class="flex flex-col h-full bg-[#FFF]" :style="mobileSafeArea">
      <main class="flex flex-col flex-1 min-h-0   bg-[#F9FBFF]">
        <div class="p-4 flex justify-between items-center rounded-md p-4 space-y-2  font-bold text-2xl text-[#3C3231] tracking-normal text-center">
      <img class="logotit" src="@/assets/logotit.png" /><div class="imgbox"><img class="w-6 h-6" src="@/assets/lefticon.png" @click="handleUpdateCollapsed"></div>
        </div>

        <!--        <div class="h-px mx-4 bg-[#E4D4D4]" /> -->
        <!-- <div class="pr-4 pl-4">
          <NInput v-model:value="serch" class="h-11 bug" style="border-radius: 12px" type="text" placeholder="搜索对话" @keypress="handleEnter">
            <template #prefix>
              <img class="cursor-pointer ml-1 mr-1" :src="searchLogo" alt="" @click="serchTap">
            </template>
          </NInput>
        </div> -->
        <div class="flex-2 min-h-0 pb-4">
          <toolList ref="toolListdom" />
        </div>
        <!-- <div class="pl-4 pr-4">
          <div class="w-full bg-[#E2E2E2] h-[1px] " />
        </div> -->



              <!-- <div class="mt-4 px-4 text-sm text-[#404040] mb-1">
                 7天内
               </div> -->
               <div class="flex-1 min-h-0  mb-4 overflow-hidden">
                 <List @onSelect="handleListSelect" />
               </div>



        <!-- <div class="pl-4 pr-4">
          <div class="w-full bg-[#E2E2E2] h-[1px] " />
        </div> -->
        <!-- <div class="p-4"> -->
          <!--          <NButton dashed block @click="handleAdd"> -->
          <!--            {{ $t('chat.newChatButton') }} -->
          <!--          </NButton> -->
          <!-- <div class=" flex items-center h-11 border-solid border-2 border-[#125eff26] bg-[#125eff0d] rounded-xl  text-[#125EFF] font-medium cursor-pointer" @click="handleAdd">
            <img class="w-4 h-4 mr-2 ml-4" :src="jia2" alt="">
            新对话
          </div> -->
        <!-- </div> -->
        <!--        <div class="flex items-center p-4 space-x-4"> -->
        <!--          <div class="flex-1"> -->
        <!--            <NButton block @click="show = true"> -->
        <!--              {{ $t('store.siderButton') }} -->
        <!--            </NButton> -->
        <!--          </div> -->
        <!--          <NButton @click="handleClearAll"> -->
        <!--            <SvgIcon icon="ri:close-circle-line" /> -->
        <!--          </NButton> -->
        <!--        </div> -->
      </main>
      <div class="bg-[#F9FBFF] z-10">
        <div class="loginout">
          <n-popconfirm
    @positive-click="handlePositiveClick"
    @negative-click="handleNegativeClick"
  >
    <template #trigger>
          <img class="closelogin" src="@/assets/loginout.png">
    </template>
    确定退出登录吗？
  </n-popconfirm>
          <!-- <img @click="logoutfun" src="@/assets/loginout.png"> -->
          <div class="radiobg">
          <img src="@/assets/usericon.png">
          </div>
        </div>
      </div>
      <!--      <Footer /> -->
    </div>

    <!-- 收缩状态 -->
    <div v-else class="flex flex-col h-full bg-[rgb(247,250,255)]" :style="mobileSafeArea">
      <main class="flex flex-col flex-1 min-h-0">
        <!-- 收缩状态下的头部 -->
        <div class="flex justify-center items-center h-20">
          <div class="w-12 h-12 flex justify-center items-center cursor-pointer hover:bg-white hover:rounded-lg transition-all duration-200" @click="handleUpdateCollapsed">
            <img class="w-6 h-6" src="@/assets/righticon.png">
          </div>
        </div>

        <!-- 收缩状态下的菜单 -->
        <div class="flex-1 overflow-visible">
          <toolList />
        </div>

        <!-- 收缩状态下的历史会话 -->
        <div class="flex-1 overflow-hidden">
          <List />
        </div>

        <!-- 收缩状态下的底部用户区域 -->
        <div class="flex justify-center items-center pb-4">
          <div class="w-12 h-12 flex justify-center items-center">
            <n-popconfirm
              @positive-click="handlePositiveClick"
              @negative-click="handleNegativeClick"
            >
              <template #trigger>
                <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center cursor-pointer hover:shadow-md transition-all duration-200">
                  <img class="w-5 h-5" src="@/assets/usericon.png">
                </div>
              </template>
              确定退出登录吗？
            </n-popconfirm>
          </div>
        </div>
      </main>
    </div>
  </NLayoutSider>
  <template v-if="isMobile">
    <div v-show="!collapsed" class="fixed inset-0 z-40 w-full h-full bg-black/40" @click="handleUpdateCollapsed" />
  </template>
  <PromptStore v-model:visible="show" />
</template>

<style scoped lang="less">
:deep(.bug .n-input__input-el){
	height:2.75rem !important;
}
.loginout{
  width: 142px;
height: 64px;
background: #FCFCFC;
background-image: linear-gradient(114deg, #FDE6FE 0%, #C3F9FF 100%);
box-shadow: 0 2px 4px 0 #e6e6e680;
border-radius: 33px;
margin: 0 auto;
display: flex;
align-items: center;
justify-content: center;
margin-bottom: 20px;
.radiobg{
  width: 44px;
height: 44px;
opacity: 0.8;
background: #FFFFFF;
display: flex;
align-items: center;
justify-content: center;
border-radius: 100%;
margin-left: 30px;
}
img{
  width: 22px;
  height: 24px;
}
}
.closelogin:hover{
  cursor: pointer;
}
.logotit{
  width: 204px;
height: 40px;
}
.imgbox{
height: 40px;
display: flex;
align-items: center;
margin-left: 10px;
}
</style>
