<template>
  <div class="h-full flex flex-col">
    <!-- 搜索区域 -->
    <div class="flex gap-4 mb-[20px] items-center justify-between">
      <div class="flex gap-4 items-center search">
        <n-select
          v-model:value="searchForm.status"
          placeholder="状态"
          clearable
          @update:value="handleSearch"
          :style="{
            width: '248px',
          }"
          :options="statusOptions"
        />
        <n-input
          v-model:value="searchForm.name"
          placeholder="搜索"
          clearable
          @input="handleSearch"
          :style="{
            width: '248px',
          }"
        >
          <template #suffix>
            <div class="search-icon-wrapper">
              <n-icon :size="16" style="color: #8f8f98">
                <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                    fill="#8F8F98"
                  />
                </svg>
              </n-icon>
            </div>
          </template>
        </n-input>
      </div>
      <n-button
        type="primary"
        @click="showCreateModal = true"
        class="create-btn"
      >
        <template #icon>
          <img
            src="@/assets/btnIcon.png"
            alt=""
            style="width: 20px; height: 20px"
          />
        </template>
        创建提示词
      </n-button>
    </div>

    <!-- 列表区域 -->
    <div class="flex-1">
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        striped
      />
    </div>

    <!-- 创建提示词弹窗 -->
    <n-modal v-model:show="showCreateModal" :mask-closable="false">
      <n-card
        style="width: 644px; border-radius: 8px !important"
        title="创建提示词"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        class="create-modal"
      >
        <n-form ref="createFormRef" :model="createForm" :rules="createRules">
          <n-form-item label="提示词名称" path="name" class="custom-form-item">
            <n-input
              v-model:value="createForm.name"
              type="textarea"
              placeholder="请输入提示词名称..."
              maxlength="50"
              show-count
              class="custom-input"
              :autosize="{ minRows: 3, maxRows: 6 }"
            />
          </n-form-item>
        </n-form>

        <template #footer>
          <div class="flex justify-end gap-3">
            <n-button
              @click="showCreateModal = false"
              secondary
              class="cancel-btn"
              >取消</n-button
            >
            <n-button
              type="primary"
              @click="handleCreate"
              :loading="createLoading"
              class="create-submit-btn"
            >
              创建
            </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from "vue";
import { useRouter } from "vue-router";
import {
  NDataTable,
  NButton,
  NInput,
  NSelect,
  NModal,
  NCard,
  NForm,
  NFormItem,
  NIcon,
  useMessage,
  useDialog,
} from "naive-ui";
import {
  createPrompt,
  getPromptList,
  deletePrompt,
} from "@/api/promptManagement";
import { getDictByCode, getDictTextByCodeValue } from "@/utils/microFrontEnd";

const router = useRouter();
const message = useMessage();
const dialog = useDialog();

// 搜索表单
const searchForm = reactive({
  name: "",
  status: null,
});

// 状态选项 - 从字典获取
const statusOptions = ref([]);

// 获取字典数据
const loadStatusOptions = () => {
  try {
    const dictData = getDictByCode("prompt_publishing_status");
    if (dictData && Array.isArray(dictData)) {
      statusOptions.value = dictData.map((item) => ({
        label: item.text,
        value: item.value,
      }));
    }
  } catch (error) {
    console.warn("获取状态字典失败:", error);
    // 如果字典获取失败，使用默认选项
    statusOptions.value = [
      { label: "未发布", value: "0" },
      { label: "已发布", value: "1" },
    ];
  }
};

// 创建表单
const createForm = reactive({
  name: "",
});

const createFormRef = ref(null);
const createRules = {
  name: [{ required: true, message: "请输入提示词名称", trigger: "blur" }],
};

// 状态
const loading = ref(false);
const showCreateModal = ref(false);
const createLoading = ref(false);
const tableData = ref([]);

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  onChange: (page) => {
    pagination.page = page;
    loadData();
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    loadData();
  },
});

// 表格列定义
const columns = [
  {
    title: "提示词名称",
    key: "name",
    width: 200,
  },
  {
    title: "提示词ID",
    key: "id",
    width: 200,
  },
  {
    title: "状态",
    key: "status",
    width: 100,
    render: (row) => {
      // 使用字典获取状态文本
      const statusText = getDictTextByCodeValue(
        "prompt_publishing_status",
        row.status
      );
      const text =
        statusText ||
        (row.status === "1" || row.status === 1 ? "已发布" : "未发布");

      const color =
        row.status === "1" || row.status === 1 ? "#00AD76" : "#FF4B4B";

      return h("span", { style: { color } }, text);
    },
  },
  {
    title: "版本",
    key: "version",
    width: 100,
    render: (row) => {
      return row.version ? "v" + row.version : "—";
    },
  },
  {
    title: "更新时间",
    key: "updatedAt",
    width: 180,
  },
  {
    title: "创建时间",
    key: "createdAt",
    width: 180,
  },
  {
    title: "操作",
    key: "actions",
    width: 150,
    render: (row) => [
      h(
        NButton,
        {
          size: "small",
          type: "primary",
          text: true,
          style: { color: "#125EFF" },
          onClick: () => handleEdit(row),
        },
        "编辑"
      ),
      h(
        NButton,
        {
          size: "small",
          type: "error",
          text: true,
          style: "margin-left: 8px; color: #FF5F5F;",
          onClick: () => handleDelete(row),
        },
        "删除"
      ),
    ],
  },
];

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      page_size: pagination.pageSize,
      page_num: pagination.page,
      name: searchForm.name,
      status: searchForm.status,
      permission: "user",
    };
    const result = await getPromptList(params);
    console.log(result, "  e");

    tableData.value = result.data?.items;
    pagination.itemCount = result.data?.total;
  } catch (error) {
    message.error("加载数据失败");
    console.error("加载数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.page = 1;
  loadData();
};

// 创建提示词
const handleCreate = async () => {
  try {
    await createFormRef.value?.validate();
    createLoading.value = true;

    const result = await createPrompt({ name: createForm.name });
    message.success("创建成功");
    showCreateModal.value = false;
    createForm.name = "";

    // 跳转到编辑页面
    router.push({
      name: "promptEdit",
      query: { id: result.data?.id, mode: "create" },
    });
  } catch (error) {
    if (error && error.length) {
      // 表单验证错误
      return;
    }
    message.error("创建失败");
    console.error("创建提示词失败:", error);
  } finally {
    createLoading.value = false;
  }
};

// 编辑
const handleEdit = (row) => {
  router.push({
    name: "promptEdit",
    query: { id: row.id, mode: "edit" },
  });
};

// 删除
const handleDelete = (row) => {
  dialog.warning({
    title: "确认删除",
    content: `确定要删除提示词"${row.name}"吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        await deletePrompt(row.id);
        message.success("删除成功");
        loadData();
      } catch (error) {
        message.error("删除失败");
        console.error("删除提示词失败:", error);
      }
    },
  });
};

onMounted(() => {
  loadStatusOptions(); // 先加载字典数据
  loadData();
});
</script>

<style lang="less" scoped>
.n-data-table {
  flex: 1;
}

/* 输入框和下拉框样式 */

.search {
  /deep/ .n-input {
    background: #ffffff !important;
    border-radius: 8px !important;
    height: 40px !important;
  }

  /deep/.n-base-selection {
    background: #ffffff !important;
    border-radius: 8px !important;
    height: 40px !important;
  }
  /deep/ .n-base-selection .n-base-selection-label {
    height: 40px !important;
    line-height: 40px !important;
  }

  /deep/ .n-input .n-input__input-el {
    height: 40px;
    line-height: 40px;
  }
}

/* 搜索图标样式 */
.search-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-left: 1px solid #e0e0e0;
  border-radius: 0 6px 6px 0;
  margin-right: -11px;
  background: #f6f8fb;
}

/* 创建按钮样式 */
.create-btn {
  background: #125eff !important;
  border-radius: 8px !important;
  border: none !important;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  height: 40px;
  padding: 0 16px;
}

.create-btn:hover {
  background: #0f52e6 !important;
}

/* 创建提示词弹窗样式 */
.create-modal :deep(.n-card__header) {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 17px;
  color: #1f1f1f;
}

.custom-form-item :deep(.n-form-item-label) {
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 15px;
  color: #323233;
  letter-spacing: 0;
}
.custom-form-item {
  /deep/ .n-input {
    background: #ffffff !important;
    border-radius: 8px !important;
  }
}

.cancel-btn {
  width: 80px;
  height: 36px;
  background: #ffffff !important;
  border: 1px solid #ebebeb !important;
  border-radius: 10px !important;
  color: #2f3033 !important;
}
.cancel-btn:hover {
  background: #f5f5f5 !important;
}

.create-submit-btn {
  width: 80px;
  height: 36px;
  background: #125eff !important;
  border-radius: 10px !important;
  border: none !important;
}

.create-submit-btn:hover {
  background: #0f52e6 !important;
}
/deep/ .n-card-header {
  border-bottom: 1px solid #ebebeb !important;
  padding: 18px 0 16px 26px !important;
  margin-bottom: 24px;
}
/deep/ .n-card__content {
  padding-left: 26px;
  padding-bottom: 3px;
}
</style>
