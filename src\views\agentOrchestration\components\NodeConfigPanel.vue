<template>
  <!-- 右侧滑出配置面板 -->
  <div
    class="config-panel-overlay"
    v-show="visible"
    @click="handleOverlayClick"
  >
    <div class="config-panel" :class="{ 'panel-open': visible }" @click.stop>
      <!-- 面板头部 -->
      <div class="panel-header">
        <div class="header-content">
          <div class="node-info">
            <div class="node-name flex">
              <img class="w-[18px] h-[18px]" :src="formData.icon" />
              {{ formData.label }}
            </div>
          </div>
          <div class="header-actions">
            <!-- 运行节点按钮 -->
            <button
              v-if="showRunButton"
              class="run-btn"
              :class="{ running: isRunning }"
              :disabled="isRunning"
              @click="handleRunNode"
              :title="isRunning ? '运行中...' : '运行节点'"
            >
              <svg
                v-if="!isRunning"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <polygon points="5,3 19,12 5,21"></polygon>
              </svg>
              <svg
                v-else
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                class="animate-spin"
              >
                <path d="M21 12a9 9 0 11-6.219-8.56"></path>
              </svg>
            </button>
            <!-- 更多操作下拉菜单 -->
            <n-dropdown
              :options="moreOptions"
              @select="handleMoreAction"
              trigger="click"
              placement="bottom-end"
            >
              <button class="more-btn" title="更多操作">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <circle cx="5" cy="12" r="1"></circle>
                  <circle cx="12" cy="12" r="1"></circle>
                  <circle cx="19" cy="12" r="1"></circle>
                </svg>
              </button>
            </n-dropdown>
            <!-- <button class="close-btn" @click="handleClose">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button> -->
          </div>
        </div>
      </div>
      <div class="divider"></div>
      <!-- 面板内容 -->
      <div class="panel-content" v-if="node">
        <n-scrollbar style="height: calc(100vh - 140px)">
          <n-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-placement="top"
            :show-feedback="true"
            :show-label="true"
          >
            <!-- 开始节点配置 -->
            <template v-if="node.type === 'start'">
              <StartNodeConfig v-model:formData="formData" :node="node" />
            </template>

            <!-- 文本生成配置 -->
            <template v-if="node.type === 'llm'">
              <LLMNodeConfig
                v-model:formData="formData"
                :node="node"
                :variableOptions="variableOptions"
                :aggregationOptions="aggregationOptions"
              />
            </template>

            <!-- 结束节点配置 -->

            <template v-if="node.type === 'end'">
              <EndNodeConfig
                v-model:formData="formData"
                :node="node"
                :variableOptions="variableOptions"
                :aggregationOptions="aggregationOptions"
                @open-variable-management="handleOpenVariableManagement"
              />
            </template>

            <!-- 意图识别配置 -->
            <template v-if="node.type === 'question-classifier'">
              <!-- QuestionClassifierConfig -->
              <QuestionClassifierConfig
                v-model:formData="formData"
                :node="node"
                :variableOptions="variableOptions"
                :aggregationOptions="aggregationOptions"
              />
            </template>

            <!-- 知识检索配置 -->
            <template v-if="node.type === 'knowledge'">
              <KnowledgeNodeConfig
                v-model:formData="formData"
                :node="node"
                :variableOptions="variableOptions"
                :aggregationOptions="aggregationOptions"
                @handleAggregationChange="handleAggregationChange"
              />
            </template>

            <!-- 变量赋值配置 -->
            <template v-if="node.type === 'assigner'">
              <VariableAssignmentConfig
                v-model:formData="formData"
                :node="node"
                :variableOptions="variableOptions"
                :aggregationOptions="aggregationOptions"
                @handleAggregationChange="handleAggregationChange"
              />
            </template>
            <!-- 条件节点配置 -->
            <template v-if="node.type === 'condition'">
              <ConditionNodeConfig
                v-model:formData="formData"
                :node="node"
                :variableOptions="variableOptions"
                :aggregationOptions="aggregationOptions"
                @handleAggregationChange="handleAggregationChange"
              />
            </template>

            <!-- 聚合节点配置 -->
            <template v-if="node.type === 'aggregation'">
              <AggregationNodeConfig
                v-model:formData="formData"
                :node="node"
                :aggregationOptions="aggregationOptions"
                @handleAggregationChange="handleAggregationChange"
              />
            </template>

            <!-- 节点特定配置 -->
            <div class="config-section" v-if="showSpecificConfig">
              <!-- API节点配置 -->
              <template v-if="node.type === 'api'">
                <n-form-item label="请求方法" path="config.method">
                  <n-select
                    v-model:value="formData.config.method"
                    :options="methodOptions"
                    placeholder="选择请求方法"
                  />
                </n-form-item>

                <n-form-item label="接口地址" path="config.url">
                  <n-input
                    v-model:value="formData.config.url"
                    placeholder="请输入API接口地址"
                  />
                </n-form-item>

                <n-form-item label="超时时间(秒)" path="config.timeout">
                  <n-input-number
                    v-model:value="formData.config.timeout"
                    placeholder="请求超时时间"
                    :min="1"
                    :max="300"
                    :step="1"
                    style="width: 100%"
                  />
                </n-form-item>
              </template>

              <!-- 问题分类器配置 -->
              <template v-if="node.type === 'questionClassifier'">
                <n-form-item label="问题分类" path="config.categories">
                  <n-dynamic-input
                    v-model:value="formData.config.categories"
                    :on-create="createCategory"
                    #="{ index, value }"
                  >
                    <div class="flex flex-col space-y-2">
                      <div class="flex space-x-2">
                        <n-input
                          v-model:value="value.name"
                          placeholder="分类名称"
                          style="width: 30%"
                        />
                        <n-input
                          v-model:value="value.keywords"
                          placeholder="关键词(逗号分隔)"
                          style="width: 40%"
                        />
                        <n-select
                          v-model:value="value.matchRule"
                          :options="matchRuleOptions"
                          placeholder="匹配规则"
                          style="width: 30%"
                        />
                      </div>
                      <n-input
                        v-model:value="value.description"
                        placeholder="分类描述(可选)"
                        type="textarea"
                        :rows="1"
                      />
                    </div>
                  </n-dynamic-input>
                </n-form-item>

                <n-form-item label="默认分类" path="config.defaultCategory">
                  <n-input
                    v-model:value="formData.config.defaultCategory"
                    placeholder="当没有匹配的分类时使用的默认分类名称"
                  />
                </n-form-item>
              </template>
            </div>

            <!-- 执行日志部分 -->
            <!-- <div class="config-section" v-if="node && executionLogs.length > 0">
              <div class="section-header">
                <h4 class="section-title">
                  <span class="mr-2">📋</span>
                  执行日志
                  <span class="log-count-badge">{{ executionLogs.length }}</span>
                </h4>
                <button
                  class="clear-logs-btn"
                  @click="handleClearLogs"
                  title="清除所有日志"
                >
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2m3 0v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6h14zM10 11v6M14 11v6"/>
                  </svg>
                </button>
              </div>

              <div class="execution-logs">
                <div
                  v-for="log in executionLogs.slice(0, 5)"
                  :key="log.id"
                  class="log-entry"
                  :class="log.status"
                >
                  <div class="log-header">
                    <div class="log-status">
                      <span class="status-dot" :class="log.status"></span>
                      <span class="status-text">{{ getStatusText(log.status) }}</span>
                    </div>
                    <div class="log-meta">
                      <span class="duration">{{ formatDuration(log.duration) }}</span>
                      <span class="timestamp">{{ formatTimestamp(log.timestamp) }}</span>
                    </div>
                  </div>

                  <div class="log-details" v-if="log.output || log.error">
                    <div v-if="log.output" class="log-output">
                      <div class="detail-label">输出:</div>
                      <div class="detail-content">{{ formatOutput(log.output) }}</div>
                    </div>

                    <div v-if="log.error" class="log-error">
                      <div class="detail-label">错误:</div>
                      <div class="detail-content error-text">{{ log.error }}</div>
                    </div>
                  </div>
                </div>

                <div v-if="executionLogs.length > 5" class="more-logs">
                  还有 {{ executionLogs.length - 5 }} 条历史记录...
                </div>
              </div>
            </div> -->
          </n-form>
        </n-scrollbar>
      </div>

      <!-- 面板底部 -->
      <!-- <div class="panel-footer">
        <div class="footer-actions">
          <n-button @click="handleClose">取消</n-button>
          <n-button type="primary" @click="handleSave">保存配置</n-button>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted, h } from "vue";
import {
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NSwitch,
  NSlider,
  NButton,
  NInputNumber,
  NScrollbar,
  NDropdown,
  NDynamicInput,
  useMessage,
  useDialog,
  NPopover,
  NDataTable,
  NCard,
  NDivider,
  NRadio,
  NRadioGroup,
  NModal,
  NCheckbox,
  NCheckboxGroup,
  SelectGroupOption,
  SelectOption,
} from "naive-ui";
import type { FlowNode } from "@/store/modules/orchestration";
import { useOrchestrationStore } from "@/store";
import VariableSelector from "./VariableSelector.vue";
import AggregationSelector from "./AggregationSelector.vue";
import { generateId } from "@/store/modules/orchestration";
import { agentParam, museModels, optimize } from "@/api/workShop";
import StartNodeConfig from "./nodeConfigs/StartNodeConfig.vue";
import EndNodeConfig from "./nodeConfigs/EndNodeConfig.vue";
import LLMNodeConfig from "./nodeConfigs/LLMNodeConfig.vue";
import QuestionClassifierConfig from "./nodeConfigs/QuestionClassifierConfig.vue";
import VariableAssignmentConfig from "./nodeConfigs/VariableAssignmentConfig.vue";
import AggregationNodeConfig from "./nodeConfigs/AggregationNodeConfig.vue";
import ConditionNodeConfig from "./nodeConfigs/ConditionNodeConfig.vue";
import KnowledgeNodeConfig from "./nodeConfigs/KnowledgeNodeConfig.vue";
import { deepClone } from "@/utils/is";

const props = defineProps<{
  show: boolean;
  node: FlowNode | null;
}>();

const emit = defineEmits<{
  "update:show": [value: boolean];
  save: [config: any];
}>();

const message = useMessage();
const dialog = useDialog();
const formRef = ref();
const orchestrationStore = useOrchestrationStore();

// 运行状态
const isRunning = ref(false);

// 修改为更简洁的版本，因为具体的 formData 更新已经在子组件中处理
const handleAggregationChange = (value: string, variable: any, group: any) => {
  console.log("选择的聚合变量:", { value, variable, group });
  // 这里可以保留日志记录或其他全局处理逻辑
};

// 双向绑定
const visible = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

// 表单数据
const formData = ref({
  label: "",
  description: "",
  config: {},
});
var showSpecificConfig = ref(false);
const modelParameterformData = ref({
  style: "",
  temperature: "",
  topP: "",
  maxTokens: "",
});
// 模型配置弹窗是否展示
var modelParameterShow = ref(false);
// 监听节点变化，更新表单数据
watch(
  () => props.node,
  (newNode) => {
    if (newNode) {
      const config = { ...newNode.data.config } || {};
      // 意图识别节点初始化以及编辑逻辑
      if (newNode.type === "question-classifier") {
        if (!config.modelConfig) {
          config.modelConfig = {};
          config.modelConfig.model = null;
          config.modelConfig.modelName = "";
          config.modelConfig.completionParams = {};
          nextTick(() => {
            if (agentParams.value) {
              config.modelConfig.completionParams = {
                temperature: Number(agentParams.value.model_style[0].model_temp),
                maxTokens: Number(agentParams.value.model_max_length),
                topP: Number(agentParams.value.model_style[0].top_p),
                style: agentParams.value.model_style[0].model_style
              };
            }
          });
        }
        if (!config.inputType) {
          config.inputType = "0";
        }
        if (!config.classes) {
          config.classes = [
            {
              id: generateId() + "-output",
              name: "意图",
              des: "",
              isEditing: false,
            },
            {
              id: generateId() + "-output",
              name: "其他",
              des: "未命中以上意图",
              disable: true,
            },
          ];
        }
      }
      // 文本生成节点初始化以及编辑逻辑
      if (newNode.type === "llm") {
        config.systemPromptTemplate = config.systemPromptTemplate
          ? config.systemPromptTemplate
          : "";
        config.wenbenshuchu = config.wenbenshuchu
          ? config.wenbenshuchu
          : "文本输出";
        //    if (newNode.data.config?.systemPromptTemplate) {
        //   nextTick(() => {
        //     rendereditableDiv(newNode.data.config?.systemPromptTemplate);
        //   });
        // }
        if (!config.modelConfig) {
          config.modelConfig = {};
          config.modelConfig.model = null;
          config.modelConfig.modelName = "";
          config.modelConfig.completionParams = {};
          nextTick(() => {
            if (agentParams.value) {
              config.modelConfig.completionParams = {
                temperature: Number(agentParams.value.model_style[0].model_temp),
                maxTokens: Number(agentParams.value.model_max_length),
                topP: Number(agentParams.value.model_style[0].top_p),
                style: agentParams.value.model_style[0].model_style
              };
            }
          });
        }
      }

      // 为条件节点初始化默认条件
      if (newNode.type === "condition" && !config.conditions) {
        if (!config.conditionBranchArr) {
          config.conditionBranchArr = [
            {
              id: generateId() + "-output",
              name: "分支1",
              disabled: false,
              conditionArr: [
                { variable: "", operator: "等于", field: "0", value: "" },
              ],
              conditionLogic: "",
            },
            {
              id: generateId() + "-output",
              name: "分支2",
              disabled: true,
              conditionArr: [],
              conditionLogic: "",
            },
          ];
          // config.conditionLogic = "且"; // 如果条件组的逻辑操作符
        }
      }

      // 为问题分类器节点初始化默认分类
      if (newNode.type === "questionClassifier" && !config.categories) {
        config.categories = [
          {
            id: "tech_" + Date.now(),
            name: "技术问题",
            keywords: "bug,错误,技术,代码",
            description: "技术相关的问题",
            matchRule: "contains",
          },
          {
            id: "business_" + Date.now(),
            name: "业务问题",
            keywords: "需求,业务,流程,规则",
            description: "业务相关的问题",
            matchRule: "contains",
          },
        ];
        config.defaultCategory = "其他问题";
      }

      // 为聚合节点初始化默认配置
      if (newNode.type === "aggregation" && !config.aggregationVariable) {
        config.aggregationVariable = "";
      }

      console.log(newNode);

      formData.value = {
        label: newNode.data.label || "",
        icon: newNode.data.icon,
        description: newNode.data.description || "",
        config,
      };
    }
  },
  { immediate: true, deep: true }
);

// 计算属性

const statusClass = computed(() => {
  if (!props.node) return "status-idle";
  switch (props.node.data.status) {
    case "running":
      return "status-running";
    case "success":
      return "status-success";
    case "error":
      return "status-error";
    default:
      return "status-idle";
  }
});

const statusText = computed(() => {
  if (!props.node) return "空闲";
  const statusMap = {
    idle: "空闲",
    running: "运行中",
    success: "成功",
    error: "错误",
  };
  return statusMap[props.node.data.status as keyof typeof statusMap] || "未知";
});

// 是否显示运行按钮
const showRunButton = computed(() => {
  return (
    props.node &&
    ["llm", "api", "condition", "questionClassifier"].includes(props.node.type)
  );
});

// 执行日志相关
const executionLogs = computed(() => {
  return props.node?.data.executionLogs || [];
});

// 选项数据
const modelOptions = ref([]);
const variableOptions = [
  { label: "变量", value: "0" },
  { label: "固定", value: "1" },
];
// 获取数据类型标签文本
const getDataTypeLabel = (dataType: string) => {
  const labelMap: Record<string, string> = {
    string: "文本",
    number: "数字",
    boolean: "布尔",
    array: "数组",
    object: "对象",
  };
  return labelMap[dataType] || dataType;
};

// 从store动态获取所有节点变量
const nodeVariables = computed(() => {
  return orchestrationStore
    .getVariablesByType("nodeVariable")
    .map((variable) => ({
      id: variable.id,
      name: variable.name,
      identifier: variable.name,
      value: variable.value || "",
      type: getDataTypeLabel(variable.valueType),
      nodeId: variable.nodeId,
      nodeName: variable.nodeName,
      nodeType: variable.nodeType,
      expanded: false,
      valueType: variable.valueType
    }));
});

// 从store动态获取所有环境变量
const envVariables = computed(() => {
  return orchestrationStore
    .getVariablesByType("envVariable")
    .map((variable) => ({
      name: variable.name,
      type: getDataTypeLabel(variable.valueType),
      id: variable.id,
      nodeName: variable.nodeName,
      nodeId: variable.nodeId,
      valueType: variable.valueType,
    }));
});
// 聚合节点选项数据
// 获取节点的直接前置节点（连接到当前节点的节点）
const getDirectPreviousNodes = (nodeId: string) => {
  if (!orchestrationStore.currentFlow) return [];
  return orchestrationStore.currentFlow.edges
    .filter((edge) => edge.target === nodeId)
    .map((edge) => edge.source);
};

// 递归获取所有前置节点（包括间接连接的节点）
const getAllPreviousNodes = (
  nodeId: string,
  visited: Set<string> = new Set()
): string[] => {
  if (!orchestrationStore.currentFlow) return [];

  if (visited.has(nodeId)) return [];
  visited.add(nodeId);

  const directPrevious = getDirectPreviousNodes(nodeId);
  const allPrevious = new Set(directPrevious);

  directPrevious.forEach((prevNodeId) => {
    const indirectPrevious = getAllPreviousNodes(prevNodeId, visited);
    indirectPrevious.forEach((node) => allPrevious.add(node));
  });

  return Array.from(allPrevious);
};

// 检查节点是否与其他节点连接
const isNodeConnected = (nodeId: string) => {
  const previousNodes = getDirectPreviousNodes(nodeId);
  const nextNodes = orchestrationStore.getNextNodes(nodeId);
  return previousNodes.length > 0 || nextNodes.length > 0;
};

// 响应式聚合选项数据
const aggregationOptions = computed(() => {
  if (!props.node) return [];

  const allPreviousNodes = getAllPreviousNodes(props.node.id);
  const isConnected = isNodeConnected(props.node.id);

  if (!isConnected) {
    // 没有连接其他节点，只返回环境变量
    if (envVariables.value.length === 0) return [];

    return [
      {
        label: "环境变量",
        value: "envVariable",
        variables: envVariables.value,
      },
    ];
  }

  // 连接了其他节点，获取所有前置节点的输出变量
  const nodeVariableList = nodeVariables.value
    .filter(
      (variable) =>
        allPreviousNodes.includes(variable.nodeId || "") &&
        variable.nodeId !== props.node?.id &&
        variable.nodeType !== "start"
    )
    .reduce((groups, variable) => {
      const key = variable.nodeId || "";
      if (!groups[key]) {
        groups[key] = {
          label: variable.nodeName || "",
          value: key,
          variables: [],
        };
      }
      groups[key].variables.push({
        name: variable.name,
        type: variable.type,
        id: variable.id,
        nodeName: variable.nodeName,
        nodeId: variable.nodeId,
        valueType: variable.valueType,
      });
      return groups;
    }, {} as Record<string, any>);

  const startNodeVariables = nodeVariables.value.filter(
    (variable) =>
      allPreviousNodes.includes(variable.nodeId || "") &&
      variable.nodeId !== props.node?.id &&
      variable.nodeType === "start"
  );

  const startNodeGroups = startNodeVariables.reduce((groups, variable) => {
    const key = variable.nodeId || "";
    if (!groups[key]) {
      groups[key] = {
        label: variable.nodeName || "",
        value: key,
        variables: [],
      };
    }
    groups[key].variables.push({
      name: variable.name,
      type: variable.type,
      id: variable.id,
      nodeName: variable.nodeName,
      nodeId: variable.nodeId,
      valueType: variable.valueType,
    });
    return groups;
  }, {} as Record<string, any>);

  const result = [];

  // 添加环境变量
  if (envVariables.value.length > 0) {
    result.push({
      label: "环境变量",
      value: "envVariable",
      variables: envVariables.value,
    });
  }

  // 添加开始节点变量
  result.push(...Object.values(startNodeGroups));

  // 添加其他节点变量
  result.push(...Object.values(nodeVariableList));

  return result;
});

// 监听store变化，自动更新数据
watch(
  () => [
    orchestrationStore.currentFlow?.variables,
    props.node?.id,
    visible.value,
  ],
  () => {
    // 当store中的变量变化时，computed属性会自动更新
    console.log("变量数据已更新");
  },
  { deep: true }
);

// 移除原有的watch(visible)逻辑，因为aggregationOptions现在是computed属性
// 原有的watch逻辑可以简化为只处理保存操作
watch(visible, (newVal) => {
  if (!newVal && props.node) {
    console.log("关闭配置弹窗");
    handleSave();
  }
});

// const modelParameterOptions = ref([
//   { key: "创意", value: "0" },
//   { key: "平衡", value: "1" },
//   { key: "精准", value: "2" },
// ]);
var agentParams = ref(null);
const methodOptions = [
  { label: "GET", value: "GET" },
  { label: "POST", value: "POST" },
  { label: "PUT", value: "PUT" },
  { label: "DELETE", value: "DELETE" },
];

// 问题分类器匹配规则选项
const matchRuleOptions = [
  { label: "包含匹配", value: "contains" },
  { label: "精确匹配", value: "exact" },
  { label: "正则匹配", value: "regex" },
];

// 更多操作菜单选项
const moreOptions = [
  {
    label: "复制节点",
    key: "duplicate",
    icon: () => "📋",
  },
  {
    label: "删除节点",
    key: "delete",
    icon: () => "🗑️",
  },
];

// 表单验证规则
const formRules = {
  // label: [{ required: true, message: "请输入节点名称", trigger: "blur" }],
  // "config.modelConfig.model": [
  //   { required: true, message: "请选择模型", trigger: "blur" },
  // ],
  // "config.inputValue": [
  //   { required: true, message: "请输入变量名称", trigger: "blur" },
  // ],
};

// 创建新分类的函数
const createCategory = () => {
  return {
    id: `category_${Date.now()}`,
    name: "",
    keywords: "",
    description: "",
    matchRule: "contains",
  };
};

// 关键词字符串转数组
const keywordsToArray = (keywords: string) => {
  return keywords
    .split(",")
    .map((k) => k.trim())
    .filter((k) => k.length > 0);
};

// 关键词数组转字符串
const arrayToKeywords = (keywords: string[]) => {
  return keywords.join(", ");
};

// 方法
const handleClose = () => {
  visible.value = false;
};

const handleOverlayClick = () => {
  handleClose();
};

const handleSave = async () => {
  try {
    await formRef.value?.validate();
    console.log(formData.value);
    emit("save", formData.value);
    handleClose();
  } catch (error) {
    message.error("请检查表单输入");
  }
};

// 运行节点
const handleRunNode = async () => {
  if (!props.node || isRunning.value) return;

  try {
    isRunning.value = true;
    await orchestrationStore.runNode(props.node.id);
    message.success("节点运行成功");
  } catch (error) {
    console.error("节点运行失败:", error);
    message.error("节点运行失败");
  } finally {
    isRunning.value = false;
  }
};

// 处理更多操作菜单
const handleMoreAction = (key: string) => {
  if (!props.node) return;

  switch (key) {
    case "delete":
      handleDeleteNode();
      break;
    case "duplicate":
      handleDuplicateNode();
      break;
  }
};

// 获取当前节点之后的所有节点（基于执行顺序）
const getSubsequentNodes = (currentNodeId: string): Node[] => {
  if (!orchestrationStore.currentFlow || !orchestrationStore.currentNodes) {
    return [];
  }

  const allNodes = orchestrationStore.currentNodes;
  const allEdges = orchestrationStore.currentFlow.edges || [];

  // 使用广度优先搜索找到当前节点之后的所有节点
  const visited = new Set<string>();
  const queue = [currentNodeId];
  const subsequentNodes: Node[] = [];

  // 从当前节点开始，找到所有可达的后续节点
  while (queue.length > 0) {
    const nodeId = queue.shift()!;

    // 找到从当前节点出发的所有边
    const outgoingEdges = allEdges.filter((edge) => edge.source === nodeId);

    for (const edge of outgoingEdges) {
      if (!visited.has(edge.target)) {
        visited.add(edge.target);
        queue.push(edge.target);

        // 找到目标节点
        const targetNode = allNodes.find((node) => node.id === edge.target);
        if (targetNode && targetNode.id !== currentNodeId) {
          subsequentNodes.push(targetNode);
        }
      }
    }
  }

  return subsequentNodes;
};

// 删除节点
const handleDeleteNode = () => {
  if (!props.node) return;

  // 检查是否为开始/结束节点
  if (props.node.type === "start" || props.node.type === "end") {
    message.warning("开始节点和结束节点不能删除");
    return;
  }

  dialog.warning({
    title: "删除确认",
    content: `确定要删除节点 "${props.node.data.label}" 吗？此操作不可撤销。`,
    positiveText: "确定删除",
    negativeText: "取消",
    onPositiveClick:async () => {
      if (props.node) {
        // 获取当前节点之后的所有节点
  const subsequentNodes = getSubsequentNodes(props.node.id);
  let variableObj = orchestrationStore
    .getVariablesByType("nodeVariable")
    .find((item) => item.nodeId === props.node.id);

  subsequentNodes.forEach((item) => {
    var needUpdate = false;
    var newConfig = deepClone(item.data.config);
    if (item.type == "assigner") {
      newConfig.items.forEach((variable) => {
        if (variable.key == variableObj.id) {
          variable.key = "";
          needUpdate = true;
        }
        if (variable.value == variableObj.id) {
          variable.value = "";
          needUpdate = true;
        }
      });
    } else if (item.type == "end") {
      if (
        newConfig.huifuneirong &&
        typeof newConfig.huifuneirong === "string"
      ) {
        const pattern = new RegExp("\\{\\{" + variableObj.id + "\\}\\}", "g");
        const originalContent = newConfig.huifuneirong;
        newConfig.huifuneirong = newConfig.huifuneirong.replace(pattern, "");
        // 如果内容有变化，标记需要更新
        if (newConfig.huifuneirong !== originalContent) {
          needUpdate = true;
          console.log("处理后的huifuneirong:", newConfig.huifuneirong);
        }
      }
      if (newConfig.jianyiwenti) {
        newConfig.jianyiwenti.forEach((jianyiitem) => {
          if (jianyiitem.value == variableObj.id) {
            jianyiitem.value = "";
            needUpdate = true;
          }
        });
      }
    } else if (item.type == "question-classifier") {
      if (newConfig.inputValue == variableObj.id) {
        newConfig.inputKey = "";
        newConfig.inputValue = "";
        needUpdate = true;
      }
    } else if (item.type == "llm") {
      if (
        newConfig.systemPromptTemplate &&
        typeof newConfig.systemPromptTemplate === "string"
      ) {
        const pattern = new RegExp("\\{\\{" + variableObj.id + "\\}\\}", "g");
        const originalContent = newConfig.systemPromptTemplate;
        newConfig.systemPromptTemplate = newConfig.systemPromptTemplate.replace(
          pattern,
          ""
        );
        // 如果内容有变化，标记需要更新
        if (newConfig.systemPromptTemplate !== originalContent) {
          needUpdate = true;
          console.log(
            "处理后的systemPromptTemplate:",
            newConfig.systemPromptTemplate
          );
        }
      }
    } else if (item.type == "knowledge") {
      if (newConfig.jiansuotypevalue == variableObj.id) {
        newConfig.jiansuotypevalue = "";
        needUpdate = true;
      }
    } else if (item.type == "aggregation") {
      newConfig.aggregationlist.forEach((variable) => {
        if (variable.value == variableObj.id) {
          variable.value = "";
          needUpdate = true;
        }
      });
    }

    if (needUpdate) {
      let config = {
        icon: item.data.icon,
        description: item.data.description,
        label: item.data.label,
        config: newConfig,
      };
      orchestrationStore.updateNodeData(item.id, config);
    }
  });
  await nextTick()
        orchestrationStore.removeNode(props.node.id);
        let variable = orchestrationStore
          .getVariablesByType("nodeVariable")
          .find((item) => item.nodeId === props.node.id);
        if (variable) {
          orchestrationStore.deleteVariable(variable.id);
        }
             orchestrationStore.saveFlowToBackend()
        handleClose();
        message.success("节点删除成功");
      }
    },
  });
};

// 复制节点
const handleDuplicateNode = () => {
  if (!props.node) return;
  try {
    orchestrationStore.duplicateNode(props.node.id);
    message.success("节点复制成功");
  } catch (error) {
    console.error("节点复制失败:", error);
    message.error("节点复制失败");
  }
};

// 清除执行日志
const handleClearLogs = () => {
  if (!props.node) return;

  dialog.warning({
    title: "清除确认",
    content: "确定要清除所有执行日志吗？此操作不可撤销。",
    positiveText: "确定清除",
    negativeText: "取消",
    onPositiveClick: () => {
      if (props.node) {
        orchestrationStore.clearNodeExecutionLogs(props.node.id);
        message.success("执行日志已清除");
      }
    },
  });
};

// 日志相关工具方法
const getStatusText = (status: string): string => {
  const statusMap = {
    idle: "空闲",
    running: "运行中",
    success: "成功",
    error: "失败",
  };
  return statusMap[status as keyof typeof statusMap] || "未知";
};

const formatDuration = (duration: number): string => {
  if (duration < 1000) {
    return `${duration}ms`;
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(1)}s`;
  } else {
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  }
};

const formatTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

const formatOutput = (output: any): string => {
  if (typeof output === "string") return output;
  if (typeof output === "object") {
    const str = JSON.stringify(output);
    return str.length > 200 ? str.substring(0, 200) + "..." : str;
  }
  return String(output);
};

const initdatafun = async () => {
  const models = await museModels();
  const res = await agentParam();
  console.log(res);
  // modelParameterOptions.value = res.data.model_style;
  agentParams.value = res.data;
  modelOptions.value = models.data.map((item: any) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
};
onMounted(() => {
  initdatafun();
});
</script>

<style scoped lang="less">
.config-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: opacity 0.3s ease;
}

.config-panel {
  position: fixed;
  top: 0;
  right: -500px;
  width: 540px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
  width: 540px;
  padding: 0 16px;
}

.config-panel.panel-open {
  right: 0;
}

.panel-header {
  /* padding: 20px 24px 16px; */
  /* border-bottom: 1px solid #e2e8f0; */
  /* background: #fafbfc; */
  height: 40px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.node-info {
  flex: 1;
  display: flex;
  align-items: center;
}
.nodeIcon {
  width: 32px;
  height: 32px;
  margin-right: 8px;
}
.node-name {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  img {
    margin-right: 11px;
  }
}
.node-type-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
}

.panel-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.node-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-idle {
  background: #d9d9d9;
}
.status-running {
  background: #faad14;
  animation: pulse 2s infinite;
}
.status-success {
  background: #52c41a;
}
.status-error {
  background: #ff4d4f;
}

.run-btn,
.more-btn,
.close-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #64748b;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.run-btn:hover,
.more-btn:hover,
.close-btn:hover {
  background: #f1f5f9;
  color: #1a202c;
}

.run-btn {
  color: #125eff;
}

.run-btn:hover {
  background: #e6f0ff;
  color: #0d47a1;
}

.run-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.run-btn.running {
  color: #faad14;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.panel-content {
  flex: 1;
  /* padding: 24px; */
  overflow: hidden;
}

.config-section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  display: flex;
  align-items: center;
}

.panel-footer {
  padding: 20px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafbfc;
  flex-shrink: 0;
  border-radius: 0 0 0 12px; // 左下角圆角
  margin: 0 -16px; // 抵消父容器的padding
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  :deep(.n-button) {
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    min-height: 36px;

    // 取消按钮样式
    &:not([type]) {
      min-width: 88px;
      border: 1px solid #e0e0e0;
      background: #ffffff;
      color: #666666;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

      &:hover {
        border-color: #125eff;
        color: #125eff;
        box-shadow: 0 2px 6px rgba(18, 94, 255, 0.15);
      }

      &:active {
        transform: translateY(1px);
      }
    }

    // 保存按钮样式
    &[type="info"] {
      min-width: 108px;
      box-shadow: 0 2px 6px rgba(18, 94, 255, 0.2);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

/* 执行日志样式 */
.log-count-badge {
  background: #1890ff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  margin-left: 8px;
}

.clear-logs-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #64748b;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-logs-btn:hover {
  background: #f1f5f9;
  color: #ff4d4f;
}

.execution-logs {
  max-height: 300px;
  overflow-y: auto;
}

.log-entry {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.log-entry:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.log-entry.success {
  border-left: 3px solid #52c41a;
}

.log-entry.error {
  border-left: 3px solid #ff4d4f;
}

.log-entry.running {
  border-left: 3px solid #faad14;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.status-dot.success {
  background: #52c41a;
}

.status-dot.error {
  background: #ff4d4f;
}

.status-dot.running {
  background: #faad14;
}

.status-text {
  font-weight: 500;
  font-size: 12px;
}

.log-meta {
  display: flex;
  gap: 8px;
  font-size: 11px;
  color: #64748b;
}

.log-details {
  margin-top: 8px;
}

.log-output,
.log-error {
  margin-bottom: 6px;
}

.detail-label {
  font-size: 11px;
  color: #64748b;
  margin-bottom: 2px;
  font-weight: 500;
}

.detail-content {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 11px;
  font-family: "Courier New", monospace;
  word-break: break-all;
  max-height: 80px;
  overflow-y: auto;
}

.log-error .detail-content {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.error-text {
  color: #dc2626;
}

.more-logs {
  text-align: center;
  color: #64748b;
  font-size: 12px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 4px;
  margin-top: 8px;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
