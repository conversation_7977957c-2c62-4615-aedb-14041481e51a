<script setup lang="ts">
import { computed, onMounted, ref,nextTick } from 'vue'
import {
  NButton,
  NCard,
  NDivider,
  NDropdown,
  NForm,
  NFormItem,
  NIcon,
  NInput,
  NModal,
  useMessage,
} from 'naive-ui'
// 使用简单的文本图标替代，避免依赖问题

// 导入组件
import NodePanel from './components/NodePanel.vue'
import FlowCanvas from './components/FlowCanvas.vue'
import NodeConfigPanel from './components/NodeConfigPanel.vue'
import TestRunPage from './components/testRunPage.vue'
import VariableManagementPanel from './components/VariableManagementPanel.vue'
import FlowManagerModal from './components/FlowManagerModal.vue'
import FlowBackupModal from './components/FlowBackupModal.vue'

import { SvgIcon } from '@/components/common'

// 导入store
import { useOrchestrationStore } from '@/store'
import { useRouter,useRoute } from 'vue-router'
import { creatWorkflowApi,workflowDetailApi,updateWorkflowApi } from '@/api/agentOrchestration'
import {
	updateAgent,
  editShop
} from "@/api/workShop";

const router = useRouter()
const route = useRoute();

const message = useMessage()
const orchestrationStore = useOrchestrationStore()
const params = ref(JSON.parse(route.query.params as string || ''));
// 响应式数据
const searchValue = ref('')
const nodeSearchValue = ref('')
const showFlowManager = ref(false)
const showCreateFlow = ref(false)
const showFlowBackup = ref(false)
const showVariableManagement = ref(false)
const createFlowFormRef = ref()

// 合并的菜单选项
const allMenuOptions = computed(() => [
  // 导入选项组
  {
    label: '导入流程文件',
    key: 'import-file'
  },
  // 分割线
  {
    type: 'divider'
  },
  // 导出选项组
  {
    label: '导出当前流程',
    key: 'export-current',
    disabled: !orchestrationStore.currentFlow
  },
  // {
  //   label: '保存到后端',
  //   key: 'export-backend',
  //   disabled: !orchestrationStore.currentFlow
  // },
  // {
  //   label: '导出为模板',
  //   key: 'export-template',
  //   disabled: !orchestrationStore.currentFlow
  // },
  // // 分割线
  // {
  //   type: 'divider'
  // },
  // // 同步选项
  // {
  //   label: '同步数据',
  //   key: 'sync-backend',
  //   disabled: !orchestrationStore.currentFlow
  // }
])

var nameEditStatus=ref(false)
var flowName=ref('')
// 新建流程表单
const createFlowForm = ref({
  name: '',
  description: '',
})

const createFlowRules = {
  name: [{ required: true, message: '请输入流程名称', trigger: 'blur' }],
}

// 计算属性
const newFlowFrom = computed(() => orchestrationStore.getNewFlowFrom)
const currentFlow = computed(() => orchestrationStore.currentFlow)
const currentNodes = computed(() => orchestrationStore.currentNodes)
const currentEdges = computed(() => orchestrationStore.currentEdges)
const selectedNode = computed(() => orchestrationStore.selectedNode)
const isFlowRunning = computed(() => orchestrationStore.isRunning)

// 后端交互状态
const isBackendLoading = computed(() => orchestrationStore.isBackendLoading)
const isBackendSyncing = computed(() => orchestrationStore.isBackendSyncing)
const backendSyncStatus = computed(() => orchestrationStore.backendSyncStatus)
const lastSyncTime = computed(() => orchestrationStore.lastSyncTime)
const lastBackendError = computed(() => orchestrationStore.lastBackendError)
const needsSync = computed(() => orchestrationStore.needsSync)
const isConfigModalVisible = computed({
  get: () => orchestrationStore.isConfigModalVisible,
  set: value => orchestrationStore.setConfigModalVisible(value),
})
var isTestRunPageVisible = ref(false)
const changeTestRunPageVisible = (status:boolean)=>{
  if (!currentFlow.value) {
    message.warning('请先创建或加载一个流程')
    return
  }
  isTestRunPageVisible.value = status
}
var renamefun =(status:Boolean)=>{
  // 重命名函数
  if(status){
    // 开启编辑状态
    nameEditStatus.value = true
    flowName.value = currentFlow.value.name;
  }
  else{
    // 关闭编辑状态
    nameEditStatus.value = false
    orchestrationStore.renameFlow(currentFlow.value.id,flowName.value)
    flowName.value = ''
  }
}
// 方法
const createNewFlow = () => {
  showCreateFlow.value = true
}

const handleUndo = () => {
  const success = orchestrationStore.undo()
  if (success)
    message.success('撤销成功')
  else message.warning('没有可撤销的操作')
}

const handleRedo = () => {
  const success = orchestrationStore.redo()
  if (success)
    message.success('重做成功')
  else message.warning('没有可重做的操作')
}
const creatworkflowFun=async ()=>{
   var graph= await orchestrationStore.saveFlowToBackend(false)
  if(!graph){
    message.warning('初始化失败')
    return
  }
  creatWorkflowApi({graph:JSON.stringify(graph),name:currentFlow.value.name,category:'0',status:'0'}).then(async (res)=>{
 orchestrationStore.updateflowId(res.data.id)
 await updateAgent({...params.value,workflowId:res.data.id}, params.value.id as string);
  })
}
const handleCreateFlow = async () => {
  try {
    await createFlowFormRef.value?.validate()
    orchestrationStore.createNewFlow(
      createFlowForm.value.name,
      createFlowForm.value.description,
    )
    showCreateFlow.value = false
    createFlowForm.value = { name: '', description: '' }
    // message.success('流程创建成功')
     nextTick(()=>{
      creatworkflowFun()
     })
  }
  catch (error) {
    console.error('表单验证失败:', error)
  }
}

const saveCurrentFlow = () => {
  if (currentFlow.value) {
    orchestrationStore.saveCurrentFlow()
    message.success('流程保存成功')
  }
}

// 统一的菜单处理
const handleMenuSelect = (key: string) => {
  // 导入选项
  if (key.startsWith('import-')) {
    switch (key) {
      case 'import-file':
        importFromFile()
        break
      case 'import-backup':
        showFlowBackup.value = true
        break
      case 'import-template':
        importTemplate()
        break
    }
  }
  // 导出选项
  else if (key.startsWith('export-')) {
    switch (key) {
      case 'export-current':
        exportCurrentFlow()
        break
      case 'export-backend':
        exportToBackend()
        break
      case 'export-all':
        exportAllFlows()
        break
      case 'export-template':
        exportAsTemplate()
        break
    }
  }
  // 同步选项
  else if (key.startsWith('sync-')) {
    switch (key) {
      case 'sync-backend':
        syncWithBackend()
        break
    }
  }
}

const exportCurrentFlow = () => {
  if (currentFlow.value) {
    const flowData = orchestrationStore.exportFlow()
    if (flowData) {
      downloadFile(
        JSON.stringify(flowData, null, 2),
        `${flowData.name}.json`,
        'application/json',
      )
      message.success('当前流程导出成功')
    }
  }
}

const exportAllFlows = () => {
  const allFlows = orchestrationStore.exportAllFlows()
  if (allFlows.length > 0) {
    const exportData = {
      version: '1.0',
      exportTime: new Date().toISOString(),
      flows: allFlows,
    }
    downloadFile(
      JSON.stringify(exportData, null, 2),
      `智能体编排流程_${new Date().toISOString().split('T')[0]}.json`,
      'application/json',
    )
    message.success(`成功导出 ${allFlows.length} 个流程`)
  }
  else {
    message.warning('没有可导出的流程')
  }
}

const exportAsTemplate = () => {
  if (currentFlow.value) {
    const flowData = orchestrationStore.exportFlow()
    if (flowData) {
      // 创建模板数据，移除运行时状态
      const templateData = {
        ...flowData,
        id: 'template',
        name: `${flowData.name}_模板`,
        nodes: flowData.nodes.map(node => ({
          ...node,
          data: {
            ...node.data,
            status: 'idle',
          },
        })),
        isTemplate: true,
        templateVersion: '1.0',
      }

      downloadFile(
        JSON.stringify(templateData, null, 2),
        `${templateData.name}.json`,
        'application/json',
      )
      message.success('模板导出成功')
    }
  }
}

// ==================== 后端交互函数 ====================

/**
 * 保存到后端
 */
const exportToBackend = async () => {
  if (!currentFlow.value) {
    message.warning('没有可保存的流程')
    return
  }

  try {
      orchestrationStore.saveFlowToBackend()
    message.success('流程已保存到后端')
  } catch (error) {
    console.error('保存到后端失败:', error)
    message.error(`保存失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}



/**
 * 同步数据
 */
const syncWithBackend = async () => {
  if (!currentFlow.value) {
    message.warning('没有可同步的流程')
    return
  }

  try {
    await orchestrationStore.syncWithBackend()
    message.success('数据同步成功')
  } catch (error) {
    console.error('同步失败:', error)
    message.error(`同步失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// ==================== 同步状态辅助函数 ====================

/**
 * 获取同步状态的CSS类
 */
const getSyncStatusClass = () => {
  if (isBackendSyncing.value) return 'syncing'
  if (backendSyncStatus.value === 'success') return 'success'
  if (backendSyncStatus.value === 'error') return 'error'
  if (needsSync.value) return 'warning'
  return 'idle'
}

/**
 * 获取同步状态的提示文本
 */
const getSyncStatusTitle = () => {
  if (isBackendSyncing.value) return '正在同步...'
  if (backendSyncStatus.value === 'success') return '同步成功'
  if (backendSyncStatus.value === 'error') return `同步失败: ${lastBackendError.value || '未知错误'}`
  if (needsSync.value) return '需要同步'
  return '已同步'
}

/**
 * 格式化同步时间
 */
const formatSyncTime = (timeString: string) => {
  const time = new Date(timeString)
  const now = new Date()
  const diff = now.getTime() - time.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return time.toLocaleDateString()
}

const importFromFile = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target?.result as string)
console.log(data);

          // 检查是否是批量导出的数据
          if (data.flows && Array.isArray(data.flows)) {
            orchestrationStore.importFlows(data.flows)
            message.success(`成功导入 ${data.flows.length} 个流程`)
          }
          else {
            // 单个流程导入
            orchestrationStore.importFlow(data)
            message.success('流程导入成功')
          }
        }
        catch (error) {
          message.error('文件格式错误或数据无效')
        }
      }
      reader.readAsText(file)
    }
  }
  input.click()
}

const importTemplate = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const templateData = JSON.parse(e.target?.result as string)

          if (templateData.isTemplate) {
            // 从模板创建新流程
            const newFlow = {
              ...templateData,
              id: undefined, // 让系统生成新ID
              name: templateData.name.replace('_模板', ''),
              isTemplate: false,
            }
            delete newFlow.templateVersion

            orchestrationStore.importFlow(newFlow)
            message.success('模板导入成功')
          }
          else {
            message.warning('这不是一个有效的模板文件')
          }
        }
        catch (error) {
          message.error('模板文件格式错误')
        }
      }
      reader.readAsText(file)
    }
  }
  input.click()
}

// 文件下载工具函数
const downloadFile = (
  content: string,
  filename: string,
  contentType: string,
) => {
  const blob = new Blob([content], { type: contentType })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  a.click()
  URL.revokeObjectURL(url)
}

const handleNodeDragStart = (nodeType: string) => {
  // 节点拖拽开始处理
  console.log('Node drag start:', nodeType)
}

const handleCanvasDrop = (event: DragEvent) => {
  // 画布拖放处理
  console.log('Canvas drop:', event)
}

const handleCanvasDragOver = (event: DragEvent) => {
  // 画布拖拽悬停处理
  event.preventDefault()
}

const handleNodeClick = (nodeId: string) => {
  orchestrationStore.setSelectedNode(nodeId)
  orchestrationStore.setConfigModalVisible(true)
}

const handleNodeConfigSave = async (config: any) => {
  console.log(selectedNode.value);
  console.log(config);
  
  if (selectedNode.value) {
    orchestrationStore.updateNodeData(selectedNode.value.id, config)
    orchestrationStore.saveFlowToBackend()
    message.success('节点配置保存成功')
  }
}

const handleLoadFlow = (flowId: string) => {
  orchestrationStore.loadFlow(flowId)
  showFlowManager.value = false
  message.success('流程加载成功')
}

const handleDeleteFlow = (flowId: string) => {
  orchestrationStore.deleteFlow(flowId)
  message.success('流程删除成功')
}

// 画布控制方法
const fitView = () => {
  // TODO: 实现适应视图功能
  console.log('Fit view')
}

const zoomIn = () => {
  // TODO: 实现放大功能
  console.log('Zoom in')
}

const zoomOut = () => {
  // TODO: 实现缩小功能
  console.log('Zoom out')
}



// 变量管理相关方法
const openVariableManagement = () => {
  showVariableManagement.value = true
}

// 注意：新的变量管理架构中，变量作为流程数据的一部分
// 变量的修改会自动保存到本地，最终通过"保存到后端"按钮一起提交
// 不再需要单独的变量保存方法

const resetFlow = () => {
  orchestrationStore.resetAllNodeStatus()
  message.info('流程状态已重置')
}
const jumpPage = (url?: string) => {
  if (url) {
    router.replace(url);
  } else {
    router.go(-1);
  }
}
const initDate = async () => {
 if(route.query.edit=='true'){
workflowDetailApi({id:params.value.workflowId}).then((res:any)=>{
  if(res.code==0){
    const graph = JSON.parse(res.data.graph)
        orchestrationStore.importBackendFlow(graph)
  }else{
    message.error(res.msg || res.message)
  }
  
})
 }else{
      //新增模式
    createFlowForm.value.name=newFlowFrom.value.name
    createFlowForm.value.description=newFlowFrom.value.description
  //  调试阶段，防止多次创建 暂时关闭新增函数
    handleCreateFlow()
 }

  // if(router.currentRoute.value.query.id){
  //   //编辑模式
  //   // 根据id调用接口获取json后存入store
  //     let jsonurl="";
  //     if(router.currentRoute.value.query.name=='知识检索问答'){
  //       jsonurl="/zhishijiansuo.json"
  //     }else{
  //       jsonurl="/qinggan.json"
  //     }
  //       fetch(jsonurl)
  //       .then(response => response.json())
  //       .then(data => {
  //         console.log(data)
  //       orchestrationStore.importFlow(data)
  //       });


  // }else{
  //   //新增模式
  //   createFlowForm.value.name=newFlowFrom.value.name
  //   createFlowForm.value.description=newFlowFrom.value.description
  // //  调试阶段，防止多次创建 暂时关闭新增函数
  //   handleCreateFlow()
  // }


}
const releaseFun=()=>{
  	//发布
	let	obj = {
			status: 3,
			// agentId:params.value.id,
			releaseNotes: '发布说明',
			businessId:"1808802434961661953",
			category: "0",
      autoReview:'1',
			agentVisitConf: {
				apiVisitStatus: 0,
				publicVisitStatus:  0,
			},
		};
    	editShop(obj, params.value.id, 0)
			.then((res: any) => {
				console.log(res);
        if(res.code==0){
          message.success('发布成功')
        }
			
			})
}
onMounted(async () => {
  // 组件挂载时的初始化逻辑
  console.log('AgentOrchestration page mounted')

  // 初始化基础数据
  initDate()

  // 自动从后端加载默认流程
  try {
    await orchestrationStore.autoLoadDefaultFlow()
    console.log('页面初始化：自动加载流程完成')
  } catch (error) {
    // 自动加载失败不影响页面正常使用
    console.warn('页面初始化：自动加载流程失败，将显示空白画布', error)
  }
})
</script>

<template>
  <!-- 全屏画布容器 -->
  <div class="fullscreen-canvas-container">
    <!-- 全屏画布 -->
    <div class="fullscreen-canvas">
      <FlowCanvas
        @node-click="handleNodeClick"
        @canvas-drop="handleCanvasDrop"
        @canvas-drag-over="handleCanvasDragOver"
      />
    </div>

    <!-- 浮动左上角智能体标题 -->
    <div class="floating-title">
      <div class="title-content">
        <div class="back-button" @click="jumpPage('/workShopPage')">
          <img src="@/assets/workShopPage/leftarrow.png" />
        </div>

        <div v-if="!nameEditStatus" class="flex items-center">
          <p class="text-[20px] ml-4 title-text">
            {{ currentFlow?.name }}
          </p>
              <!-- 
          <SvgIcon class="cursor-pointer ml-[14px] text-[26px] text-[#999]" icon="line-md:edit" @click="renamefun(true)" />
        -->
        </div>
        <div v-else class="flex items-center ml-[8px]">
          <NInput
            v-model:value="flowName"
          />
          <SvgIcon class="cursor-pointer ml-[4px] text-[32px] text-[#999]" icon="line-md:confirm" @click="renamefun(false)" />
        </div>


      </div>
      <div class="close-button" @click="jumpPage()">
        <span>×</span>
      </div>
    </div>

    <!-- 浮动左侧节点面板 -->
    <div class="floating-node-panel">
      <div class="panel-header">
        <div class="panel-title">基础节点</div>
      </div>
      <div class="panel-content">
        <NodePanel
          :search-value="nodeSearchValue"
          @node-drag-start="handleNodeDragStart"
        />
      </div>
    </div>

    <!-- 浮动右上角工具栏 -->
    <div class="floating-toolbar">
      <div class="toolbar-actions">
        <div class="action-group">
          <img
            class="w-[24px] h-[18px] cursor-pointer"
            src="@/assets/agentOrchestration/bianliang.png"
            @click="openVariableManagement"
            title="变量管理"
          >
        </div>

        <!-- 同步状态指示器 -->
        <div class="action-group sync-status" v-if="currentFlow">
          <div class="sync-indicator" :class="getSyncStatusClass()" :title="getSyncStatusTitle()">
            <div class="sync-icon" v-if="isBackendSyncing">⟳</div>
            <div class="sync-icon" v-else-if="backendSyncStatus === 'success'">✓</div>
            <div class="sync-icon" v-else-if="backendSyncStatus === 'error'">✗</div>
            <div class="sync-icon" v-else-if="needsSync">⚠</div>
            <div class="sync-icon" v-else>○</div>
          </div>
          <div class="sync-text" v-if="lastSyncTime">
            {{ formatSyncTime(lastSyncTime) }}
          </div>
        </div>

        <div class="divider"></div>

        <div class="action-group">
          <NButton
            type="info"
            ghost
            color="#125EFF"
            :disabled="!currentFlow || isFlowRunning"
            :loading="isFlowRunning"
            @click="changeTestRunPageVisible(true)"
            size="small"
          >
            <img class="w-[14px] h-[14px] mr-[6px]" src="@/assets/agentOrchestration/ceshi.png">
            测试
          </NButton>

          <NButton
            type="info"
            color="#125EFF"
            @click="releaseFun"
            size="small"
          >
            <img class="w-[14px] h-[14px] mr-[6px]" src="@/assets/agentOrchestration/xinjian.png">
            发布
          </NButton>

          <NDropdown :options="allMenuOptions" @select="handleMenuSelect">
            <NButton ghost size="small" class="more-button">
              <img src="@/assets/agentOrchestration/shengluehao.png">
            </NButton>
          </NDropdown>
        </div>
      </div>
    </div>
    <!-- 浮动右侧配置面板 -->
    <NodeConfigPanel
      v-model:show="isConfigModalVisible"
      :node="selectedNode"
      @save="handleNodeConfigSave"
      class="floating-config-panel"
    />
    <!-- 测试运行面板 -->
    <TestRunPage
      v-model:show="isTestRunPageVisible"
      :currentFlow="currentFlow"
      class="floating-config-panel"
    />

    <!-- 变量管理面板 -->
    <VariableManagementPanel
      v-model:show="showVariableManagement"
    />

    <!-- 流程管理弹窗 -->
    <FlowManagerModal
      v-model:show="showFlowManager"
      @load-flow="handleLoadFlow"
      @delete-flow="handleDeleteFlow"
    />

    <!-- 备份管理弹窗 -->
    <FlowBackupModal v-model:show="showFlowBackup" />

    <!-- 新建流程弹窗 -->
    <NModal v-model:show="showCreateFlow">
      <NCard
        style="width: 500px"
        title="新建流程"
        :bordered="false"
        size="huge"
      >
        <NForm
          ref="createFlowFormRef"
          :model="createFlowForm"
          :rules="createFlowRules"
        >
          <NFormItem label="流程名称" path="name">
            <NInput
              v-model:value="createFlowForm.name"
              placeholder="请输入流程名称"
            />
          </NFormItem>
          <NFormItem label="流程描述" path="description">
            <NInput
              v-model:value="createFlowForm.description"
              type="textarea"
              placeholder="请输入流程描述（可选）"
              :rows="3"
            />
          </NFormItem>
        </NForm>
        <template #footer>
          <div class="flex justify-end space-x-2">
            <NButton @click="showCreateFlow = false">
              取消
            </NButton>
            <NButton type="primary" @click="handleCreateFlow">
              创建
            </NButton>
          </div>
        </template>
      </NCard>
    </NModal>
  </div>
</template>

<style scoped lang="less">
// 全屏画布容器
.fullscreen-canvas-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;
  z-index: 1;
}

// 全屏画布
.fullscreen-canvas {
  width: 100%;
  height: 100%;
  position: relative;
}

// 浮动左上角智能体标题
.floating-title {
  position: absolute;
  top: 20px;
  left: 20px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 280px;

  .title-content {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #2f3033;

    .back-button {
      width: 32px;
      height: 32px;
      background: #fafbff;
      border: 1px solid #e9ecf3;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 12px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #f0f5ff;
        border-color: #125EFF;
      }

      img {
        width: 16px;
        height: 16px;
      }
    }

    .title-text {
      margin-right: 8px;
    }
  }

  .close-button {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px;
    color: #999;
    font-size: 18px;
    transition: all 0.2s ease;

    &:hover {
      background: #f5f5f5;
      color: #666;
    }
  }
}

// 浮动左侧节点面板 - 调整位置和宽度
.floating-node-panel {
  position: absolute;
  top: 100px; // 增加与标题的间距
  left: 20px;
  width: 280px; // 增加宽度
  max-height: calc(100vh - 140px); // 调整最大高度适应新位置
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;

  .panel-header {
    padding: 16px 16px 12px;
    border-bottom: 1px solid #f0f0f0;

    .panel-title {
      font-size: 14px;
      font-weight: 600;
      color: #2f3033;
    }
  }

  .panel-content {
    padding: 8px;
    max-height: calc(100vh - 220px); // 调整内容区域高度
    overflow-y: auto;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #d9d9d9;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #bfbfbf;
    }
  }
}

// 浮动右上角工具栏 - 简化版本
.floating-toolbar {
  position: absolute;
  top: 20px;
  right: 20px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;

  .toolbar-actions {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    gap: 12px;

    .action-group {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .divider {
      width: 1px;
      height: 24px;
      background: #e9ecf3;
    }

    // 同步状态指示器样式
    .sync-status {
      flex-direction: column;
      align-items: center;
      gap: 2px;

      .sync-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        font-size: 12px;
        font-weight: bold;

        &.idle {
          background: #f0f0f0;
          color: #999;
        }

        &.syncing {
          background: #1890ff;
          color: white;
          animation: spin 1s linear infinite;
        }

        &.success {
          background: #52c41a;
          color: white;
        }

        &.error {
          background: #ff4d4f;
          color: white;
        }

        &.warning {
          background: #faad14;
          color: white;
        }

        .sync-icon {
          line-height: 1;
        }
      }

      .sync-text {
        font-size: 10px;
        color: #666;
        white-space: nowrap;
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 工具栏按钮样式
.floating-toolbar {
  :deep(.n-button) {
    border-radius: 6px;
    height: 32px;
    padding: 0 12px;
    font-size: 12px;

    &.more-button {
      width: 32px;
      padding: 0;
      background: #ffffff;
      border: 1px solid #ebebeb;

      img {
        width: 16px;
        height: 16px;
      }
    }
  }
}

// 浮动右侧配置面板样式调整
:deep(.floating-config-panel) {
  .n-drawer {
    z-index: 200;
  }

  .n-drawer-content {
    background: #ffffff;
    border-radius: 12px 0 0 12px;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  }
}

// 全局按钮样式
:deep(.n-button) {
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
  }
}

:deep(.n-card) {
  border-radius: 12px;
}

// 响应式设计
@media (max-width: 1200px) {
  .floating-node-panel {
    width: 160px;
  }

  .floating-toolbar {
    min-width: 280px;

    .toolbar-header .flow-title {
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  .floating-node-panel {
    width: 140px;
    top: 10px;
    left: 10px;
  }

  .floating-toolbar {
    top: 10px;
    right: 10px;
    min-width: 240px;
  }
}
</style>
