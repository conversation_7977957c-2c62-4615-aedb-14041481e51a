<template>
  <div class="condition-node" :class="{ 'selected': selected, 'running': isRunning }">
    <!-- 输入连接点 -->
    <Handle
      type="target"
      :position="Position.Left"
      :id="`${id}-input`"
      class="input-handle"
    />

    <!-- 节点主体 -->
    <div class="node-body">
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="statusClass"></div>

      <!-- 节点头部 -->
      <div class="node-header">
        <img class="node-icon" src="@/assets/agentOrchestration/conditionalBranchIcon.png" alt="条件分支">
        <div class="node-title">{{ data.label || '条件分支' }}</div>
      </div>

      <!-- 节点描述信息 -->
      <div v-if="data.description" class="node-description">
        {{ data.description }}
      </div>
      <!-- 分支选项列表 -->
      <div class="branches-list">
        <div class="branch-item" v-for="(item, index) in data.config.conditionBranchArr" :key="index">
          <span class="branch-label">{{
              index == 0
                ? "如果"
                : index ==
                  data.config.conditionBranchArr.length - 1
                  ? "否则"
                  : "否则如果"
            }}</span>
          <span class="branch-value">{{item.name}}</span>
          <!-- True分支连接点 -->
          <Handle
            type="source"
            :position="Position.Right"
            :id="`${item.id}`"
            class="output-handle true-handle branch-handle"
          />
        </div>
        <!-- <div class="branch-item">
          <span class="branch-label">否则</span>
          <span class="branch-value">分支2</span>
          <Handle
            type="source"
            :position="Position.Right"
            :id="`${id}-output-false`"
            class="output-handle false-handle branch-handle"
          />
        </div> -->
      </div>
    </div>

    <!-- 节点下方的执行日志显示 -->
    <NodeLogDisplay :node-id="id" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import { NodeStatus } from '@/store/modules/orchestration'
import NodeLogDisplay from '../NodeLogDisplay.vue'

interface ConditionNodeProps {
  id: string
  data: {
    label?: string
    description?: string
    status?: NodeStatus
    config?: {
      condition?: string
      conditionDesc?: string
      [key: string]: any
    }
    [key: string]: any
  }
  selected?: boolean
}

const props = defineProps<ConditionNodeProps>()

// 计算属性
const isRunning = computed(() => props.data.status === NodeStatus.RUNNING)

const statusClass = computed(() => {
  switch (props.data.status) {
    case NodeStatus.RUNNING:
      return 'status-running'
    case NodeStatus.SUCCESS:
      return 'status-success'
    case NodeStatus.ERROR:
      return 'status-error'
    default:
      return 'status-idle'
  }
})
</script>

<style scoped lang="less">
@import './styles/unified-node-styles.less';

.condition-node {
  .rectangular-node-style();
  .unified-handle-style();

  .node-body {
    .node-header {
      .node-header-style();

      .node-icon {
        width: 16px;
        height: 16px;
        color: #6b7280;
      }
    }

    .node-description {
      .node-description-style();
    }

    .branches-list {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .branch-item {
        .node-list-item-style();
        position: relative;

        .branch-label {
          font-size: 11px;
          color: #6b7280;
          font-weight: 500;
        }

        .branch-value {
          font-size: 11px;
          color: #9ca3af;
        }

        // 分支连接点样式 - 显示在节点卡片上方，位置与分支对齐
        :deep(.branch-handle) {
          position: absolute;
          right: -16px; // 放在卡片外侧，视觉上更明显
          top: 50%;
          transform: translateY(-50%);
          width: 10px;
          height: 10px;
          background: #3b82f6;
          border: 2px solid #ffffff;
          border-radius: 50%;
          // 分离transform和其他属性的transition，避免位移
          transition: border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
          z-index: 100; // 高层级，显示在卡片上方
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover {
            // 使用原地缩放而不是改变尺寸，避免位移
            transform: translateY(-50%) scale(1.2);
            transform-origin: center;
            background: #2563eb;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            // 为transform单独设置快速transition
            transition: border-color 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease, transform 0.1s ease;
          }

          &.vue-flow__handle-connecting {
            background: #125EFF;
            box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.3);
          }
        }
      }
    }
  }

  .status-indicator {
    .status-indicator-style();
  }
}
</style>
