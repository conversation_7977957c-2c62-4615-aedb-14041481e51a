<template>
	<div class="usage-overview">
		<!-- 筛选区域 -->
		<div class="bg-white rounded-lg p-4 shadow-sm border mb-4">
			<div class="flex items-center space-x-4">
				<div class="flex items-center space-x-2">
					<span class="text-sm text-gray-600">时间范围:</span>
					<n-select
						v-model:value="filters.timeRange"
						:options="timeRangeOptions"
						placeholder="选择时间范围"
						style="width: 150px"
						clearable
					/>
				</div>
				<!-- <div class="flex items-center space-x-2">
					<span class="text-sm text-gray-600">用户:</span>
					<n-input
						v-model:value="filters.userId"
						placeholder="输入用户ID"
						style="width: 150px"
						clearable
					/>
				</div> -->
				<!-- agentId和snapshotAgentId从路由参数获取，不需要输入框 -->
				<n-button type="primary" @click="searchConversations"> 搜索 </n-button>
			</div>
		</div>

		<!-- 会话列表 -->
		<div class="bg-white rounded-lg shadow-sm border">
			<div class="p-6">
				<n-data-table
					:columns="columns"
					:data="conversations"
					:pagination="pagination"
					:loading="loading"
				/>
			</div>
		</div>

		<!-- 聊天详情抽屉 -->
		<n-drawer
			v-model:show="showDetailDrawer"
			:width="600"
			placement="right"
			:mask-closable="true"
			:close-on-esc="true"
		>
			<n-drawer-content title="会话详情" closable>
				<div v-if="selectedConversation" class="chat-detail">
					<!-- 会话基本信息 -->
					<div class="conversation-header mb-4">
						<div class="info-row">
							<span class="label">会话标题:</span>
							<span class="value">{{
								selectedConversation.title || selectedConversation.messageTitle
							}}</span>
						</div>
						<div class="info-row">
							<span class="label">会话ID:</span>
							<span class="value">{{
								selectedConversation.conversationId || selectedConversation.id
							}}</span>
						</div>
					</div>

					<!-- 会话历史记录 -->
					<div class="chat-content">
						<div class="chat-history">
							<h4 class="mb-3">对话记录</h4>
							<div
								v-if="
									selectedConversation.chatHistory &&
									selectedConversation.chatHistory.length > 0
								"
							>
								<div
									v-for="(item, index) in selectedConversation.chatHistory"
									:key="item.id || index"
									class="conversation-item"
								>
									<!-- 用户提问 -->
									<div v-if="item.question" class="message-item user-message">
										<div class="message-header">
											<span class="role-label">用户</span>
											<span class="timestamp">{{
												formatTime(item.createdAt || item.requestTime)
											}}</span>
										</div>
										<div class="message-content">
											{{ item.question }}
										</div>
									</div>

									<!-- AI回答 -->
									<div
										v-if="item.answer"
										class="message-item assistant-message"
										@mouseenter="hoveredMessageIndex = index"
										@mouseleave="hoveredMessageIndex = null"
									>
										<div class="message-header">
											<div class="message-header-left">
												<span class="role-label">AI助手</span>
												<img 
													:src="copyImg" 
													alt="复制" 
													class="copy-icon" 
													:class="{ 'visible': hoveredMessageIndex === index }"
													@click="copyToClipboard(item.answer)"
													title="复制"
												/>
											</div>
											<span class="timestamp">{{
												formatTime(
													item.responseTime || item.endTime || item.createdAt
												)
											}}</span>
										</div>
										<div class="message-content">
											{{ item.answer }}
										</div>

										<!-- Token信息 -->
									</div>
								</div>
							</div>
							<div v-else class="no-messages">暂无对话记录</div>
						</div>
					</div>
				</div>
			</n-drawer-content>
		</n-drawer>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, h } from "vue";
import {
	NButton,
	NSelect,
	NInput,
	NDataTable,
	NDrawer,
	NDrawerContent,
	useMessage,
} from "naive-ui";
import { getConversationList } from "@/api/workShop";
import copyImg from '@/assets/chat/copy.png';

// 定义props
interface Props {
	snapshotAgentId?: string;
	agentId?: string;
}

const props = withDefaults(defineProps<Props>(), {
	snapshotAgentId: "",
	agentId: "",
});

const message = useMessage();
const loading = ref(false);
const showDetailDrawer = ref(false);
const selectedConversation = ref<any>(null);
const hoveredMessageIndex = ref<number | null>(null);

const filters = ref({
	timeRange: "3", // 默认选择"全部"
	userId: "", // 用户ID搜索
	title: "", // 会话标题搜索
	category: "1", // 默认查询正式会话
});

const timeRangeOptions = [
	{ label: "今天", value: "0" },
	{ label: "近7天", value: "1" },
	{ label: "近30天", value: "2" },
	{ label: "全部", value: "3" },
];

const columns = [
	{
		title: "会话ID",
		key: "agentId",
		width: 120,
	},
	{
		title: "用户ID",
		key: "id",
		width: 100,
	},
	{
		title: "消息标题",
		key: "title",
		width: 200,
		render(row: any) {
			return h(
				"a",
				{
					href: "javascript:void(0)",
					style: "color: #1890ff; cursor: pointer; text-decoration: none;",
					onClick: () => showChatDetail(row),
				},
				row.title
			);
		},
	},
	{
		title: "对话轮数",
		key: "conversationNum",
		width: 100,
		align: "center" as const,
	},
	{
		title: "时间",
		key: "createdAt",
		width: 150,
	},
	{
		title: "输入Token",
		key: "inputTokens",
		width: 100,
		align: "right" as const,
	},
	{
		title: "输出Token",
		key: "outputTokens",
		width: 100,
		align: "right" as const,
	},
];

const conversations = ref([]);

const pagination = ref({
	page: 1,
	pageSize: 10,
	showSizePicker: true,
	pageSizes: [10, 20, 50],
	itemCount: 0,
	onChange: (page: number) => {
		pagination.value.page = page;
		loadConversations();
	},
	onUpdatePageSize: (pageSize: number) => {
		pagination.value.pageSize = pageSize;
		pagination.value.page = 1;
		loadConversations();
	},
});

onMounted(() => {
	loadConversations();
});

async function loadConversations() {
	loading.value = true;
	try {
		const params = {
			page_num: pagination.value.page,
			page_size: pagination.value.pageSize,
			timeRange: filters.value.timeRange,
			agentId: props.agentId,
			snapshotAgentId: props.snapshotAgentId,
			title: filters.value.title, // 会话标题搜索
			category: filters.value.category, // 会话类型
		};

		const result: any = await getConversationList(params);

		if (result.code === "0" || result.code === 0) {
			conversations.value = result.data?.items || [];
			pagination.value.itemCount = result.data?.total || 0;
		} else {
			console.error("加载会话数据失败:", result.message);
			conversations.value = [];
			pagination.value.itemCount = 0;
		}
	} catch (error) {
		console.error("加载会话数据失败:", error);
		conversations.value = [];
		pagination.value.itemCount = 0;
	} finally {
		loading.value = false;
	}
}

function searchConversations() {
	pagination.value.page = 1;
	loadConversations();
}

function showChatDetail(conversation: any) {
	// 直接使用传入数据中的conversationDetailList，不调用接口
	const conversationDetailList = conversation.conversationDetailList || [];

	// 添加调试信息
	console.log("使用本地数据:", conversation);
	console.log("conversationDetailList:", conversationDetailList);
	console.log("conversationDetailList长度:", conversationDetailList.length);

	if (conversationDetailList.length > 0) {
		console.log("第一条记录:", conversationDetailList[0]);
		console.log("question字段:", conversationDetailList[0].question);
		console.log("answer字段:", conversationDetailList[0].answer);
	}

	selectedConversation.value = {
		...conversation,
		chatHistory: conversationDetailList,
	};

	showDetailDrawer.value = true;
}

function formatTime(timeString: string) {
	if (!timeString) return "";
	try {
		const date = new Date(timeString);
		return date.toLocaleString("zh-CN", {
			year: "numeric",
			month: "2-digit",
			day: "2-digit",
			hour: "2-digit",
			minute: "2-digit",
			second: "2-digit",
		});
	} catch (error) {
		return timeString;
	}
}

function copyToClipboard(content: string) {
  // 清理内容，移除HTML标签
  const cleanContent = content.replace(/<[^>]*>/g, '').trim()
  console.log('要复制的内容:', cleanContent);
  
  // 方法1: 尝试使用现代的 navigator.clipboard（如果可用）
  if (navigator.clipboard && window.isSecureContext) {
    navigator.clipboard.writeText(cleanContent)
      .then(() => {
        console.log('navigator.clipboard 复制成功')
        message.success('复制成功')
      })
      .catch((err) => {
        console.error('navigator.clipboard 失败:', err)
        fallbackCopy(cleanContent)
      })
    return
  }
  
  // 方法2: 降级方案
  fallbackCopy(cleanContent)
}

function fallbackCopy(text: string) {
  try {
    // 创建一个临时的 span 元素来包含文本
    const span = document.createElement('span')
    span.textContent = text
    span.style.position = 'absolute'
    span.style.left = '-9999px'
    span.style.top = '-9999px'
    span.style.fontSize = '12pt' // 防止在某些浏览器中缩放
    span.style.border = '0'
    span.style.padding = '0'
    span.style.margin = '0'
    
    document.body.appendChild(span)
    
    // 创建选择范围
    const selection = window.getSelection()
    const range = document.createRange()
    
    if (selection && range) {
      // 清除之前的选择
      selection.removeAllRanges()
      
      // 选择 span 中的文本
      range.selectNodeContents(span)
      selection.addRange(range)
      
      // 尝试复制
      let successful = false
      try {
        successful = document.execCommand('copy')
        console.log('fallback execCommand 返回:', successful)
      } catch (err) {
        console.error('fallback execCommand 错误:', err)
      }
      
      // 清除选择
      selection.removeAllRanges()
      
      // 移除临时元素
      document.body.removeChild(span)
      
      if (successful) {
        console.log('fallback 复制成功')
        message.success('复制成功')
      } else {
        console.log('fallback 复制失败')
        message.error('复制失败，请手动复制文本')
        // 可以考虑显示一个对话框让用户手动复制
        showManualCopyDialog(text)
      }
    }
  } catch (err) {
    console.error('fallback 复制过程出错:', err)
    message.error('复制失败')
    showManualCopyDialog(text)
  }
}

function showManualCopyDialog(text: string) {
  // 如果自动复制失败，显示文本让用户手动复制
  alert(`复制失败，请手动复制以下内容：\n\n${text}`)
}
</script>

<style lang="less" scoped>
/* 会话详情弹窗样式 */
.chat-detail {
	padding: 16px;
}

.conversation-header {
	border-bottom: 1px solid #e0e0e0;
	padding-bottom: 12px;
}

.info-row {
	display: flex;
	align-items: center;
	gap: 8px;
	margin-bottom: 8px;
}

.info-row:last-child {
	margin-bottom: 0;
}

.label {
	font-weight: 500;
	color: #666;
	font-size: 14px;
	min-width: 80px;
}

.value {
	color: #333;
	font-size: 14px;
	word-break: break-all;
}

.chat-content {
	margin-top: 16px;
}

.chat-history h4 {
	color: #333;
	font-size: 16px;
	font-weight: 500;
	margin: 0 0 12px 0;
}

.conversation-item {
	margin-bottom: 20px;
}

.message-item {
	margin-bottom: 8px;
	padding: 12px;
	border-radius: 8px;
	border: 1px solid #e0e0e0;
}

.user-message {
	background-color: #f0f9ff;
	border-color: #bae6fd;
}

.assistant-message {
	background-color: #f9fafb;
	border-color: #e5e7eb;
}

.message-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}

.message-header-left {
	display: flex;
	align-items: center;
	gap: 8px;
}

.role-label {
	font-weight: 500;
	font-size: 12px;
	padding: 2px 8px;
	border-radius: 4px;
	color: white;
}

.user-message .role-label {
	background-color: #3b82f6;
}

.assistant-message .role-label {
	background-color: #6b7280;
}

.copy-icon {
	width: 16px;
	height: 16px;
	cursor: pointer;
	opacity: 0;
	transition: opacity 0.2s ease;
	filter: brightness(0) saturate(100%);
}

.copy-icon.visible {
	opacity: 1;
}

.copy-icon:hover {
	opacity: 0.8;
}

.timestamp {
	font-size: 12px;
	color: #9ca3af;
}

.message-content {
	color: #374151;
	font-size: 14px;
	line-height: 1.5;
	white-space: pre-wrap;
	word-break: break-word;
}

.no-messages {
	text-align: center;
	color: #9ca3af;
	font-size: 14px;
	padding: 20px;
}

.message-block {
	background: #f8f9fa;
	border-radius: 8px;
	padding: 16px;
	border: 1px solid #e0e0e0;
}

.message-text {
	color: #333;
	line-height: 1.5;
	font-size: 14px;
}

.action-buttons {
	text-align: center;
}

.token-info {
	margin-top: 8px;
	padding-top: 8px;
	border-top: 1px solid #e5e7eb;
	display: flex;
	gap: 12px;
	flex-wrap: wrap;
}

.token-item {
	font-size: 12px;
	color: #6b7280;
	background-color: #f3f4f6;
	padding: 2px 6px;
	border-radius: 4px;
}
</style>
