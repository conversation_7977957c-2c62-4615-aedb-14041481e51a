import { get, post } from '@/utils/request'

export function getquestionsApi<T = any>(data: any) {
  return get<T>({
    url: '/eaide/summary/exam_guides/exam_question',
    data,
    headers: {
      'Content-Type': 'application/json',
  },
  })
}

export function examweakApi<T = any>(data: any) {
  return post<T>({
    url: '/eaide/summary/exam_guides/analyze/exam_test_weak',
    data,
    headers: {
      'Content-Type': 'application/json',
  },
  })
}
export function getlinksApi<T = any>(data: any) {
  return get<T>({
    url: '/eaide/sys_link/find_links',
    data,
    headers: {
      'Content-Type': 'application/json',
  },
  })
}

export function getListApi<T = any>(data: any) {
  return get<T>({
    url: '/emind/agent_square/all',
    data,
    headers: {
      'Content-Type': 'application/json',
  },
  })
}

