# AI对话公共组件

一个功能完备的、高度可配置的AI对话界面组件，支持多种交互模式和业务场景。基于Vue 3 + TypeScript开发，提供现代化的聊天体验。

## 🌟 功能特性

### 🎯 核心功能
- **双模式展示** - 支持气泡模式和平铺模式的AI消息展示
- **流式输出** - 打字机效果的逐字显示，支持SSE实时流
- **Markdown渲染** - 支持代码块、表格、链接等格式
- **思考模式** - 显示AI的思考过程，支持实时展示和自动折叠
- **工作流模式** - 任务编排和节点可视化，支持阶段性展示
- **动态宽度** - 用户消息根据内容长度自动调整宽度

### 🔧 交互功能
- **消息反馈** - 点赞/点踩和详细反馈，支持颜色状态反馈
- **消息编辑** - 用户消息的编辑和重发
- **复制功能** - 支持文本和Markdown格式复制，自动清理HTML标签
- **重新生成** - AI回答的重新生成，支持真实API调用
- **停止生成** - 中断AI回答生成
- **消息操作** - 支持删除、置顶、引用等扩展操作

### 📎 输入功能
- **多行输入** - 自适应高度的文本输入框，支持最大高度限制
- **语音输入** - 按住说话的语音识别，支持语音转文字
- **文件上传** - 支持多种格式的文件上传，拖拽上传
- **知识库选择** - 切换不同的知识库
- **快捷输入** - 预设问题的快速发送，支持顶部和底部位置
- **欢迎提示** - "猜你想问"功能，支持自定义提示问题

### 🎨 界面特性
- **双主题支持** - 支持明暗主题切换
- **响应式设计** - 完美适配桌面、平板和移动端
- **高度可配置** - 支持功能开关和样式定制
- **插槽扩展** - 支持消息操作、输入工具、用户消息的插槽扩展
- **无障碍支持** - 键盘导航和屏幕阅读器友好
- **现代化设计** - 渐变背景、多层阴影、圆角设计

## 🚀 快速开始

### 基础使用

```vue
<template>
  <AIChat
    :config="chatConfig"
    @send-message="handleSendMessage"
    @feedback="handleFeedback"
    @regenerate="handleRegenerate"
  />
</template>

<script setup lang="ts">
import AIChat from '@/components/common/AIChat/index.vue'
import { useChat } from '@/components/common/AIChat/composables/useChat'
import type { AIChatConfig, MessageFeedback } from '@/components/common/AIChat/types'

const { messages, sendMessage, regenerateMessage, addFeedback } = useChat()

const chatConfig: AIChatConfig = {
  theme: 'light',
  features: {
    feedback: true,
    voiceInput: true,
    fileUpload: true,
    thinking: true,
    workflow: true
  },
  styles: {
    aiMessageMode: 'bubble', // 'bubble' | 'flat'
    maxWidth: '800px',
    maxHeight: '600px'
  }
}

const handleSendMessage = async (content: string) => {
  await sendMessage(content)
}

const handleFeedback = async (messageId: string, feedback: MessageFeedback) => {
  await addFeedback(messageId, feedback)
}

const handleRegenerate = async (messageId: string) => {
  await regenerateMessage(messageId)
}
</script>
```

### 插槽扩展使用

```vue
<template>
  <AIChat :config="chatConfig">
    <!-- 消息操作插槽 - 为AI消息添加额外操作按钮 -->
    <template #message-actions="{ message, handleFeedback, handleCopy, handleRegenerate }">
      <button @click="shareMessage(message)">📤 分享</button>
      <button @click="bookmarkMessage(message)">⭐ 收藏</button>
      <button @click="translateMessage(message)">🌐 翻译</button>
    </template>

    <!-- 输入工具插槽 - 为输入区域添加额外工具按钮 -->
    <template #input-tools="{ config }">
      <button @click="openCamera()">📷 拍照</button>
      <button @click="openMicrophone()">🎤 录音</button>
      <button @click="getLocation()">📍 位置</button>
    </template>

    <!-- 用户消息插槽 - 为用户消息添加额外内容 -->
    <template #user-message-extra="{ message }">
      <div class="message-meta">
        <span>{{ formatTime(message.timestamp) }}</span>
        <button @click="pinMessage(message)">📌 置顶</button>
        <button @click="quoteMessage(message)">💬 引用</button>
      </div>
    </template>
  </AIChat>
</template>
```

### 完整配置

```typescript
const config: AIChatConfig = {
  // 界面配置
  theme: 'light', // 'light' | 'dark' | 'auto'
  layout: 'responsive', // 'desktop' | 'mobile' | 'responsive'

  // 功能开关
  features: {
    feedback: true,        // 反馈功能
    voiceInput: true,      // 语音输入
    fileUpload: true,      // 文件上传
    knowledgeBase: true,   // 知识库选择
    workflow: true,        // 工作流模式
    thinking: true,        // 思考模式
    messageEdit: true,     // 消息编辑
    stopGeneration: true,  // 停止生成
    textToSpeech: true,    // 文字转语音
    speechToText: true,    // 语音转文字
    export: true,          // 导出功能
    clear: true,           // 清空功能
    multiTurn: true,       // 多轮对话
    contextMemory: true    // 上下文记忆
  },

  // 样式定制
  styles: {
    primaryColor: '#125EFF',
    backgroundColor: '#f9fafb',
    messageStyle: 'bubble',        // 'bubble' | 'card'
    aiMessageMode: 'bubble',       // 'bubble' | 'flat' - AI消息展示模式
    maxWidth: '800px',             // 最大宽度
    maxHeight: '600px',            // 最大高度
    avatarStyle: 'circle',         // 'circle' | 'square'
    colorScheme: 'blue',           // 颜色方案
    fontSize: 'medium',            // 'small' | 'medium' | 'large'
    messageSpacing: 'normal',      // 'compact' | 'normal' | 'loose'
    borderRadius: 'medium',        // 'none' | 'small' | 'medium' | 'large'
    animation: 'fade'              // 'none' | 'fade' | 'slide' | 'bounce'
  },

  // 占位符文本
  placeholders: {
    input: '请输入您的问题...',
    thinking: 'AI正在思考中...',
    voiceInput: '按住说话',
    fileUpload: '点击或拖拽上传文件',
    knowledgeBase: '选择知识库',
    searching: '搜索中...',
    generating: '生成中...',
    error: '出现错误，请重试'
  },

  // 快捷输入配置
  quickPrompts: {
    enabled: true,
    position: 'bottom',  // 'top' | 'bottom'
    prompts: [
      { id: '1', text: '你好', description: '打招呼' },
      { id: '2', text: '帮我写一份报告', description: '文档写作' },
      { id: '3', text: '解释这个概念', description: '知识问答' }
    ]
  },

  // 欢迎提示配置（猜你想问）
  welcomePrompts: {
    enabled: true,
    title: '猜你想问',
    prompts: [
      { id: '1', text: '如何使用这个组件？', description: '使用指南' },
      { id: '2', text: '有哪些配置选项？', description: '配置说明' },
      { id: '3', text: '如何自定义样式？', description: '样式定制' }
    ]
  },

  // API配置
  api: {
    chatEndpoint: '/api/chat',
    uploadEndpoint: '/api/upload',
    feedbackEndpoint: '/api/feedback',
    baseUrl: 'https://api.example.com',
    voiceEndpoint: '/api/voice',
    audioEndpoint: '/api/audio',
    translationEndpoint: '/api/translate',
    headers: {
      'Authorization': 'Bearer token',
      'Content-Type': 'application/json'
    },
    timeout: 30000
  },

  // 兼容性配置
  compatibility: {
    useOriginalAPI: true,
    conversationId: 'chat-123',
    category: '0',
    agentId: '1966317284699451393',
    apiAdapter: 'custom'
  }
}
```

## 📚 API 文档

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| config | AIChatConfig | {} | 组件配置对象 |
| title | string | - | 聊天窗口标题 |
| subtitle | string | - | 聊天窗口副标题 |
| messages | Message[] | [] | 消息列表（可选，使用useChat时自动管理） |
| quickPrompts | QuickPrompt[] | [] | 快捷输入提示列表 |
| knowledgeBases | KnowledgeBase[] | [] | 知识库列表 |
| isLoading | boolean | false | 全局加载状态 |
| isGenerating | boolean | false | AI生成状态 |
| workflowNodes | WorkflowNode[] | [] | 工作流节点数据 |
| welcomeMessage | WelcomeMessage | - | 欢迎消息配置 |

### Events

| 事件 | 参数 | 说明 |
|------|------|------|
| send-message | (content: string, attachments?: Attachment[]) | 用户发送消息 |
| feedback | (messageId: string, feedback: MessageFeedback) | 消息反馈（点赞/点踩） |
| edit-message | (messageId: string, content: string) | 编辑用户消息 |
| regenerate | (messageId: string) | 重新生成AI回答 |
| upload-file | (file: File) | 文件上传 |
| select-knowledge-base | (kb: KnowledgeBase) | 选择知识库 |
| stop-generation | () | 停止AI生成 |
| clear-chat | () | 清空对话历史 |
| export-chat | (format: 'json' \| 'markdown' \| 'txt') | 导出对话 |
| voice-input | (text: string) | 语音输入结果 |
| text-to-speech | (messageId: string) | 文字转语音 |
| copy-message | (messageId: string, content: string) | 复制消息内容 |
| quick-prompt | (prompt: QuickPrompt) | 点击快捷输入 |
| welcome-prompt | (prompt: WelcomePrompt) | 点击欢迎提示 |

### Slots

| 插槽名 | 作用域数据 | 说明 |
|--------|-----------|------|
| message-actions | { message, handleFeedback, handleCopy, handleRegenerate } | AI消息操作按钮扩展 |
| input-tools | { config, triggerFileUpload, showKnowledgeSelector } | 输入区域工具按钮扩展 |
| user-message-extra | { message, startEdit, showEdit } | 用户消息额外内容扩展 |

### 组合式函数

#### useChat(config?: AIChatConfig)

```typescript
const {
  // 状态管理
  messages,           // Ref<Message[]> - 消息列表
  isLoading,          // Ref<boolean> - 全局加载状态
  isGenerating,       // Ref<boolean> - AI生成状态
  currentMessage,     // Ref<Message | null> - 当前正在生成的消息

  // 消息操作
  sendMessage,        // (content: string, attachments?: Attachment[]) => Promise<void>
  stopGeneration,     // () => void - 停止AI生成
  regenerateMessage,  // (messageId: string) => Promise<void>
  editMessage,        // (messageId: string, content: string) => Promise<void>
  deleteMessage,      // (messageId: string) => void

  // 反馈和交互
  addFeedback,        // (messageId: string, feedback: MessageFeedback) => Promise<void>
  copyMessage,        // (messageId: string) => Promise<void>

  // 会话管理
  clearMessages,      // () => void
  exportChat,         // (format: 'json' | 'markdown' | 'txt') => Promise<void>
  importChat,         // (data: any) => void

  // 工具函数
  updateMessage,      // (messageId: string, updates: Partial<Message>) => void
  findMessage,        // (messageId: string) => Message | undefined
  getMessageHistory,  // () => Message[]

  // API适配器
  sendMessageWithOriginalAPI  // 兼容原有API的发送函数
} = useChat(config)
```

#### 使用示例

```typescript
// 基础使用
const { messages, sendMessage, isGenerating } = useChat()

// 带配置使用
const { messages, sendMessage } = useChat({
  compatibility: {
    useOriginalAPI: true,
    conversationId: 'chat-123'
  }
})

// 发送消息
await sendMessage('你好，请帮我分析这个问题')

// 重新生成
await regenerateMessage('message-id-123')

// 添加反馈
await addFeedback('message-id-123', {
  type: 'like',
  rating: 5,
  comment: '回答很好'
})
```

## 🔧 类型定义

### Message

```typescript
interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: number
  loading?: boolean
  error?: boolean
  thinking?: string                    // 思考过程内容
  thinkingCompleted?: boolean         // 思考是否完成
  stages?: MessageStage[]             // 工作流阶段
  stagesCompleted?: boolean           // 工作流是否完成
  attachments?: Attachment[]          // 附件列表
  feedback?: MessageFeedback          // 反馈信息
  metadata?: Record<string, any>      // 元数据
}
```

### MessageStage

```typescript
interface MessageStage {
  id: string
  name: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  description?: string
  result?: string
  startTime?: number
  endTime?: number
  progress?: number
}
```

### MessageFeedback

```typescript
interface MessageFeedback {
  type: 'like' | 'dislike'
  rating?: number                     // 1-5星评分
  comment?: string                    // 反馈评论
  categories?: string[]               // 反馈分类
  timestamp: number
  userId?: string
}
```

### AIChatConfig

```typescript
interface AIChatConfig {
  // 基础配置
  theme?: 'light' | 'dark' | 'auto'
  layout?: 'desktop' | 'mobile' | 'responsive'

  // 功能开关
  features?: {
    feedback?: boolean
    voiceInput?: boolean
    fileUpload?: boolean
    knowledgeBase?: boolean
    workflow?: boolean
    thinking?: boolean
    messageEdit?: boolean
    stopGeneration?: boolean
    textToSpeech?: boolean
    speechToText?: boolean
    export?: boolean
    clear?: boolean
    multiTurn?: boolean
    contextMemory?: boolean
  }

  // 样式定制
  styles?: {
    primaryColor?: string
    backgroundColor?: string
    messageStyle?: 'bubble' | 'card'
    aiMessageMode?: 'bubble' | 'flat'  // AI消息展示模式
    maxWidth?: string | number
    maxHeight?: string | number
    avatarStyle?: 'circle' | 'square'
    colorScheme?: string
    fontSize?: 'small' | 'medium' | 'large'
    messageSpacing?: 'compact' | 'normal' | 'loose'
    borderRadius?: 'none' | 'small' | 'medium' | 'large'
    animation?: 'none' | 'fade' | 'slide' | 'bounce'
  }

  // 占位符文本
  placeholders?: {
    input?: string
    thinking?: string
    voiceInput?: string
    fileUpload?: string
    knowledgeBase?: string
    searching?: string
    generating?: string
    error?: string
  }

  // 快捷输入配置
  quickPrompts?: {
    enabled?: boolean
    position?: 'top' | 'bottom'
    prompts?: QuickPrompt[]
  }

  // 欢迎提示配置
  welcomePrompts?: {
    enabled?: boolean
    title?: string
    prompts?: WelcomePrompt[]
  }

  // API配置
  api?: {
    chatEndpoint?: string
    uploadEndpoint?: string
    feedbackEndpoint?: string
    baseUrl?: string
    voiceEndpoint?: string
    audioEndpoint?: string
    translationEndpoint?: string
    headers?: Record<string, string>
    timeout?: number
  }

  // 兼容性配置
  compatibility?: {
    useOriginalAPI?: boolean
    conversationId?: string
    category?: string
    agentId?: string
    apiAdapter?: string
  }
}
```

## 🎨 样式定制

### CSS 变量

```css
.ai-chat-container {
  /* 主色调 */
  --primary-color: #125EFF;
  --primary-hover: #1e7fff;

  /* 背景色 */
  --chat-bg: #f9fafb;
  --message-bg: #ffffff;
  --user-message-bg: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
  --ai-message-bg: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  --flat-message-bg: linear-gradient(135deg, #f8faff 0%, #f1f5f9 100%);

  /* 文字颜色 */
  --text-color: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;

  /* 边框和阴影 */
  --border-color: rgba(18, 94, 255, 0.08);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 16px rgba(18, 94, 255, 0.04);
  --shadow-lg: 0 8px 32px rgba(18, 94, 255, 0.04);

  /* 圆角 */
  --border-radius: 20px;
  --border-radius-sm: 12px;
  --border-radius-lg: 24px;

  /* 间距 */
  --message-padding: 16px 20px;
  --message-margin: 16px 0;
  --input-padding: 12px 16px;
}
```

### 主题配置

```typescript
// 明亮主题
const lightTheme: AIChatConfig = {
  theme: 'light',
  styles: {
    primaryColor: '#125EFF',
    backgroundColor: '#f9fafb',
    aiMessageMode: 'bubble',
    colorScheme: 'blue'
  }
}

// 深色主题
const darkTheme: AIChatConfig = {
  theme: 'dark',
  styles: {
    primaryColor: '#60a5fa',
    backgroundColor: '#1f2937',
    aiMessageMode: 'flat',
    colorScheme: 'dark-blue'
  }
}

// 企业主题
const enterpriseTheme: AIChatConfig = {
  theme: 'light',
  styles: {
    primaryColor: '#059669',
    backgroundColor: '#f0fdf4',
    aiMessageMode: 'flat',
    messageStyle: 'card',
    borderRadius: 'small'
  }
}
```

### 响应式设计

```css
/* 桌面端 */
@media (min-width: 1024px) {
  .ai-chat-container {
    --message-padding: 16px 20px;
    --border-radius: 20px;
  }
}

/* 平板端 */
@media (max-width: 768px) {
  .ai-chat-container {
    --message-padding: 10px 14px;
    --border-radius: 16px;
  }
}

/* 手机端 */
@media (max-width: 480px) {
  .ai-chat-container {
    --message-padding: 8px 12px;
    --border-radius: 14px;
  }
}
```

## 💡 最佳实践

### 1. 消息管理
```typescript
// 使用 useChat 组合式函数管理消息状态
const { messages, sendMessage, clearMessages } = useChat({
  compatibility: {
    useOriginalAPI: true,
    conversationId: generateConversationId()
  }
})

// 合理控制消息列表长度，避免性能问题
const MAX_MESSAGES = 100
if (messages.value.length > MAX_MESSAGES) {
  messages.value = messages.value.slice(-MAX_MESSAGES)
}

// 实现消息持久化存储
watch(messages, (newMessages) => {
  localStorage.setItem('chat-messages', JSON.stringify(newMessages))
}, { deep: true })
```

### 2. 错误处理
```typescript
// 监听网络错误和API错误
const handleSendMessage = async (content: string) => {
  try {
    await sendMessage(content)
  } catch (error) {
    console.error('发送消息失败:', error)
    // 显示友好的错误提示
    showErrorMessage('发送失败，请检查网络连接后重试')
    // 提供重试机制
    showRetryButton(() => handleSendMessage(content))
  }
}
```

### 3. 性能优化
```typescript
// 防抖处理用户输入
import { debounce } from 'lodash-es'

const debouncedSend = debounce(async (content: string) => {
  await sendMessage(content)
}, 300)

// 懒加载附件和媒体内容
const loadAttachment = async (attachment: Attachment) => {
  if (!attachment.loaded) {
    attachment.content = await fetchAttachmentContent(attachment.id)
    attachment.loaded = true
  }
}
```

### 4. 插槽扩展
```vue
<!-- 合理使用插槽扩展功能 -->
<AIChat :config="config">
  <!-- 只在需要时添加额外操作 -->
  <template #message-actions="{ message }" v-if="showExtraActions">
    <button @click="shareMessage(message)">分享</button>
  </template>

  <!-- 根据权限显示不同工具 -->
  <template #input-tools="{ config }" v-if="hasAdvancedFeatures">
    <button @click="openAdvancedTools()">高级工具</button>
  </template>
</AIChat>
```

### 5. 无障碍支持
```vue
<!-- 添加适当的ARIA标签 -->
<AIChat
  :config="config"
  role="main"
  aria-label="AI对话界面"
  aria-describedby="chat-description"
/>

<!-- 支持键盘导航 -->
<style>
.ai-chat-container:focus-within {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
</style>
```

### 6. 模式选择建议
```typescript
// 根据使用场景选择合适的展示模式
const getRecommendedMode = (scenario: string): 'bubble' | 'flat' => {
  switch (scenario) {
    case 'personal-assistant':
    case 'customer-service':
    case 'social-chat':
      return 'bubble'  // 个人化场景使用气泡模式

    case 'enterprise-ai':
    case 'document-generation':
    case 'knowledge-qa':
    case 'report-analysis':
      return 'flat'    // 专业场景使用平铺模式

    default:
      return 'bubble'
  }
}
```

## 🎯 使用场景

### 个人AI助手
```typescript
const personalAssistantConfig: AIChatConfig = {
  theme: 'light',
  styles: {
    aiMessageMode: 'bubble',
    primaryColor: '#125EFF'
  },
  features: {
    voiceInput: true,
    textToSpeech: true,
    thinking: true
  }
}
```

### 企业知识问答
```typescript
const enterpriseConfig: AIChatConfig = {
  theme: 'light',
  styles: {
    aiMessageMode: 'flat',
    primaryColor: '#059669'
  },
  features: {
    knowledgeBase: true,
    export: true,
    workflow: true
  }
}
```

### 客服系统
```typescript
const customerServiceConfig: AIChatConfig = {
  theme: 'light',
  styles: {
    aiMessageMode: 'bubble',
    messageSpacing: 'compact'
  },
  features: {
    feedback: true,
    fileUpload: true,
    quickPrompts: true
  }
}
```

## 📱 演示页面

访问 `/ai-chat-demo` 查看完整的功能演示和配置示例。

### 演示功能
- ✅ 双模式展示切换（气泡/平铺）
- ✅ 思考过程和工作流演示
- ✅ 插槽扩展示例
- ✅ 配置面板实时调试
- ✅ 真实API集成测试
- ✅ 响应式设计预览
- ✅ 主题切换演示

### 快速体验
```bash
# 启动开发服务器
npm run dev

# 访问演示页面
http://localhost:1003/#/simple-test
```

## 🔄 版本更新

### v2.0.0 (最新)
- ✅ 新增AI消息双模式展示（气泡/平铺）
- ✅ 新增用户消息动态宽度
- ✅ 新增三种插槽扩展支持
- ✅ 新增欢迎提示功能
- ✅ 优化思考过程和工作流展示
- ✅ 完善真实API集成
- ✅ 优化移动端体验
- ✅ 增强配置系统

### v1.0.0
- ✅ 基础聊天功能
- ✅ 流式输出支持
- ✅ 文件上传功能
- ✅ 语音输入支持
- ✅ 反馈系统

## 📞 技术支持

如有问题或建议，请联系开发团队或提交Issue。
