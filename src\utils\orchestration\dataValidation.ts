/**
 * 数据验证工具函数
 *
 * 提供完整的后端数据格式验证功能，
 * 确保数据完整性和一致性
 */

import { isArray, isNumber, isObject, isString } from '@/utils/is'
import type {
  BackendFlowData,
  BackendNodeData,
  BackendEdgeData,
  NodeConfig,
  LLMNodeConfig,
  APINodeConfig,
  ConditionNodeConfig,
  ParameterDefinition,
  FlowVariable
} from '@/types/backend'
import { NodeType } from '@/store/modules/orchestration'

/**
 * 验证错误信息接口
 */
export interface ValidationError {
  field: string
  message: string
  value?: any
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  valid: boolean
  errors: ValidationError[]
}

/**
 * 验证后端流程数据格式
 * @param data 待验证的数据
 * @returns 验证结果
 */
export function validateBackendFlowData(data: any): ValidationResult {
  const errors: ValidationError[] = []
  // 基础类型检查
  if (!data || !isObject(data)) {
    errors.push({
      field: 'root',
      message: '流程数据必须是一个对象',
      value: data
    })
    return { valid: false, errors }
  }

  // 验证必需字段
  if (!isString(data.id) || !data.id.trim()) {
    errors.push({
      field: 'id',
      message: '流程ID必须是非空字符串',
      value: data.id
    })
  }

  if (!isString(data.name) || !data.name.trim()) {
    errors.push({
      field: 'name',
      message: '流程名称必须是非空字符串',
      value: data.name
    })
  }

  // 验证节点数组
  if (!isArray(data.nodes)) {
    errors.push({
      field: 'nodes',
      message: '节点列表必须是数组',
      value: data.nodes
    })
  } else {
    data.nodes.forEach((node: any, index: number) => {
      const nodeValidation = validateBackendNodeData(node)
      if (!nodeValidation.valid) {
        nodeValidation.errors.forEach(error => {
          errors.push({
            field: `nodes[${index}].${error.field}`,
            message: error.message,
            value: error.value
          })
        })
      }
    })
  }

  // 验证边数组
  if (!isArray(data.edges)) {
    errors.push({
      field: 'edges',
      message: '边列表必须是数组',
      value: data.edges
    })
  } else {
    data.edges.forEach((edge: any, index: number) => {
      const edgeValidation = validateBackendEdgeData(edge)
      if (!edgeValidation.valid) {
        edgeValidation.errors.forEach(error => {
          errors.push({
            field: `edges[${index}].${error.field}`,
            message: error.message,
            value: error.value
          })
        })
      }
    })
  }

  // 验证元数据
  if (data.metadata) {
    const metadataValidation = validateMetadata(data.metadata)
    if (!metadataValidation.valid) {
      metadataValidation.errors.forEach(error => {
        errors.push({
          field: `metadata.${error.field}`,
          message: error.message,
          value: error.value
        })
      })
    }
  }

  // 验证变量（可选）
  if (data.variables && isArray(data.variables)) {
    data.variables.forEach((variable: any, index: number) => {
      const variableValidation = validateFlowVariable(variable)
      if (!variableValidation.valid) {
        variableValidation.errors.forEach(error => {
          errors.push({
            field: `variables[${index}].${error.field}`,
            message: error.message,
            value: error.value
          })
        })
      }
    })
  }

  return { valid: errors.length === 0, errors }
}

/**
 * 验证后端节点数据
 * @param node 节点数据
 * @returns 验证结果
 */
export function validateBackendNodeData(node: any): ValidationResult {
  const errors: ValidationError[] = []

  if (!node || !isObject(node)) {
    errors.push({
      field: 'root',
      message: '节点数据必须是一个对象',
      value: node
    })
    return { valid: false, errors }
  }

  // 验证必需字段
  if (!isString(node.id) || !node.id.trim()) {
    errors.push({
      field: 'id',
      message: '节点ID必须是非空字符串',
      value: node.id
    })
  }

  if (!isString(node.type) || !Object.values(NodeType).includes(node.type)) {
    errors.push({
      field: 'type',
      message: '节点类型必须是有效的NodeType枚举值',
      value: node.type
    })
  }

  if (!isString(node.label) || !node.label.trim()) {
    errors.push({
      field: 'label',
      message: '节点标签必须是非空字符串',
      value: node.label
    })
  }

  // 验证位置信息
  if (!node.position || !isObject(node.position)) {
    errors.push({
      field: 'position',
      message: '节点位置信息必须是一个对象',
      value: node.position
    })
  } else {
    if (!isNumber(node.position.x)) {
      errors.push({
        field: 'position.x',
        message: '节点X坐标必须是数字',
        value: node.position.x
      })
    }
    if (!isNumber(node.position.y)) {
      errors.push({
        field: 'position.y',
        message: '节点Y坐标必须是数字',
        value: node.position.y
      })
    }
  }

  // 验证节点配置
  if (node.config) {
    const configValidation = validateNodeConfig(node.type, node.config)
    if (!configValidation.valid) {
      configValidation.errors.forEach(error => {
        errors.push({
          field: `config.${error.field}`,
          message: error.message,
          value: error.value
        })
      })
    }
  }

  // 验证参数定义（可选）
  if (node.inputParams && isArray(node.inputParams)) {
    node.inputParams.forEach((param: any, index: number) => {
      const paramValidation = validateParameterDefinition(param)
      if (!paramValidation.valid) {
        paramValidation.errors.forEach(error => {
          errors.push({
            field: `inputParams[${index}].${error.field}`,
            message: error.message,
            value: error.value
          })
        })
      }
    })
  }

  if (node.outputParams && isArray(node.outputParams)) {
    node.outputParams.forEach((param: any, index: number) => {
      const paramValidation = validateParameterDefinition(param)
      if (!paramValidation.valid) {
        paramValidation.errors.forEach(error => {
          errors.push({
            field: `outputParams[${index}].${error.field}`,
            message: error.message,
            value: error.value
          })
        })
      }
    })
  }

  return { valid: errors.length === 0, errors }
}

/**
 * 验证后端边数据
 * @param edge 边数据
 * @returns 验证结果
 */
export function validateBackendEdgeData(edge: any): ValidationResult {
  const errors: ValidationError[] = []

  if (!edge || !isObject(edge)) {
    errors.push({
      field: 'root',
      message: '边数据必须是一个对象',
      value: edge
    })
    return { valid: false, errors }
  }

  // 验证必需字段
  if (!isString(edge.id) || !edge.id.trim()) {
    errors.push({
      field: 'id',
      message: '边ID必须是非空字符串',
      value: edge.id
    })
  }

  if (!isString(edge.source) || !edge.source.trim()) {
    errors.push({
      field: 'source',
      message: '源节点ID必须是非空字符串',
      value: edge.source
    })
  }

  if (!isString(edge.target) || !edge.target.trim()) {
    errors.push({
      field: 'target',
      message: '目标节点ID必须是非空字符串',
      value: edge.target
    })
  }

  // 验证可选字段
  if (edge.sourceHandle !== undefined && !isString(edge.sourceHandle)) {
    errors.push({
      field: 'sourceHandle',
      message: '源连接点ID必须是字符串',
      value: edge.sourceHandle
    })
  }

  if (edge.targetHandle !== undefined && !isString(edge.targetHandle)) {
    errors.push({
      field: 'targetHandle',
      message: '目标连接点ID必须是字符串',
      value: edge.targetHandle
    })
  }

  if (edge.condition !== undefined && !isString(edge.condition)) {
    errors.push({
      field: 'condition',
      message: '条件表达式必须是字符串',
      value: edge.condition
    })
  }

  return { valid: errors.length === 0, errors }
}

/**
 * 验证节点配置
 * @param nodeType 节点类型
 * @param config 配置数据
 * @returns 验证结果
 */
export function validateNodeConfig(nodeType: NodeType, config: any): ValidationResult {
  if (!config || !isObject(config)) {
    return {
      valid: false,
      errors: [{
        field: 'root',
        message: '节点配置必须是一个对象',
        value: config
      }]
    }
  }

  switch (nodeType) {
    case NodeType.API:
      return validateAPIConfig(config)
    case NodeType.CONDITION:
      return validateConditionConfig(config)
    default:
      // 对于其他节点类型，进行基础验证
      return { valid: true, errors: [] }
  }
}


/**
 * 验证API节点配置
 * @param config API配置数据
 * @returns 验证结果
 */
export function validateAPIConfig(config: any): ValidationResult {
  const errors: ValidationError[] = []
  const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']

  if (!isString(config.method) || !validMethods.includes(config.method)) {
    errors.push({
      field: 'method',
      message: `请求方法必须是以下之一: ${validMethods.join(', ')}`,
      value: config.method
    })
  }

  if (!isString(config.url) || !config.url.trim()) {
    errors.push({
      field: 'url',
      message: '接口地址必须是非空字符串',
      value: config.url
    })
  }

  // 验证URL格式
  if (isString(config.url) && config.url.trim()) {
    try {
      new URL(config.url)
    } catch {
      // 如果不是完整URL，检查是否是相对路径
      if (!config.url.startsWith('/') && !config.url.startsWith('http')) {
        errors.push({
          field: 'url',
          message: '接口地址必须是有效的URL或以/开头的路径',
          value: config.url
        })
      }
    }
  }

  if (config.timeout !== undefined && (!isNumber(config.timeout) || config.timeout < 1)) {
    errors.push({
      field: 'timeout',
      message: '超时时间必须是大于0的数字',
      value: config.timeout
    })
  }

  if (config.retryCount !== undefined && (!isNumber(config.retryCount) || config.retryCount < 0)) {
    errors.push({
      field: 'retryCount',
      message: '重试次数必须是非负整数',
      value: config.retryCount
    })
  }

  return { valid: errors.length === 0, errors }
}

/**
 * 验证条件节点配置
 * @param config 条件配置数据
 * @returns 验证结果
 */
export function validateConditionConfig(config: any): ValidationResult {
  const errors: ValidationError[] = []
  const validLogicOperators = ['AND', 'OR']

  if (!isArray(config.conditions)) {
    errors.push({
      field: 'conditions',
      message: '条件列表必须是数组',
      value: config.conditions
    })
  } else if (config.conditions.length === 0) {
    errors.push({
      field: 'conditions',
      message: '条件列表不能为空',
      value: config.conditions
    })
  }

  if (config.conditionLogic && !validLogicOperators.includes(config.conditionLogic)) {
    errors.push({
      field: 'conditionLogic',
      message: `逻辑操作符必须是以下之一: ${validLogicOperators.join(', ')}`,
      value: config.conditionLogic
    })
  }

  return { valid: errors.length === 0, errors }
}

/**
 * 验证元数据
 * @param metadata 元数据
 * @returns 验证结果
 */
export function validateMetadata(metadata: any): ValidationResult {
  const errors: ValidationError[] = []

  if (!isObject(metadata)) {
    errors.push({
      field: 'root',
      message: '元数据必须是一个对象',
      value: metadata
    })
    return { valid: false, errors }
  }

  if (!isString(metadata.version) || !metadata.version.trim()) {
    errors.push({
      field: 'version',
      message: '版本号必须是非空字符串',
      value: metadata.version
    })
  }

  if (!isString(metadata.createdAt) || !metadata.createdAt.trim()) {
    errors.push({
      field: 'createdAt',
      message: '创建时间必须是非空字符串',
      value: metadata.createdAt
    })
  }

  if (!isString(metadata.updatedAt) || !metadata.updatedAt.trim()) {
    errors.push({
      field: 'updatedAt',
      message: '更新时间必须是非空字符串',
      value: metadata.updatedAt
    })
  }

  return { valid: errors.length === 0, errors }
}

/**
 * 验证参数定义
 * @param param 参数定义
 * @returns 验证结果
 */
export function validateParameterDefinition(param: any): ValidationResult {
  const errors: ValidationError[] = []
  const validTypes = ['string', 'number', 'boolean', 'object', 'array']

  if (!isObject(param)) {
    errors.push({
      field: 'root',
      message: '参数定义必须是一个对象',
      value: param
    })
    return { valid: false, errors }
  }

  if (!isString(param.name) || !param.name.trim()) {
    errors.push({
      field: 'name',
      message: '参数名称必须是非空字符串',
      value: param.name
    })
  }

  if (!isString(param.type) || !validTypes.includes(param.type)) {
    errors.push({
      field: 'type',
      message: `参数类型必须是以下之一: ${validTypes.join(', ')}`,
      value: param.type
    })
  }

  return { valid: errors.length === 0, errors }
}

/**
 * 验证流程变量
 * @param variable 流程变量
 * @returns 验证结果
 */
export function validateFlowVariable(variable: any): ValidationResult {
  const errors: ValidationError[] = []
  const validTypes = ['string', 'number', 'boolean', 'object', 'array']
  if (!isObject(variable)) {
    errors.push({
      field: 'root',
      message: '流程变量必须是一个对象',
      value: variable
    })
    return { valid: false, errors }
  }

  if (!isString(variable.name) || !variable.name.trim()) {
    errors.push({
      field: 'name',
      message: '变量名称必须是非空字符串',
      value: variable.name
    })
  }

  if (!isString(variable.valueType) || !validTypes.includes(variable.valueType)) {
    errors.push({
      field: 'valueType',
      message: `变量类型必须是以下之一: ${validTypes.join(', ')}`,
      value: variable.valueType
    })
  }

  return { valid: errors.length === 0, errors }
}

/**
 * 类型守卫：检查数据是否为有效的后端流程数据
 * @param data 待检查的数据
 * @returns 类型守卫结果
 */
export function isValidBackendFlowData(data: any): data is BackendFlowData {
  const validation = validateBackendFlowData(data)
  return validation.valid
}
