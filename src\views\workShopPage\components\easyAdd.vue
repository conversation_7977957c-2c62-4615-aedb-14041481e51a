<script lang="ts" setup>
import {computed, onMounted, reactive, ref, useTemplateRef} from "vue";
import {
	NButton,
	NCard,
	NDataTable,
	NDynamicInput,
	NForm,
	NFormItem,
	NInput,
	NInputNumber,
	NModal,
	NRadio,
	NRadioGroup,
	NSelect,
	NSwitch,
	NSlider,
	useMessage,
} from "naive-ui";
import type {DataTableRowKey, FormInst} from "naive-ui";
import {useRoute, useRouter} from "vue-router";
import {QuillEditor} from "@vueup/vue-quill";
import "@vueup/vue-quill/dist/vue-quill.snow.css";
import {SvgIcon} from "@/components/common";
import ChatPreview from "./ChatPreview.vue";
import {
	addConversation,
	addShop,
	agentKnowledge,
	agentParam,
	editShop,
	updateAgent,
	getAgentKnowledge,
	getAgentlistId,
	museModels,
	optimize,
} from "@/api/workShop";

const router = useRouter();
const route = useRoute();

const formRef = ref<FormInst | null>(null);
const message = useMessage();
const size = ref("medium");
const params = ref(JSON.parse(route.query.params as string || ''));
const edit = route.query.edit;
const nameEdit = ref(false);
console.log(edit, params);
const radioCustomList = ref([
	{
		value: "0",
		label: "默认",
		note: "每次对话后自动生成问题建议",
	},
	{
		value: "1",
		label: "自定义",
		note: "自定义",
		disabled: true,
	},
]);
const showModal = ref(false);
// 表单数据
const model = ref({
	knowledgeReferenceFlag: 0,
	multiTurnFlag: "0",
	fileUploadFlag: "0",
	webSearchFlag: "0",
	nextQuestionsSuggestion: "0",
});

// 知识库弹窗数据
const storeDetails = ref({
	callCategory: '0',
	searchCategory: '0',
	topK: '1',
	minMatch: '1',
});

const songs = ref([
	{
		value: "0",
		label: "创意",
	},
	{
		value: "1",
		label: "平衡",
	},
	{
		value: "2",
		label: "严肃",
	},
]);

const rules = {
	modelId: {
		required: true,
		message: "请选择模型",
		trigger: "input",
	},
	promptTemplate: {
		required: true,
		message: "请输入提示词",
		trigger: "input",
	},
};

const options = ref([
	{
		label: "Everybody's Got Something to Hide Except Me and My Monkey",
		value: "song0",
		disabled: true,
	},
	{
		label: "Drive My Car",
		value: "song1",
	},
]);

function jumpPage() {
	router.push('/workShopPage');
}

// 添加知识库弹窗
const data = ref([]);
const table = ref(null);
const loading = ref(true);
const ShowAddStore = ref(false);
const searchvalue = ref();
// 状态管理
const checkedRowKeys = ref<DataTableRowKey[]>([]);
const checkedRows = ref<any[]>([]);
// 创建列配置
const createColumns = () => [
	{
		type: "selection",
		// disabled: (row: any) => row.name === 'Edward King 3',
	},
	{
		title: "智库名称",
		key: "storeName",
	},
	{
		title: "文件数量",
		key: "fileNum",
	},
];

// 表格配置
const columns = createColumns();
const rowKey = (row: any) => row.id;
const pagination = reactive({
	page: 1,
	pageCount: 1,
	pageSize: 10,
	// prefix({ itemCount }) {
	//   return `Total is ${itemCount}.`
	// },
});

// 选中数据
const dataCache: any = ref([]);
const handleCheck = (rowKeys: DataTableRowKey[], row: any) => {
	// console.log(rowKeys)
	if (rowKeys?.length > 10) {
		message.warning('达到关联上限')
		rowKeys.splice(10)
		return
	}
	checkedRowKeys.value = rowKeys;
	// 1. 过滤掉undefined的元素
	const validData = row.filter(item => item !== undefined);
	// 2. 合并新数据与已有数据，并根据id去重
	// 创建一个Map来存储不重复的元素，利用Map的键唯一性
	const dataMap = new Map();

	// 先添加已有数据
	dataCache.value.forEach(item => {
		dataMap.set(item.id, item);
	});

	// 再添加新数据（如果id已存在，会自动覆盖，保持最新）
	validData.forEach(item => {
		dataMap.set(item.id, item);
	});

	// 3. 转换为数组，并过滤出id在allowedIds中的数据
	// dataCache.value = Array.from(dataMap.values());
	dataCache.value = Array.from(dataMap.values())
		.filter(item => rowKeys.includes(item.id));
};

// 知识库列表
const getList = async () => {
	loading.value = true;
	try {
		const agentKnowledgeList = await getAgentKnowledge({
			pageNum: pagination.page,
			pageSize: pagination.pageSize,
			storeName: searchvalue.value,
			deptFlag: true,
			permissionFlag: true,
			source: 0
		});
		data.value = agentKnowledgeList.data.items;
		console.log(data.value)
		pagination.itemCount = agentKnowledgeList.data.total;
		pagination.pageCount = agentKnowledgeList.data.pageNum;
	} catch (error) {
		console.error("Error fetching table data:", error);
	} finally {
		loading.value = false;
	}
};
// 点击新增按钮
const addShowAddStore = async () => {
	ShowAddStore.value = true;
	await getList();
};
// 点击分页
const handlePageChange = async (currentPage: number) => {
	if (!loading.value) {
		pagination.page = currentPage;
		await getList();
	}
};
// 点击保存
const storeSave = () => {
	ShowAddStore.value = false;
	checkedRows.value = dataCache.value;
};
// 删除选中数据
const delStore = (row: any) => {
	checkedRowKeys.value = checkedRowKeys.value.filter((item) => {
		return item !== row.id;
	});
	checkedRows.value = checkedRows.value.filter((item) => {
		return item.id !== row.id;
	});
};
// 新建知识库
const addModalTap = () => {
	router.push("/knowledgeFactoryPage");
};

// 打开模型设置弹窗
const tempModel = ref({})
const realModel = ref({})
let showModalFn = () => {
	Object.assign(tempModel.value, JSON.parse(JSON.stringify(realModel.value)));
	showModal.value = true
}
const updateRadio = (val: any) => {
	console.log(val)
	if (val === 0) {
		return
	}
	let obj: any = songs.value.find((item: any) => {
		return item.model_style == val
	})

	tempModel.value.modelTemp = Number(obj.model_temp);
	tempModel.value.topP = Number(obj.top_p);
};
// 模型温度数据发生变化
let modelTempUpdate = (v: number | null) => {
	console.log(v)
	if (v === null) {
		tempModel.modelTemp = 0
	}
	tempModel.value.modelStyle = 0
}
// topP数据发生变化
let topPUpdate = (v: number | null) => {
	if (v === null) {
		tempModel.topP = 0.1
	}
	tempModel.value.modelStyle = 0
}
// 保存模型参数
let realModelSave = () => {
	Object.assign(realModel.value, JSON.parse(JSON.stringify(tempModel.value)));
	showModal.value = false;
}

// 知识库数据弹窗
const showStore = ref(false);
const storeSongs = ref([
	{
		value: "0",
		label: "强制调用",
	},
	{
		value: "1",
		label: "按需调用",
	},
]);
const searchCategorySongs = ref([
	{
		value: "0",
		label: "混合检索",
	},
	{
		value: "1",
		label: "简易检索",
	},
	{
		value: "2",
		label: "图谱检索",
	},
]);

// 一键优化
const optimizingLogding = ref(false);
const optimizing = async () => {
	optimizingLogding.value = true;
	try {
		await optimize({
			modelTemp: model.value.modelTemp,
			promptTemplate: model.value.promptTemplate,
			modelId: model.value.modelId,
			maxLength: model.value.maxLength,
			onDownloadProgress: ({event}) => {
				const xhr = event.target
				const {responseText} = xhr
				// if (chatStore.netFlag) {
				// 深度思考
				// 按行分割响应文本
				const lines = responseText.split('\n').filter((line: any) => line.trim() !== '')
				for (const line of lines) {
					const trimmedLine = line.replace(/^data: /, '').trim()

					const data = JSON.parse(trimmedLine?.substring(5))
					console.log(data)
					if (data.event === "STOP" || data.event === "stop") {
						model.value.promptTemplate = data.content
					}


				}
			}
		});
		// model.value.promptTemplate = res.data;
		// console.log(res);
	} catch (e) {
		message.error("优化失败");
	} finally {
		optimizingLogding.value = false;
	}
};

onMounted(async () => {
	await agentKnowledge();
	const models = await museModels();
	options.value = models.data;
	const res = await agentParam();
	songs.value = res.data.model_style;
	console.log(res.data)
	if (edit === "true") {
		const detail = await getAgentlistId(params.value.id);
		// console.log(detail)
		// 给模型参数赋默认值
		checkedRows.value = detail.data.agentKnowledgeList || [];
		dataCache.value = detail.data.agentKnowledgeList || [];
		model.value = detail.data;
		realModel.value.modelStyle = Number(detail.data.modelStyle);
		realModel.value.modelTemp = Number(detail.data.modelTemp);
		realModel.value.topP = Number(detail.data.topP);
		realModel.value.maxLength = Number(res.data.model_max_length);
		model.value.preQuestions = JSON.parse(model.value.preQuestions);
		checkedRowKeys.value = detail.data.agentKnowledgeList?.map(
			(item: any) => item.id
		);
		// if (detail.data?.agentKnowledgeBaseDesc) {
		// 	const {id, kid, searchCategory, callCategory, topK, minMatch} =
		// 		detail.data?.agentKnowledgeBaseDesc;
		// 	storeDetails.value = {
		// 		id,
		// 		callCategory: String(callCategory),
		// 		searchCategory: String(searchCategory),
		// 		kid,
		// 		topK,
		// 		minMatch,
		// 	};
		// }
	}
});

// 提交
const submitLoading = ref(false);
// 提交需要的数据
const getMsg = computed(() => {
	return {
		...model.value,
		...params.value,
		...realModel.value,
		preQuestions: JSON.stringify(model.value.preQuestions),
		agentKnowledgeList: checkedRows.value?.map((item) => ({id: item.id})),
		agentKnowledgeBaseDesc: storeDetails.value,
	};
});
const handleValidateButtonClick = async (e: MouseEvent) => {
	e.preventDefault();
	formRef.value?.validate(async (errors) => {
		if (!errors) {
			if (!params.value.name) {
				message.warning("请填写智能体名称");
				return;
			}
			submitLoading.value = true;
			let obj = {
				...getMsg.value, agentKnowledgeList: checkedRows.value?.map((item) => ({id: item.id})),
				agentKnowledgeBaseDesc: storeDetails.value,
				knowledgeReferenceFlag: model.value.knowledgeReferenceFlag == null ? 0 : model.value.knowledgeReferenceFlag,
			};
			console.log(obj);

			try {
				if (edit === "true") await updateAgent(obj, obj.id);
				else await addShop(obj);
				message.success("提交成功");
				submitLoading.value = false;
				await router.push("/workShopPage");
			} catch (e) {
				message.error(e);
			}
		}
	});
};

</script>

<template>
	<div class="p-6">
		<div class="flex items-center justify-between mb-[30px]">
			<div class="flex items-center">
				<img
					alt=""
					class="w-[36px] cursor-pointer"
					src="@/assets/workShopPage/rest.png"
					@click="jumpPage"
				/>
				<div v-if="!nameEdit" class="flex items-center">
					<p class="text-[20px] ml-4 font-medium">
						{{ params.name }}
					</p>
					<!--					<SvgIcon-->
					<!--						class="cursor-pointer ml-[14px] text-[26px] text-[#999]"-->
					<!--						icon="line-md:edit"-->
					<!--						@click="nameEdit = true"-->
					<!--					/>-->
					<img alt="" class="w-[14px] h-[14px] cursor-pointer ml-2" src="@/assets/workShopPage/edit-name.png"
							 @click="nameEdit = true">
				</div>
				<div v-else class="flex items-center ml-[8px]">
					<NInput v-model:value="params.name"/>
					<SvgIcon
						class="cursor-pointer ml-[4px] text-[32px] text-[#999]"
						icon="line-md:confirm"
						@click="nameEdit = false"
					/>
				</div>
			</div>
			<NButton
				:loading="submitLoading"
				round
				type="primary"
				@click="handleValidateButtonClick"
			>
				保存
			</NButton>
		</div>
		<div class="flex gap-6">
			<div class="w-[70%]">
				<NForm
					ref="formRef"
					:model="model"
					:rules="rules"
					:size="size"
					class="h-full"
					label-placement="top"
				>
					<div class="flex h-full">
						<div class="w-1/2">
							<div
								class="flex mb-[18px] text-[18px] text-[#2F3033] font-semibold items-center"
							>
								<div class="w-[6px] h-[18px] backImg mr-2"></div>
								模型设置
							</div>
							<NFormItem label="模型" path="modelId">
								<NSelect
									v-model:value="model.modelId"
									:options="options"
									label-field="name"
									value-field="id"
								/>
								<!--								<SvgIcon-->
								<!--									class="cursor-pointer ml-[14px] text-[26px] text-[#666]"-->
								<!--									icon="material-symbols:edit-note-outline"-->
								<!--									@click="showModal = true"-->
								<!--								/>-->
								<img alt="" class="w-5 h-5 cursor-pointer ml-[14px]" src="@/assets/workShopPage/stting.png"
										 @click="showModalFn">
							</NFormItem>
							<NFormItem class="prompt" label="提示词">
								<template #label>
									<div class="">提示词</div>
									<div class="absolute right-0 bottom-1">
										<NButton
											:disabled="
                        !model.promptTemplate?.length || !model.modelId?.length
                      "
											:loading="optimizingLogding"
											color="#125EFF"
											text
											@click="optimizing"
										>
											一键优化
										</NButton>
									</div>
								</template>
								<NInput
									v-model:value="model.promptTemplate"
									:autosize="{
                    minRows: 6,
                    maxRows: 6,
                  }"
									placeholder="提示词"
									type="textarea"
								/>
							</NFormItem>
							<div
								class="flex mb-[18px] text-[18px] text-[#2F3033] font-semibold items-center"
							>
								<div class="w-[6px] h-[18px] backImg mr-2"></div>
								知识
							</div>
							<NFormItem class="store" path="inputValue">
								<template #label>
									<div class="flex justify-between items-center">
										<div class="">知识库</div>
										<div class="flex items-center">
											<SvgIcon
												class="cursor-pointer text-[22px] text-[#666]"
												icon="material-symbols:add-2"
												@click="addShowAddStore"
											/>
											<!--											<SvgIcon-->
											<!--												class="cursor-pointer ml-[14px] text-[26px] text-[#666]"-->
											<!--												icon="material-symbols:edit-note-outline"-->
											<!--												@click="showStore = true"-->
											<!--											/>-->
											<img alt="" class="w-5 h-5 cursor-pointer ml-[14px]" src="@/assets/workShopPage/stting.png"
													 @click="showStore = true">
										</div>
									</div>
								</template>
								<div class="w-full gap-1 flex flex-col">
									<NCard
										v-for="item in checkedRows"
										:key="item.id"
										size="small"
									>
										<div class="flex w-full justify-between items-center">
											<div class="">
												{{ item.storeName }}
											</div>
											<SvgIcon
												class="text-[20px] text-[#666] cursor-pointer"
												icon="material-symbols:delete-outline"
												@click="delStore(item)"
											/>
										</div>
									</NCard>
								</div>
							</NFormItem>
							<NFormItem
								class="NFormItemRight"
								label="知识引用"
								label-placement="left"
								path="knowledgeReferenceFlag"
							>
								<NSwitch
									v-model:value="model.knowledgeReferenceFlag"
									:checked-value="1"
									:unchecked-value="0"
								/>
							</NFormItem>
						</div>
						<div class="w-[1px] bg-[#E0E0E0] mx-[20px]"/>
						<div class="w-1/2">
							<div
								class="flex mb-[18px] text-[18px] text-[#2F3033] font-semibold items-center"
							>
								<div class="w-[6px] h-[18px] backImg mr-2"></div>
								对话设置
							</div>
							<h5 class="ml-[2px] text-[16px] mb-[14px] font-semibold">
								开场白
							</h5>
							<NFormItem label="开场白文案" path="openingWords">
								<div class="quill-editor-container" style="width: 100%">
									<!-- [{ header: [1, 2, 3, false] }], -->
									<!-- [{ align: [] }], -->
									<QuillEditor
										v-model:content="model.openingWords"
										:toolbar="[
                      ['bold', 'italic', 'underline', 'strike'],
                      [{ color: [] }, { background: [] }],
                      ['clean'],
                    ]"
										content-type="html"
										placeholder="请输入开场白内容..."
										theme="snow"
									/>
								</div>
							</NFormItem>
							<NFormItem label="预置问题" path="preQuestions">
								<NDynamicInput
									v-model:value="model.preQuestions"
									:max="5"
									:min="0"
									placeholder="请输入"
									show-sort-button
								/>
							</NFormItem>
							<NFormItem class="NFormItemRight"
												 label="多轮对话"
												 label-placement="left"
												 path="multiTurnFlag"
							>
								<NSwitch
									v-model:value="model.multiTurnFlag"
									checked-value="1"
									unchecked-value="0"
								/>
							</NFormItem>
							<NFormItem
								class="NFormItemRight" label="文件上传"
								label-placement="left"
								path="fileUploadFlag"
							>
								<NSwitch
									v-model:value="model.fileUploadFlag"
									checked-value="1"
									unchecked-value="0"
								/>
							</NFormItem>
							<NFormItem
								class="NFormItemRight" label="联网搜索"
								label-placement="left"
								path="webSearchFlag"
							>
								<NSwitch
									v-model:value="model.webSearchFlag"
									checked-value="1"
									unchecked-value="0"
								/>
							</NFormItem>
							<!--          <NFormItem label-placement="left" label="下一步问题建议" path="nextQuestionsSuggestion"> -->
							<!--            <NSwitch v-model:value="model.nextQuestionsSuggestion" checked-value="1" unchecked-value="0" /> -->
							<!--          </NFormItem> -->
							<!--          <NFormItem label-placement="left" label=""> -->
							<!--            <radioCustom :list="radioCustomList" value="0" @change="radioCustomChange" /> -->
							<!--          </NFormItem> -->
						</div>
					</div>
				</NForm>
			</div>
			<div class="w-[30%]">
				<div class="border w-full h-[80vh] rounded-[16px]">
					<ChatPreview
						:agent-id="params.id || 'preview'"
						:description="params.description"
						:loading="false"
						:max-length="model.maxLength"
						:model-id="model.modelId"
						:model-temp="model.modelTemp"
						:multiTurnFlag="model.multiTurnFlag"
						:opening-words="model.openingWords"
						:pre-questions="model.preQuestions"
						:prompt-template="model.promptTemplate || ''"
						:title="params.name"
						:topP="model.topP"
					/>
				</div>
			</div>
		</div>
	</div>

	<!-- 模型参数 -->
	<n-modal
		v-model:show="showModal"
		:bordered="false"
		class="custom-card"
		preset="card"
		size="huge"
		style="width: 650px"
		title="模型参数"
	>
		<div class="">
			<NForm
				ref="modelRef"
				label-placement="left"
				label-width="auto"
				require-mark-placement="right-hanging"
			>
				<NFormItem label="模型风格" label-placement="left">
					<NRadioGroup
						v-model:value="tempModel.modelStyle"
						name="radioGroup1"
						@update:value="updateRadio"
					>
						<NRadio
							v-for="song in songs"
							:key="song.model_style"
							:label="song.dictValue"
							:value="song.model_style"
						/>
					</NRadioGroup>
				</NFormItem>
				<NFormItem label="模型温度" label-placement="left">
					<n-slider v-model:value="tempModel.modelTemp" :max="2" :min="0" :step="0.1" @update:value="modelTempUpdate"/>
					<NInputNumber
						v-model:value="tempModel.modelTemp"
						:max="2"
						:min="0"
						:precision="1"
						:show-button="false"
						class="ml-10"
						@update:value="modelTempUpdate"
					/>
				</NFormItem>
				<NFormItem label="Top P" label-placement="left">
					<n-slider v-model:value="tempModel.topP" :max="0.9" :min="0.1" :step="0.1" @update:value="topPUpdate"/>
					<NInputNumber
						v-model:value="tempModel.topP"
						:max="0.9"
						:min="0.1"
						:precision="1"
						:show-button="false"
						class="ml-10"
						@update:value="topPUpdate"
					/>
				</NFormItem>
				<NFormItem label="最大输出长度" label-placement="left">
					<n-slider v-model:value="tempModel.maxLength" :max="32768" :min="1" :step="1"/>
					<NInputNumber
						v-model:value="tempModel.maxLength"
						:max="32768"
						:min="1"
						:precision="0"
						:show-button="false"
						class="ml-10"
						@update:value="(v:number | null)=>{if (v === null){model.maxLength = 1}}"
					/>
				</NFormItem>
			</NForm>
		</div>
		<template #footer>
			<div class="flex flex-row-reverse w-full">
				<NButton type="info" @click="realModelSave"> 保存</NButton>
				<NButton class="!mr-5" @click="showModal = false"> 取消</NButton>
			</div>
		</template>
	</n-modal>

	<!-- 添加知识库 -->
	<n-modal
		v-model:show="ShowAddStore"
		:bordered="false"
		class="custom-card"
		preset="card"
		size="huge"
		style="width: 800px"
		title="添加知识库"
	>
		<div class="">
			<div class="flex mb-5 justify-between">
				<!--          <NInput v-model:value="searchvalue" placeholder="搜索智能体" size="small"> -->

				<!--          </NInput> -->
				<NInput
					v-model:value="searchvalue"
					class="!w-100%"
					placeholder="搜索知识库"
				>
					<template #suffix>
						<div
							class="bg-[#EBF3FF] rounded-full flex items-center justify-center"
						>
							<img
								class="w-[20px] cursor-pointer"
								src="@/assets/toolboxPage/SearchOutline.png"
								@click="getList"
							/>
						</div>
					</template>
				</NInput>
				<NButton v-show="!data?.length" class="!w-[112px] !ml-[24px]" type="info" @click="addModalTap">
					新建知识库
				</NButton>
				<div v-show="data?.length" class="!w-[112px] !ml-[24px]"></div>
			</div>
			<NDataTable
				ref="table"
				v-model:checked-row-keys="checkedRowKeys"
				:columns="columns"
				:data="data"
				:loading="loading"
				:pagination="pagination"
				:row-key="rowKey"
				remote
				@update:page="handlePageChange"
				@update:checked-row-keys="handleCheck"
			/>
		</div>
		<template #footer>
			<div class="flex w-full justify-between items-center">
				<div class="text-[13px] text-[#A4A6AB]">
					已选 {{ checkedRowKeys?.length || 0 }} 个
				</div>
				<div class="flex flex-row-reverse">
					<NButton type="info" @click="storeSave"> 保存</NButton>
					<NButton class="!mr-5" @click="ShowAddStore = false"> 取消</NButton>
				</div>
			</div>
		</template>
	</n-modal>

	<!-- 知识库参数 -->
	<NModal v-model:show="showStore">
		<NCard
			:bordered="false"
			aria-modal="true"
			role="dialog"
			size="huge"
			style="width: 600px"
			title="知识库参数"
		>
			<div class="">
				<NForm
					ref="modelRef"
					:model="storeDetails"
					label-placement="left"
					label-width="auto"
					require-mark-placement="right-hanging"
				>
					<NFormItem label="调用方式">
						<NRadioGroup
							v-model:value="storeDetails.callCategory"
							name="radioGroup2"
						>
							<NRadio
								v-for="song in storeSongs"
								:key="song.value"
								:label="song.label"
								:value="song.value"
							/>
						</NRadioGroup>
					</NFormItem>
					<NFormItem label="检索模式">
						<NRadioGroup
							v-model:value="storeDetails.searchCategory"
							name="radioGroup3"
						>
							<NRadio
								v-for="song in searchCategorySongs"
								:key="song.value"
								:label="song.label"
								:value="song.value"
							/>
						</NRadioGroup>
					</NFormItem>
					<NFormItem label="返回结果数量 （Top K）" label-placement="left">
						<NInputNumber
							v-model:value="storeDetails.topK"
							:min="0"
							:show-button="false"
						/>
					</NFormItem>
					<NFormItem label="最小匹配度 （阈值）" label-placement="left">
						<NInputNumber
							v-model:value="storeDetails.minMatch"
							:min="0"
							:show-button="false"
						/>
					</NFormItem>
				</NForm>
			</div>
			<template #footer>
				<div class="flex flex-row-reverse w-full">
					<NButton type="info" @click="showStore = false"> 保存</NButton>
					<NButton class="!mr-5" @click="showStore = false"> 取消</NButton>
				</div>
			</template>
		</NCard>
	</NModal>
</template>

<style lang="less" scoped>
.NFormItemRight {
	:deep(.n-form-item-blank) {
		flex-direction: row-reverse;
	}
}


.backImg {
	background-image: linear-gradient(180deg, #82FBA5 0%, #058DFC 100%);
	border-radius: 3px;
}

.store {
	:deep(.n-form-item-label__text) {
		width: 100%;
	}
}

.prompt {
	:deep(.n-form-item-label) {
		position: relative;
	}
}

// QuillEditor 样式调整
.quill-editor-container {
	:deep(.ql-toolbar) {
		border-top: 1px solid #ccc;
		border-left: 1px solid #ccc;
		border-right: 1px solid #ccc;
		border-bottom: none;
		border-radius: 6px 6px 0 0;
	}

	:deep(.ql-container) {
		border-bottom: 1px solid #ccc;
		border-left: 1px solid #ccc;
		border-right: 1px solid #ccc;
		border-top: none;
		border-radius: 0 0 6px 6px;
		min-height: 120px;
	}

	:deep(.ql-editor) {
		min-height: 100px;
		font-size: 14px;
		line-height: 1.5;
		padding: 12px 15px;
	}

	:deep(.ql-editor.ql-blank::before) {
		font-style: normal;
		color: #999;
	}
}
</style>
