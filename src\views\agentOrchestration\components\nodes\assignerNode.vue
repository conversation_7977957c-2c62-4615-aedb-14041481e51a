<template>
  <div class="variable-node" :class="{ 'selected': selected, 'running': isRunning }">
    <!-- 输入连接点 -->
    <Handle
      type="target"
      :position="Position.Left"
      :id="`${id}-input`"
      class="input-handle"
    />

    <!-- 节点主体 -->
    <div class="node-body">
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="statusClass"></div>

      <!-- 节点头部 -->
      <div class="node-header">
        <img class="node-icon" src="@/assets/agentOrchestration/variableIcon.png" alt="变量赋值">
        <div class="node-title">{{ data.label || '变量赋值' }}</div>
      </div>

      <!-- 节点描述信息 -->
      <div v-if="data.description" class="node-description">
        {{ data.description }}
      </div>

      <!-- 变量配置 -->
      <div class="variable-config" v-for="item in variablelist" :key="item">
        <div class="config-item">
          <span class="config-label">变量:</span>
          <span class="config-value">{{getNamebyIdFun(item.targetKey) }}</span>
        </div>
        <div class="config-item">
          <span class="config-label">值:</span>
          <span class="config-value">{{ getNamebyIdFun(item.input) }}</span>
        </div>
      </div>
    </div>

    <!-- 输出连接点 -->
    <Handle
      type="source"
      :position="Position.Right"
      :id="`${id}-output`"
      class="output-handle"
    />

    <!-- 节点下方的执行日志显示 -->
    <NodeLogDisplay :node-id="id" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import { NodeStatus } from '@/store/modules/orchestration'
import NodeLogDisplay from '../NodeLogDisplay.vue'
import { useOrchestrationStore } from "@/store";

interface VariableNodeProps {
  id: string
  data: {
    label?: string
    description?: string
    status?: NodeStatus
    config?: {
      items: [{
        targetKey: string
        input: string | number
        writeMode: string
      }]
    }
    [key: string]: any
  }
  selected?: boolean
}
const orchestrationStore = useOrchestrationStore();

const props = defineProps<VariableNodeProps>()
const variablelist = computed(() => {
  return props.data.config?.items || [];
});
const getNamebyIdFun = (id: string) => {
  return orchestrationStore.getVariableById(id)?.name || id;
}

// 计算属性
const isRunning = computed(() => props.data.status === NodeStatus.RUNNING)

const statusClass = computed(() => {
  switch (props.data.status) {
    case NodeStatus.RUNNING:
      return 'status-running'
    case NodeStatus.SUCCESS:
      return 'status-success'
    case NodeStatus.ERROR:
      return 'status-error'
    default:
      return 'status-idle'
  }
})
</script>

<style scoped lang="less">
@import './styles/unified-node-styles.less';

.variable-node {
  .rectangular-node-style();
  .unified-handle-style();

  .node-body {
    .node-header {
      .node-header-style();

      .node-icon {
        width: 16px;
        height: 16px;
        color: #6b7280;
      }
    }

    .node-description {
      .node-description-style();
    }

    .variable-config {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .config-item {
        .node-list-item-style();

        .config-label {
          font-size: 11px;
          color: #6b7280;
          font-weight: 500;
        }

        .config-value {
          font-size: 11px;
          color: #9ca3af;
        }
      }
    }
  }

  .status-indicator {
    .status-indicator-style();
  }
}
</style>
