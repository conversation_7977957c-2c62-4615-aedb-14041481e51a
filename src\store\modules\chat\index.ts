import {defineStore} from 'pinia'
import {marked} from 'marked'
import {defaultState, getLocalState, setLocalState} from './helper'
import {router} from '@/router'
import {updateHistory} from '@/api/sider'
import {infoStore} from '@/store/modules/info'
import {t} from "@/locales";

export const useChatStore = defineStore('chat-store', {
	state: (): Chat.ChatState => getLocalState(),

	getters: {
		getChatHistoryByCurrentActive(state: Chat.ChatState) {
			const index = state.history.findIndex(item => item.uuid === state.active)
			if (index !== -1)
				return state.history[index]
			return null
		},

		getChatByUuid(state: Chat.ChatState) {
			return (uuid?: any) => {
				return state.chat.find(item => item.uuid === uuid)?.data ?? []
			}
		},
	},

	actions: {
		async setState(params?: any, active?: any) {
			// getHistory(params).then((res) => {
			// 	console.log(res)
			//   let arr = []
			//   arr = this.processData(res.data)
			const localState = {
				active: null,
				usingContext: true,
				history: [{uuid: '111', id: '111', data: []}],
				chat: [{uuid: '111', id: '111', data: []}],
			}
			// this.$state = localState
			//   if (active)
			//     this.activeHistory(active)
			// })
		},
		settoolboxInfo(info: object) {
			this.toolboxInfo = info
			this.recordState()
		},
		setUsingContext(context: boolean) {
			this.usingContext = context
			this.recordState()
		},

		activeHistory(uuid: any) {
			console.log('activeHistory执行了')
			this.active = uuid
			this.recordState()
		},

		updateHistory(uuid: any, edit: Partial<Chat.History>, title: string, flag?: boolean) {
			const index = this.history.findIndex(item => item.uuid === uuid)
			if (index !== -1) {
				this.history[index] = {...this.history[index], ...edit}
				this.recordState()
			}

			if (flag) {
				updateHistory(uuid, {
					title,
					userId: infoStore().userInfo.userId,
				}).then(() => {

				})
			}
		},

		editHistory(uuid: string | number, data: any) {
			const index = this.chat.findIndex(item => item.id === uuid || item.uuid === uuid)
			if (index !== -1) {
				this.chat[index].data = data
			} else {
				this.chat.push({
					uuid,
					data,
				})
			}
		},

		async setActive(uuid: any) {
			this.active = uuid
			// return await this.reloadRoute(uuid)
		},
		setnewAddHistory(state: boolean) {
			this.newAddHistory = state
		},
		setrefreshHistory(state: boolean) {
			this.refreshHistory = state
		},

		getChatByUuidAndIndex(uuid: any, index: any) {
			if (!uuid || uuid === 0) {
				if (this.chat.length)
					return this.chat[0].data[index]
				return null
			}
			const chatIndex = this.chat.findIndex(item => item.uuid === uuid)
			if (chatIndex !== -1)
				return this.chat[chatIndex].data[index]
			return null
		},
		addChatByUuid(uuid: any, chat: Chat.Chat) {
			const index = this.chat.findIndex(item => item.uuid === uuid)
			if (index !== -1) {
				this.chat[index].data.push(chat)
				// if (this.history[index].title === '新建会话')
				// this.history[index].title = chat.text
				this.recordState()
			} else {
				this.chat.push({
					uuid,
					data: [chat],
				})
				// this.chat[index].data.push(chat)
			}
		},

		updateChatByUuid(uuid: any, index: any, chat: Chat.Chat) {
			if (!uuid || uuid === 0) {
				if (this.chat.length) {
					this.chat[0].data[index] = chat
					this.recordState()
				}
				return
			}

			const chatIndex = this.chat.findIndex(item => item.uuid === uuid)
			if (chatIndex !== -1) {
				this.chat[chatIndex].data[index] = chat
				this.recordState()
			}
		},

		updateChatSomeByUuid(uuid: any, index: any, chat: Partial<Chat.Chat>) {
			if (!uuid || uuid === 0) {
				if (this.chat.length) {
					this.chat[0].data[index] = {...this.chat[0].data[index], ...chat}
					this.recordState()
				}
				return
			}

			const chatIndex = this.chat.findIndex(item => item.uuid === uuid)
			if (chatIndex !== -1) {
				this.chat[chatIndex].data[index] = {...this.chat[chatIndex].data[index], ...chat}
				this.recordState()
			}
		},

		deleteChatByUuid(uuid: any, index: any) {
			if (!uuid || uuid === 0) {
				if (this.chat.length) {
					this.chat[0].data.splice(index, 1)
					this.recordState()
				}
				return
			}

			const chatIndex = this.chat.findIndex(item => item.uuid === uuid)
			if (chatIndex !== -1) {
				this.chat[chatIndex].data.splice(index, 1)
				this.recordState()
			}
		},

		clearChatByUuid(uuid: any) {
			if (!uuid || uuid === 0) {
				if (this.chat.length) {
					this.chat[0].data = []
					this.recordState()
				}
				return
			}

			const index = this.chat.findIndex(item => item.uuid === uuid)
			if (index !== -1) {
				this.chat[index].data = []
				this.recordState()
			}
		},

		clearHistory() {
			this.$state = {...defaultState()}
			this.recordState()
		},

		async reloadRoute(uuid?: any) {
			this.recordState()
			await router.push({name: 'Chat', params: {uuid}})
		},

		recordState() {
			setLocalState(this.$state)
		},

		processData(data: any) {
			return data.map((item: any) => {
				// 为每个对象添加 uuid 字段，其值等于 id 字段的值
				const newItem = {
					...item,
					uuid: item.sessionId,
					// 将 imMsgHistoryList 改为 data，并处理其中的子元素
					data: (item.imMsgHistoryList || []).map((subItem: any) => (
						{
							...subItem,
							uuid: subItem.conversationId,
							// 聊天记录
							text: subItem.sendUserType === '0' ? (subItem.answer ? (subItem.status === '1' ? JSON.parse(subItem.answer).answer : subItem.answer) : subItem.answer) : subItem.answer,
							// text: subItem.sendUserType === '0' && subItem.status === '1' && subItem.answer ? JSON.parse(subItem.answer).answer : subItem.answer,
							inversion: subItem.sendUserType !== '0',
							// answerList: subItem.sendUserType === '1' ? [] : JSON.parse(subItem.answer).answerList,
							// 搜索来源
							answerList: subItem.sendUserType === '0' ? (subItem.answer ? (subItem.status === '1' ? JSON.parse(subItem.answer).answerList : []) : []) : [],
							endstatus: '1',
						})),
				}
				// 删除原来的 imMsgHistoryList 字段
				delete newItem.imMsgHistoryList
				return newItem
			})
		},
		removeThinkTag(str: string) {
			// 去除 <think> 标签及其内容，支持跨行内容，忽略大小写
			return str.replace(/<think[^>]*>[\s\S]*?<\/think>/gi, '')
		},
		markdownToPlainText(markdown: string) {
			// 先用 marked 转成 HTML
			const html = marked(this.removeThinkTag(markdown))

			// 创建一个 DOM 元素，用它的 textContent 取纯文本
			const tempDiv = document.createElement('div')
			tempDiv.innerHTML = html

			return tempDiv.textContent || tempDiv.innerText || ''
		},
		// 判断是否返回了敏感词
		validateAndAddInitialChats(data?: any, updateChat?: any, index?: number, uuid?: any) {
			// console.log(data)
			if (data.event === "sensitive_words") {
				if (uuid)
					updateChat(uuid, index, {
						loading: false,
						text: '抱歉, 回答内容涉及敏感信息, 请换个话题吧',
					})
				else
					updateChat(index, {
						loading: false,
						text: '抱歉, 回答内容涉及敏感信息, 请换个话题吧',
					})
			}
		}
	},
})
