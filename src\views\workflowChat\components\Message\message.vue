<template>
	<div
		:class="{ 'toast-visible': visible }"
		class="toast"
	>
		<img :src="err" alt="" class="w-4 h-4">
		<p>{{ message }}</p>
	</div>
</template>

<script setup>
import {ref} from 'vue'
import err from '@/assets/chat/err.png'

// 定义组件属性
const props = defineProps({
	message: {
		type: String,
		default: '您发送的内容包含敏感词汇，请修改后再试。'
	},
	duration: {
		type: Number,
		default: 2000 // 默认2秒后消失
	}
})

// 控制显示状态
const visible = ref(false)

// 显示提示的方法
const show = (msg = '', dur = props.duration) => {
	// 如果有自定义消息则使用自定义消息，否则使用props中的消息
	if (msg) {
		// 这里需要使用nextTick确保DOM更新
		setTimeout(() => {
			visible.value = true
		}, 0)
	} else {
		visible.value = true
	}

	// 定时隐藏
	setTimeout(() => {
		visible.value = false
	}, dur)
}

// 暴露方法给父组件
defineExpose({
	show
})
</script>

<style scoped>
.toast {
	position: absolute;
	top: -50px;
	left: 50%;
	transform: translate(-50%, 0);
	font-size: 14px;
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
	z-index: 99999999;
	height: 40px;
	background: #FFF1F0;
	border: 1px solid #FFCCC7;
	border-radius: 8px;
	color: #FF4B4B;
	width: 332px;
	display: flex;
	align-items: center;
	gap: 9px;
	justify-content: center;
}

.toast-visible {
	opacity: 1;
}
</style>
