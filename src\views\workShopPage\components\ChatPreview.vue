<script lang="ts" setup>
import {computed, ref, useTemplateRef, watch} from "vue";
import {NButton, NInput, NSpin} from "naive-ui";
import {Message} from "@/views/tankChat/components";
import {useScroll} from "@/views/tankChat/hooks/useScroll";
import {useBasicLayout} from "@/hooks/useBasicLayout";
import {t} from "@/locales";
import {fetchChatAPIProcess} from "@/api/tankChat";
import {useChatStore} from '@/store'
import messageComponent from '@/views/tankChat/components/Message/message.vue'
import {addConversation} from "@/api/workShop";

interface Props {
	title?: string;
	description?: string;
	openingWords?: string;
	preQuestions?: string[];
	loading?: boolean;
	// 添加聊天所需的参数
	agentId?: string;
	modelId?: string;
	modelTemp?: number;
	maxLength?: number;
	promptTemplate?: string;
	multiTurnFlag?: string;
	topP?: string;
}

const props = withDefaults(defineProps<Props>(), {
	title: "",
	description: "",
	openingWords: "",
	preQuestions: () => [],
	loading: false,
	agentId: "",
	modelId: "",
	modelTemp: 0.7,
	maxLength: 2000,
	promptTemplate: "",
});

defineOptions({
	name: "ChatPreview",
});

const {scrollRef, scrollToBottom, scrollToBottomIfAtBottom} = useScroll();
const {isMobile} = useBasicLayout();
const chatStore = useChatStore()
const prompt = ref("");
const chatLoading = ref(false);
const dataSources = ref<any[]>([]);
let controller = new AbortController();
let conversationId = ref<string>("");
const chatApiLoading = ref(false)
let messageComponentRef = useTemplateRef('messageComponentRef')

// 添加聊天消息
const addChat = (chat: any) => {
	dataSources.value.push(chat);
};

// 更新聊天消息
const updateChat = (index: number, chat: any) => {
	if (index >= 0 && index < dataSources.value.length) {
		dataSources.value[index] = {...dataSources.value[index], ...chat};
	}
};

// 处理预设问题点击
const handlePreQuestionClick = (question: string) => {
	prompt.value = question;
	handleSubmit();
};

// 处理提交 - 真实API调用
const handleSubmit = async () => {
	if (!prompt.value.trim() || chatLoading.value) return;

	const message = prompt.value;

	chatLoading.value = true;
	controller = new AbortController();

	try {
		let lastText = "";
		// 记录是否第一次
		let once = true
		chatApiLoading.value = true
		// 如果没有对话ID，创建一个临时的
		if (!conversationId.value) {
			// conversationId.value = "preview_" + Date.now();
			// 创建对话
			const res = await addConversation({
				agentId: props.agentId,
				title: message,
				category: "0",
			});
			conversationId.value = res.data.id
		}

		const fetchChatAPIOnce = async () => {
			await fetchChatAPIProcess({
				signal: controller.signal,
				question: message,
				conversationId: conversationId.value,
				category: "0",
				agentId: props.agentId,
				modelId: props.modelId,
				modelTemp: props.modelTemp,
				maxLength: props.maxLength,
				promptTemplate: props.promptTemplate,
				multiTurnFlag: props.multiTurnFlag,
				topP: props.topP,
				onDownloadProgress: ({event}) => {
					const xhr = event.target;
					const {responseText} = xhr;

					// 第一次触发判断是不是违规词汇
					if (once) {
						try {
							let data = JSON.parse(responseText)
							if (data.code === "DataCheckFailure") {
								messageComponentRef.value.show()
							}
							return
						} catch (e) {
							prompt.value = "";
							// 添加用户消息
							addChat({
								dateTime: new Date().toLocaleString(),
								text: message,
								inversion: true,
								error: false,
								loading: false,
								answerList: [],
								endstatus: 1,
								conversationOptions: null,
								requestOptions: {prompt: message, options: null},
							});
							// 添加AI思考中的消息
							addChat({
								dateTime: new Date().toLocaleString(),
								text: t("chat.thinking"),
								inversion: false,
								error: false,
								loading: true,
								answerList: [],
								endstatus: 1,
								conversationOptions: null,
							});
							scrollToBottom();
						} finally {
							chatApiLoading.value = false
							once = false
						}
					}

					// 按行分割响应文本
					const lines = responseText
						.split("\n")
						.filter((line: string) => line.trim() !== "");

					// 重置文本,避免重复累加
					lastText = "";

					// 处理每一行数据
					for (const line of lines) {
						const trimmedLine = line.replace(/^data: /, "").trim();

						try {
							const data = JSON.parse(trimmedLine?.substring(5));

							// 直接使用当前响应文本,不进行累加
							const deltaContent = data.choices[0].message.content || "";
							lastText += deltaContent;

							updateChat(dataSources.value.length - 1, {
								dateTime: new Date().toLocaleString(),
								text: lastText,
								inversion: false,
								error: false,
								loading: true,
								conversationOptions: {
									conversationId:
										data.conversationContentId || conversationId.value,
									parentMessageId: data.id || "",
								},
							});

							scrollToBottomIfAtBottom();

							if (
								data.choices[0].finish_reason === "stop" ||
								data.choices[0].finish_reason === "STOP"
							) {
								updateChat(dataSources.value.length - 1, {
									loading: false,
									answerList: data.answerList || [],
								});
								chatLoading.value = false;
							}
							// 判断是否返回了敏感词
							chatStore.validateAndAddInitialChats(data, updateChat, dataSources.value.length - 1)
						} catch (error) {
							console.error("Parse response error:", error);
						}
					}
				},
			});
		};

		await fetchChatAPIOnce();
	} catch (error: any) {
		console.error("Chat API error:", error);

		// 更新最后一条消息为错误状态
		if (dataSources.value.length > 0) {
			updateChat(dataSources.value.length - 1, {
				text: "抱歉，发生了错误，请稍后重试。",
				loading: false,
				error: true,
			});
		}
	} finally {
		chatLoading.value = false;
		scrollToBottom();
	}
};

// 处理Enter键
const handleEnter = (event: KeyboardEvent) => {
	if (!isMobile.value) {
		if (event.key === "Enter" && !event.shiftKey) {
			event.preventDefault();
			handleSubmit();
		}
	} else {
		if (event.key === "Enter" && event.ctrlKey) {
			event.preventDefault();
			handleSubmit();
		}
	}
};

// 计算是否显示开场白
const showWelcome = computed(() => dataSources.value.length === 0);

// 监听props变化，清空聊天记录以显示新的开场白
watch(
	() => [
		props.title,
		props.description,
		props.openingWords,
		props.preQuestions,
	],
	() => {
		// 只有在有实际内容变化时才清空
		if (dataSources.value.length > 0) {
			dataSources.value = [];
		}
	},
	{deep: true}
);
</script>

<template>
	<NSpin :show="chatApiLoading">
		<div class="flex flex-col w-full content-h rounded-[16px] ">
			<!-- 头部标题 -->
			<header class="p-4 rounded-[16px] border-gray-200 bg-white flex-shrink-0">
				<div class="flex items-center">
					<img alt="" class="w-5 mr-2" src="@/assets/workShopPage/test-tit.png"/>
					<p class="text-[16px] font-medium">测试预览</p>
				</div>
			</header>

			<!-- 主聊天区域 -->
			<main class="flex-1 overflow-hidden">
				<div
					id="scrollRef"
					ref="scrollRef"
					class="h-full overflow-hidden overflow-y-auto"
				>
					<NSpin :show="loading">
						<!-- 开场白区域 -->
						<div
							v-if="showWelcome"
							class="w-full m-auto kaichangbai headpadding"
							style="min-height: 58vh"
						>
							<div class="flex flex-col items-center justify-center p-8">
								<img
									alt=""
									class="w-16 h-16 mb-4"
									src="@/assets/applicationPage/icon1.png"
								/>
								<div class="title text-xl font-medium mb-2">
									你好~我是{{ title }}
								</div>
								<div
									class="des text-gray-600 mb-8 text-center"
									v-html="
                  openingWords || '我是您的智能助手，有什么可以帮助您的吗？'
                "
								></div>

								<!-- 预设问题 -->
								<div
									v-if="preQuestions && preQuestions.length > 0"
									class="openingQuestionrow space-y-3 w-full max-w-md"
								>
									<div
										v-for="(question, index) in preQuestions"
										:key="index"
										class="openingQuestionitem cursor-pointer p-3 bg-white rounded-lg border border-gray-200 hover:border-blue-500 hover:shadow-md transition-all duration-200"
										@click="handlePreQuestionClick(question)"
									>
										<div class="flex items-center justify-between">
											<div class="flex items-center">
												<img
													alt=""
													class="w-4 h-4 mr-2"
													src="@/assets/openingicon.png"
												/>
												<span class="text-sm text-gray-700">{{ question }}</span>
											</div>
											<img
												alt=""
												class="w-4 h-4"
												src="@/assets/workShopPage/leftarrow.png"
												style="transform: rotate(180deg)"
											/>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- 聊天消息 -->
						<div v-if="!showWelcome" class="w-full max-w-screen-xl m-auto p-4">
							<Message
								v-for="(item, index) of dataSources"
								:key="index"
								:answer-list="item.answerList"
								:date-time="item.dateTime"
								:error="item.error"
								:inversion="item.inversion"
								:loading="item.loading"
								:text="item.text"
								@delete="() => dataSources.splice(index, 1)"/>
						</div>
					</NSpin>
				</div>
			</main>

			<!-- 底部输入区域 - 固定在底部 -->
			<footer class="relative flex-shrink-0 p-4 rounded-[16px]">
				<messageComponent ref="messageComponentRef"></messageComponent>

				<div class="max-w-screen-xl m-auto relative">

					<!--					<div class="gradient-border relative">-->

					<!--						<div class="gradient-border-cen">-->

					<!--						</div>-->
					<!--					</div>-->
					<NInput
						v-model:value="prompt"
						:bordered="false"
						:disabled="chatLoading"
						:placeholder="
                title ? `向${title}提问` : '有什么问题可以尽管问我哦…'
              "
						class="!rounded-[16px] !bg-[#f2f2f2]"
						size="large"
						@keypress="handleEnter"
					>
						<template #suffix>
							<NButton
								:disabled="!prompt.trim() || chatLoading"
								class="!p-[8px] !rounded-[8px] !w-[30px] !h-[30px]"
								color="#125EFF"
								@click="handleSubmit"
							>
								<img
									alt=""
									class="w-[15px] h-[15px]"
									src="@/assets/workShopPage/test-btn.png"
								/>
							</NButton>
						</template>
					</NInput>
					<p class="text-center text-[#3232334d] text-[12px] mt-[12px]">
						内容由AI生成，请以最新政策文件为准。
					</p>
				</div>
			</footer>
		</div>
	</NSpin>

</template>

<style lang="less" scoped>
.content-h {
	height: calc(80vh - 2px);
}

.gradient-border {
	border-radius: 17px;
	//background-image: linear-gradient(114deg, #ca82ff 0%, #5479f5 100%);

	.gradient-border-cen {
		position: absolute;
		width: calc(100% - 4px);
		background: #fff;
		border-radius: 16px;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		border: 1px solid #ededed;
		box-shadow: 0 0 15px 0 #d8d8d866;
		border-radius: 16px;
		display: flex;
		overflow: hidden;
		align-items: center;
		// padding-right: 20px;

		:deep(.n-input-wrapper) {
			padding-left: 20px;
		}
	}
}

.kaichangbai {
	.title {
		height: 25px;
		font-family: PingFangSC-Medium;
		font-weight: 500;
		font-size: 18px;
		color: #323233;
		letter-spacing: 0;
		text-align: center;
		margin-top: 19px;
		margin-bottom: 10px;
	}

	.des {
		height: 22px;
		font-family: PingFangSC-Regular;
		font-weight: 400;
		font-size: 16px;
		color: #606266;
		letter-spacing: 0;
		text-align: center;
		margin-bottom: 30px;
	}

	.openingQuestionrow {
		.openingQuestionitem:hover {
			cursor: pointer;
			border: 1px solid #125eff;
		}
	}
}

.headpadding {
	padding: 0 20px;
}
</style>
