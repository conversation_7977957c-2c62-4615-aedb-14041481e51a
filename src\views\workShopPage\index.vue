<script lang="ts" setup>
import { computed, onMounted, ref, watch, h } from "vue";
import {
	<PERSON><PERSON>lert,
	NButton,
	NCard,
	NCheckbox,
	NCheckboxGroup,
	NDivider,
	NEllipsis,
	NForm,
	NFormItem,
	NInput,
	NModal,
	NPagination,
	NPopselect,
	NRadio,
	NRadioButton,
	NRadioGroup,
	useDialog,
	useMessage,
	NSpin,
	NSwitch,
} from "naive-ui";
import { useRouter } from "vue-router";
import radioCustom from "./components/radioCustom.vue";
import {
	addShop,
	delModel,
	editShop,
	getAgentlist,
	getAgentlistId,
	getRolesTree,
	updateAgent,
} from "@/api/workShop";
import { getlinksApi } from "@/api/tools";
import icon1 from "@/assets/applicationPage/icon1.png";
import { getpictureDictTextByCodeValue } from "@/utils/microFrontEnd";
import lssue from "@/assets/workShopPage/lssue.png";
import withdraw from "@/assets/workShopPage/withdraw.png";
import { SvgIcon } from "@/components/common";
import { debounce } from "@/utils/functions/debounce";
import { useOrchestrationStore } from "@/store";

// 类型定义
interface AgentItem {
	id: string;
	name: string;
	description: string;
	buildCategory: number;
	scenceCategory: string;
	status: number;
	agentKnowledgeNum: number;
	iconUrl?: string;
	icon?: string;
	agentId?: string;
}

const orchestrationStore = useOrchestrationStore();

const message = useMessage();
const router = useRouter();
const dialog = useDialog();
const page = ref(1);
const pageSize = ref(20);
const pageCount = ref(0);
const searchvalue = ref("");
const edit = ref(false);
const loadingshow = ref(false);
const options = (row: any) => [
	{
		label: "编辑",
		value: "edit",
		disabled: row.status === 1,
	},
	{
		label: "克隆",
		value: "copy",
		disabled: true,
	},
	{
		label: "使用分析",
		value: "use",
		disabled: false,
	},
	{
		label: "访问设置",
		value: "visit",
		disabled: row.status === 0,
	},
	{
		label: "删除",
		value: "del",
	},
];
const moretagarr = ref([
	{ name: "全部", ischeck: true, value: 0 },
	// { name: "教研教学", ischeck: false, value: 1 },
	// { name: "自适应学习", ischeck: false, value: 2 },
	// { name: "校务服务", ischeck: false, value: 3 },
]);
const applicationList = ref<AgentItem[]>([]);
const changmoretagfun = (index: any) => {
	moretagarr.value.forEach((item, i) => {
		item.ischeck = i === index;
	});
};

function getagentlisfun() {
	loadingshow.value = true;

	getAgentlist({
		pageNum: page.value,
		pageSize: pageSize.value,
		name: searchvalue.value,
		permissionFlag: true,
	})
		.then((res: any) => {
			loadingshow.value = false;

			console.log(res, "getagentlisfun  ------ 109");
			if (res.code == "0") {
				res.data.items.forEach((item: AgentItem) => {
					try {
						const iconUrl = getpictureDictTextByCodeValue(
							"profile_picture",
							item.icon || ""
						);
						item.iconUrl = typeof iconUrl === "string" ? iconUrl : "";
					} catch {
						item.iconUrl = "";
					}
				});
				applicationList.value = res.data.items;
				pageCount.value = Number(res.data.total);
			} else {
				message.error(res.message);
			}
		})
		.catch(() => {
			loadingshow.value = false;
		});
}

const updatePage = (v: any) => {
	page.value = v;
	getagentlisfun();
};
const updatePageSize = (v: any) => {
	pageSize.value = v;
	getagentlisfun();
};
const searchTap = debounce(() => {
	page.value = 1;
	getagentlisfun();
}, 500);
onMounted(async () => {
	getagentlisfun();
	getRolesList();
});

// 新建智能体
const showModal = ref(false);
const formRef = ref();
const model = ref<{
	buildCategory: string;
	scenceCategory: string;
	name: string;
	description: string;
	id?: string;
	workflowId?: string;
}>({
	buildCategory: "0",
	scenceCategory: "",
	name: "",
	description: "",
	id: undefined,
});
const rules = {
	buildCategory: {
		required: true,
		message: "请选择构建方式",
		trigger: "input",
	},
	name: {
		required: true,
		message: "请输入智能体名称",
		trigger: "blur",
	},
	scenceCategory: {
		required: true,
		message: "请选择场景类别",
		trigger: "blur",
	},
	description: {
		required: true,
		message: "请输入智能体介绍",
		trigger: "blur",
	},
};

const songs = ref([
	{
		value: "0",
		label: "自适应学习",
	},
	{
		value: "1",
		label: "教研教学",
	},
	{
		value: "2",
		label: "校务服务",
	},
]);
const radioCustomList = computed(() => [
	{
		value: "0",
		label: "简单构建",
		note: "构建简单的对话问答智能体",
		disabled: edit.value ? model.value.buildCategory !== "0" : false,
		icon: "/src/assets/agent/agent-type.png",
	},
	{
		value: "1",
		label: "工作流创建",
		note: "工作流编排创建强大能力智能体",
		disabled: edit.value ? model.value.buildCategory !== "1" : false,
		icon: "/src/assets/agent/agent-type2.png",
	},
]);
// const radioCustomListMsg = ref(radioCustomList())

const offModal = (flag: any) => {
	showModal.value = flag;
};
const afterLeave = () => {
	model.value = {
		buildCategory: "0",
		scenceCategory: "",
		name: "",
		description: "",
		id: undefined,
	};
	edit.value = false;
};

const radioCustomChange = (val: any) => {
	model.value.buildCategory = val.value;
};

const submitLoding = ref(false);
const submit = async (e: any) => {
	e.preventDefault();
	formRef.value?.validate(async (errors: any) => {
		if (!errors) {
			console.log(model.value);
			if (model.value.buildCategory == "0") {
				// 新建时需要直接新建一个智能体
				submitLoding.value = true;
				if (!edit.value) {
					let res = await addShop({ ...model.value, status: 0 });
					console.log(model.value, res);
					model.value.id = res.data.id;
				} else {
					await updateAgent({ ...model.value }, model.value.id as string);
				}
				submitLoding.value = false;
				await router.push({
					path: "/creatIntelligencePage",
					query: {
						params: JSON.stringify(model.value),
						edit: "true",
						// edit: edit.value.toString(),
					},
				});
			} else {
				submitLoding.value = true;
				if (!edit.value) {
					let res = await addShop({ ...model.value, status: 0 });
					console.log(model.value, res);
					model.value.id = res.data.id;
				} else {
					await updateAgent({ ...model.value }, model.value.id as string);
				}

				submitLoding.value = false;
				orchestrationStore.updateNewFlowFrom({
					name: model.value.name,
					description: model.value.description,
					scenceCategory: model.value.scenceCategory,
				});
				await router.push({
					path: "/agentOrchestration",
					query: {
						params: JSON.stringify(model.value),
						edit: String(edit.value),
					},
				});
			}
		} else {
			console.log(errors);
		}
	});
};

// 操作行
const updatePopSelect = async (val: string, item: AgentItem) => {
	console.log(val, item);
	if (val === "del") {
		dialog.warning({
			title: "删除智能体",
			content: () =>
				h("div", {
					innerHTML: `${
						item.status === 1
							? "当前智能体已发布，删除后将下架该智能体并删除，用户将不能继续使用该智能体。"
							: "删除后将无法继续使用该智能体。"
					}<br>你确定要删除当前智能体吗？`,
				}),
			positiveText: "删除",
			negativeText: "取消",
			draggable: true,
			onPositiveClick: async () => {
				await delModel(item.id);
				getagentlisfun();
				message.success("删除成功");
			},
		});
	} else if (val === "edit") {
		const res = (await getAgentlistId(item.id)) as any;
		// if (res.data.buildCategory == "1") {
		// 	await router.push({
		// 		path: "/agentOrchestration",
		// 		query: {
		// 			id: String(item.id),
		// 			name: res.data.name,
		// 			edit: String(edit.value),
		// 		},
		// 	});
		// 	return;
		// }
		edit.value = true;
		const { name, buildCategory, description, scenceCategory, id, workflowId } =
			res.data;
		showModal.value = true;
		model.value = {
			name,
			buildCategory: String(buildCategory),
			description,
			scenceCategory: String(scenceCategory),
			id,
			workflowId,
		};
		console.log(res);
	} else if (val === "visit") {
		await router.push({
			path: "/visit",
			query: {
				id: String(item.id),
				name: item.name,
			},
		});
	} else if (val === "use") {
		await router.push({
			path: "/usageAnalysis",
			query: {
				id: String(item.id),
				agentId: String(item.agentId || item.id), // 传递agentId，如果没有则使用id
				name: item.name,
			},
		});
	}
};

// 发布逻辑
const issueModal = ref(false);
const issueContent = ref<{
	businessId: string[];
	category: string;
	note: string;
	apiVisitStatus: string;
	publicVisitStatus: string;
}>({
	businessId: [],
	category: "0",
	note: "",
	apiVisitStatus: "0",
	publicVisitStatus: "0",
});
const issueContentRules = {
	note: {
		required: true,
		message: "请输入发布说明",
		trigger: "blur",
	},
	businessId: {
		required: true,
		message: "请选择发布范围",
		trigger: "blur",
		type: "array",
		min: 1,
	},
};
const issueMsg = ref<AgentItem | null>(null);

// 角色列表
const rolesList = ref<Array<{ id: string; name: string }>>([]);
// 获取角色列表
const getRolesList = async () => {
	try {
		const res: any = await getRolesTree();
		if (res.code === "0") {
			rolesList.value = res.data.map((item: any) => ({
				id: item.id,
				name: item.name,
			}));
		}
	} catch (error) {
		console.error("获取角色列表失败:", error);
	}
};

// 发布  1已发布  0未发布
const issueFormRef = ref();
const upLoading = ref(false);
const issueSubmit = async (e: any) => {
	e.preventDefault();

	issueFormRef.value?.validate((errors: any) => {
		if (!errors) {
			editShopTap();
		} else {
			console.log(errors);
		}
	});
};
// 取消发布
const changeLssue = (item: AgentItem) => {
	issueMsg.value = item;
	if (item.status === 1) {
		dialog.warning({
			title: "确认要取消发布",
			content: "取消发布后，智能体将对平台用户不可见",
			positiveText: "确定",
			negativeText: "取消",
			draggable: true,
			onPositiveClick: () => {
				editShopTap();
			},
			onNegativeClick: () => {},
		});
	} else {
		issueModal.value = true;
	}
};
const editShopTap = () => {
	if (!issueMsg.value) return;

	let obj: any = null;
	upLoading.value = true;
	if (issueMsg.value.status == 1) {
		//取消发布
		obj = {
			status: 0,
			agentId: issueMsg.value.agentId,
		};
	} else {
		//发布
		obj = {
			status: 3,
			agentId: issueMsg.value.agentId,
			releaseNotes: issueContent.value.note,
			businessId: issueContent.value.businessId.join(","),
			category: issueContent.value.category,
			agentVisitConf: {
				apiVisitStatus: issueContent.value.apiVisitStatus || 0,
				publicVisitStatus: issueContent.value.publicVisitStatus || 0,
			},
		};
		if (issueMsg.value.buildCategory == "1") {
			obj.autoReview = "1";
		}
	}
	if (!obj.agentId) {
		delete obj.agentId;
	}
	if (issueMsg.value) {
		editShop(obj, issueMsg.value.id, issueMsg.value.status)
			.then((res: any) => {
				console.log(res);
				if (res.code == "0") {
					getagentlisfun();
					issueModal.value = false;
					issueMsg.value = null;
					message.success("操作成功");
				} else {
					message.error(res.message);
				}
				upLoading.value = false;
			})
			.catch((err) => {
				upLoading.value = false;
				message.error("操作失败");
			});
	}
};

// 弹窗消失
const afterLeaveEdit = () => {
	issueContent.value = {
		businessId: [],
		category: "0",
		note: "",
		apiVisitStatus: "0",
		publicVisitStatus: "0",
	};
};
</script>

<template>
	<div class="app p-8 pr-[37px] pl-[40px] pb-[80px]">
		<n-spin :show="loadingshow">
			<header class="bothends flex justify-between items-center">
				<div
					class="title h-9 font-semibold text-[26px] text-[#2f3033] leading-9 flex items-center"
				>
					<img
						alt=""
						class="w-[22px] h-[22px] mr-2"
						src="@/assets/toolboxPage/titicon.png"
					/>
					智能体工坊
				</div>
				<div class="w-[400px] h-12">
					<NInput
						v-model:value="searchvalue"
						class="modern-search-input"
						clearable
						placeholder="搜索智能体"
						round
						size="large"
						@update:value="searchTap"
					>
						<template #prefix>
							<div class="search-prefix-icon">
								<img
									class="w-[18px] h-[18px] opacity-60"
									src="../../assets/toolboxPage/SearchOutline.png"
								/>
							</div>
						</template>
						<template #suffix>
							<div class="search-suffix-btn" @click="searchTap">
								<img
									class="w-[18px] h-[18px]"
									src="../../assets/toolboxPage/SearchOutline.png"
								/>
							</div>
						</template>
					</NInput>
				</div>
			</header>

			<div class="collectbox flex flex-wrap">
				<div
					v-for="(item, index) in moretagarr"
					:key="index"
					:class="{ 'category-tag-active': item.ischeck }"
					class="category-tag"
					@click="changmoretagfun(index)"
				>
					{{ item.name }}
				</div>
			</div>
			<div class="applicationrow">
				<div class="create-card" @click="showModal = true">
					<div class="create-icon">+</div>
					<div class="create-title">创建你的专属智能体</div>
				</div>

				<div
					v-for="(item, index) in applicationList"
					:key="index"
					class="application-card"
				>
					<div class="card-content">
						<div class="app-icon-wrapper">
							<img :src="icon1" class="app-icon" />
						</div>
						<div class="app-info">
							<div class="app-header">
								<h3 class="app-name">{{ item.name }}</h3>
								<div class="app-tags">
									<span v-if="item.buildCategory === 1" class="app-tag"
										>工作流</span
									>
									<span v-if="item.buildCategory === 0" class="app-tag"
										>问答</span
									>
									<div v-if="item.status != 3" class="app-actions">
										<NPopselect
											:options="options(item)"
											trigger="hover"
											@update:value="updatePopSelect($event, item)"
										>
											<SvgIcon
												class="cursor-pointer"
												icon="material-symbols:more-vert"
											/>
										</NPopselect>
									</div>
								</div>
							</div>
							<div class="app-description">
								<NEllipsis :line-clamp="2">
									{{ item.description }}
									<template #tooltip>
										<div style="text-align: center; width: 250px">
											{{ item.description }}
										</div>
									</template>
								</NEllipsis>
							</div>
						</div>
					</div>

					<div class="card-divider"></div>

					<div class="app-meta">
						<div class="knowledge-info">
							<img src="@/assets/workShopPage/books.png" />
							<span>{{ item.agentKnowledgeNum }}</span>
						</div>
						<div class="publish-status">
							<div
								:class="{ 'status-published': item.status == 1 }"
								class="status-indicator"
							>
								<span class="status-dot"></span>
							</div>
							<span class="status-text">{{
								item.status == 1
									? "已发布"
									: item.status == 0
									? "未发布"
									: "审核中"
							}}</span>
							<img
								v-if="item.status != 3"
								:src="item.status !== 1 ? lssue : withdraw"
								class="status-action"
								@click="changeLssue(item)"
							/>
						</div>
					</div>
				</div>
			</div>
			<div class="fixed right-[20px] bottom-[16px]">
				<NPagination
					v-model:page="page"
					:item-count="pageCount"
					:on-update:page="updatePage"
					:on-update:page-size="updatePageSize"
					:page-size="pageSize"
					:page-sizes="[10, 20, 50, 100]"
					show-quick-jumper
					show-size-picker
				/>
			</div>
		</n-spin>
	</div>

	<!-- 新建 -->
	<NModal v-model:show="showModal" @after-leave="afterLeave">
		<NCard
			:bordered="false"
			:title="edit ? '编辑智能体' : '新建智能体'"
			aria-modal="true"
			role="dialog"
			size="huge"
			style="
				width: 1000px;
				height: 728px;
				border-radius: 8px;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
			"
		>
			<div class="flex h-full">
				<!-- 左侧表单和按钮区域 -->
				<div class="flex-1 flex flex-col pr-6" style="max-width: 550px">
					<div class="flex-1 overflow-y-auto" style="max-height: 550px">
						<NForm
							ref="formRef"
							:model="model"
							:rules="rules"
							label-placement="top"
						>
							<NFormItem class="mb-4" label="构建方式" path="buildCategory">
								<div class="flex space-x-3">
									<div
										v-for="(item, index) in radioCustomList"
										:key="item.value"
										:class="{
											'border-blue-500 bg-blue-50':
												model.buildCategory === item.value,
											'border-gray-300': model.buildCategory !== item.value,
											'opacity-50 cursor-not-allowed': item.disabled,
										}"
										class="flex-1 border border-solid border-gray-300 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:border-blue-400"
										@click="!item.disabled && radioCustomChange(item)"
									>
										<div class="flex items-center mb-2">
											<img
												v-if="index == 0"
												src="@/assets/agent/agent-type.png"
												alt=""
												class="w-4 h-4 mr-2"
											/>
											<img
												v-if="index == 1"
												src="@/assets/agent/agent-type2.png"
												alt=""
												class="w-4 h-4 mr-2"
											/>
											<span class="text-sm font-medium text-gray-900">{{
												item.label
											}}</span>
										</div>
										<div class="text-xs text-gray-500 ml-6">
											{{ item.note }}
										</div>
									</div>
								</div>
							</NFormItem>
							<NFormItem class="mb-4" label="智能体名称" path="name">
								<NInput
									v-model:value="model.name"
									placeholder="请输入智能体名称"
								/>
							</NFormItem>
							<NFormItem class="mb-4" label="智能体介绍" path="description">
								<NInput
									v-model:value="model.description"
									maxlength="200"
									placeholder="请输入智能体介绍"
									show-count
									type="textarea"
								/>
							</NFormItem>
							<NFormItem class="mb-0" label="场景类别" path="scenceCategory">
								<div class="p-1 rounded-lg" style="background-color: #f9fafc">
									<NRadioGroup
										v-model:value="model.scenceCategory"
										name="radiobuttongroup1"
									>
										<NRadio
											v-for="song in songs"
											:key="song.value"
											:label="song.label"
											:value="song.value"
											style="margin-right: 30px"
										/>
									</NRadioGroup>
								</div>
							</NFormItem>
						</NForm>
					</div>

					<!-- 按钮区域 -->
					<div
						class="flex flex-row-reverse w-full pt-4 border-t border-gray-200"
					>
						<NButton :loading="submitLoding" type="info" @click="submit">
							确认</NButton
						>
						<NButton class="!mr-5" @click="offModal(false)"> 取消</NButton>
					</div>
				</div>

				<!-- 右侧图片 -->
				<div
					class="flex-shrink-0 flex items-center justify-center"
					style="width: 400px; height: 100%; margin-top: -40px"
				>
					<img
						v-if="model.buildCategory === '0'"
						alt="智能体创建"
						src="@/assets/agent/agent-banner.png"
						style="
							width: 391px;
							height: 674px;
							object-fit: contain;
							border-radius: 8px;
						"
					/>
					<img
						v-else
						alt="智能体创建"
						src="@/assets/agent/agent-banner2.png"
						style="
							width: 391px;
							height: 674px;
							object-fit: contain;
							border-radius: 8px;
						"
					/>
				</div>
			</div>
		</NCard>
	</NModal>

	<!-- 发布 -->
	<NModal v-model:show="issueModal" @after-leave="afterLeaveEdit">
		<NCard
			:bordered="false"
			aria-modal="true"
			role="dialog"
			size="huge"
			style="width: 700px"
			title="发布智能体"
		>
			<!--			<NAlert class="mb-[20px]" type="info">-->
			<!--				智能体发布后，将对平台用户可见-->
			<!--			</NAlert>-->
			<NForm
				ref="issueFormRef"
				:model="issueContent"
				:rules="issueContentRules"
				label-placement="top"
			>
				<NFormItem label="发布说明" path="note">
					<NInput
						v-model:value="issueContent.note"
						maxlength="200"
						placeholder="发布说明"
						show-count
						type="textarea"
					/>
				</NFormItem>
				<NFormItem label="发布范围" path="businessId">
					<div class="mb-2 text-sm text-gray-600">
						已选择 {{ issueContent.businessId.length }} 个角色
					</div>
					<NCheckboxGroup
						v-model:value="issueContent.businessId"
						name="businessIdGroup"
						class="roles-checkbox-group"
					>
						<div class="flex flex-wrap gap-3">
							<NCheckbox
								v-for="role in rolesList"
								:key="role.id"
								:label="role.name"
								:value="role.id"
								class="role-checkbox"
							/>
						</div>
					</NCheckboxGroup>
				</NFormItem>
				<NFormItem label="API发布" label-placement="left">
					<NSwitch
						v-model:value="issueContent.apiVisitStatus"
						checked-value="1"
						unchecked-value="0"
					/>
				</NFormItem>
				<NFormItem label="URL发布" label-placement="left">
					<NSwitch
						v-model:value="issueContent.publicVisitStatus"
						checked-value="1"
						unchecked-value="0"
					/>
				</NFormItem>
			</NForm>
			<template #footer>
				<div class="flex flex-row-reverse w-full">
					<NButton :loading="upLoading" type="info" @click="issueSubmit">
						确认
					</NButton>
					<NButton class="!mr-5" @click="issueModal = false"> 取消</NButton>
				</div>
			</template>
		</NCard>
	</NModal>
</template>

<style lang="less" scoped>
/deep/ .n-form-item .n-form-item-blank {
	display: block;
}

// 表单标签样式
:deep(.n-form-item-label__text) {
	font-family: PingFangSC-Semibold;
	font-weight: 600;
	font-size: 15px;
	color: #323233;
}

.app {
	background: url("@/assets/topbg.png") no-repeat;
	background-size: 90% 220px;
	background-position-x: 5%;
}

// 现代化搜索框样式
.modern-search-input {
	:deep(.n-input-wrapper) {
		padding-right: 4px;
		padding-left: 16px;
		border: none;
		background: transparent;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
		transition: all 0.3s ease;
		border-radius: 3rem;

		&:hover {
			box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
		}

		&.n-input-wrapper--focus {
			box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.15),
				0 4px 16px rgba(0, 0, 0, 0.1);
		}
	}

	:deep(.n-input__input-el) {
		font-size: 15px;
		color: #2f3033;

		&::placeholder {
			color: #9ca3af;
			font-weight: 400;
		}
	}
}

.search-prefix-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 8px;
}

.search-suffix-btn {
	width: 36px;
	height: 36px;
	background: linear-gradient(135deg, #125eff 0%, #1e7fff 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.2s ease;

	&:hover {
		transform: scale(1.05);
		box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
	}

	&:active {
		transform: scale(0.98);
	}

	img {
		filter: brightness(0) invert(1);
	}
}

// 分类标签样式
.category-tag {
	width: 128px;
	height: 40px;
	background: #ffffff;
	border: 1px solid #e5e7eb;
	border-radius: 20px;
	font-weight: 500;
	font-size: 14px;
	color: #6b7280;
	line-height: 40px;
	text-align: center;
	margin-right: 12px;
	margin-bottom: 12px;
	cursor: pointer;
	transition: all 0.2s ease;

	&:hover {
		border-color: #125eff;
		color: #125eff;
		box-shadow: 0 2px 8px rgba(18, 94, 255, 0.1);
	}

	&.category-tag-active {
		background: linear-gradient(135deg, #125eff 0%, #1e7fff 100%);
		border-color: #125eff;
		color: #ffffff;
		box-shadow: 0 4px 12px rgba(18, 94, 255, 0.25);
	}
}

.collectbox {
	margin-top: 32px;
}

// 应用卡片网格布局
.applicationrow {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(330px, 1fr));
	gap: 20px;
	margin-top: 32px;
}

// 创建卡片样式
.create-card {
	background: linear-gradient(135deg, #e7f9ff 0%, #f5f5ff 100%);
	border: 2px dashed #125eff;
	border-radius: 16px;
	padding: 40px 24px;
	text-align: center;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	//min-height: 200px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;

	&:hover {
		background: linear-gradient(135deg, #d1e9ff 0%, #e8f0ff 100%);
		border-color: #0052cc;
		transform: translateY(-2px);
		box-shadow: 0 8px 32px rgba(18, 94, 255, 0.15);
	}
}

.create-icon {
	width: 48px;
	height: 48px;
	border-radius: 50%;
	background: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32px;
	color: #125eff;
	margin-bottom: 16px;
	box-shadow: 0 2px 8px rgba(18, 94, 255, 0.1);
}

.create-title {
	font-size: 18px;
	font-weight: 500;
	color: #125eff;
	line-height: 24px;
}

// 现代化应用卡片设计
.application-card {
	background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
	border: 1px solid #e8ecf0;
	border-radius: 16px;
	padding: 24px;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

	&:hover {
		background: #ffffff;
		border-color: #125eff;
		box-shadow: 0 8px 32px rgba(18, 94, 255, 0.12);
		transform: translateY(-2px);
	}
}

.card-content {
	margin-bottom: 16px;
}

.app-icon-wrapper {
	position: relative;
	margin-right: 20px;
	float: left;

	// 默认状态的微妙背景
	&::before {
		content: "";
		position: absolute;
		top: -6px;
		left: -6px;
		right: -6px;
		bottom: -6px;
		background: linear-gradient(
			135deg,
			rgba(18, 94, 255, 0.05) 0%,
			rgba(30, 127, 255, 0.02) 100%
		);
		border-radius: 18px;
		opacity: 1;
		transition: all 0.3s ease;
	}

	.application-card:hover &::before {
		background: linear-gradient(
			135deg,
			rgba(18, 94, 255, 0.15) 0%,
			rgba(30, 127, 255, 0.08) 100%
		);
		transform: scale(1.05);
	}
}

.app-icon {
	width: 64px;
	height: 64px;
	border-radius: 12px;
	position: relative;
	z-index: 1;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;

	.application-card:hover & {
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
		transform: scale(1.02);
	}
}

.app-info {
	overflow: hidden;
}

.app-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 12px;
}

.app-name {
	font-size: 18px;
	font-weight: 600;
	color: #1f2937;
	line-height: 24px;
	margin: 0;
	flex: 1;
	transition: color 0.2s ease;

	.application-card:hover & {
		color: #125eff;
	}
}

.app-tags {
	display: flex;
	align-items: center;
	gap: 8px;
}

.app-tag {
	font-size: 12px;
	color: #6b7280;
	background: rgba(107, 114, 128, 0.1);
	border: 1px solid rgba(107, 114, 128, 0.2);
	border-radius: 12px;
	padding: 2px 8px;
	line-height: 16px;
}

.app-actions {
	display: flex;
	align-items: center;
}

.app-description {
	font-size: 14px;
	color: #6b7280;
	line-height: 20px;
	margin: 0;
	clear: both;
}

.card-divider {
	height: 1px;
	background: linear-gradient(
		90deg,
		transparent 0%,
		#e5e7eb 20%,
		#e5e7eb 80%,
		transparent 100%
	);
	margin: 16px 0;
}

.app-meta {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.knowledge-info {
	display: flex;
	align-items: center;
	font-size: 13px;
	color: #6b7280;

	img {
		width: 16px;
		height: 16px;
		margin-right: 6px;
		opacity: 0.8;
	}
}

.publish-status {
	display: flex;
	align-items: center;
	font-size: 13px;
	color: #6b7280;
	gap: 8px;
}

.status-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 14px;
	height: 14px;
	border-radius: 50%;
	background: #e5e7eb;

	&.status-published {
		background: #c0f1cc;
	}
}

.status-dot {
	width: 4px;
	height: 4px;
	border-radius: 50%;
	background: #9ca3af;

	.status-published & {
		background: #30d158;
	}
}

.status-text {
	font-size: 13px;
	color: #6b7280;
}

.status-action {
	width: 16px;
	height: 16px;
	cursor: pointer;
	transition: all 0.2s ease;

	&:hover {
		transform: scale(1.1);
	}
}

@media (max-width: 768px) {
	.app {
		padding: 16px 20px;
	}

	.bothends {
		flex-direction: column;
		gap: 20px;
		align-items: flex-start;
	}

	.applicationrow {
		grid-template-columns: 1fr;
	}

	.card-content {
		padding: 20px;
	}
}
</style>
