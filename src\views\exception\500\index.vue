<script lang="ts" setup>
import { NButton } from 'naive-ui'
import { useRouter } from 'vue-router'
import { ref, onMounted } from 'vue'
import Icon500 from '@/icons/500.vue'

const router = useRouter()
const isVisible = ref(false)

function goHome() {
  router.push('/')
}

// 添加进入动画
onMounted(() => {
  setTimeout(() => {
    isVisible.value = true
  }, 100)
})
</script>

<template>
  <div class="error-container">
    <div class="error-content" :class="{ 'fade-in': isVisible }">
      <!-- 500 数字显示 -->
      <div class="error-number">
        <span class="number-5">5</span>
        <span class="number-0">0</span>
        <span class="number-0">0</span>
      </div>

      <!-- 错误信息 -->
      <div class="error-info">
        <h1 class="error-title">服务器错误</h1>
        <p class="error-description">
          抱歉，服务器出现内部错误。
          <br>
          请稍后重试，或联系系统管理员。
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="error-actions">
        <NButton
          type="primary"
          size="large"
          class="action-button primary-button"
          @click="goHome"
        >
          返回首页
        </NButton>
      </div>

      <!-- 装饰元素 -->
      <div class="decoration">
        <div class="floating-shape shape-1"></div>
        <div class="floating-shape shape-2"></div>
        <div class="floating-shape shape-3"></div>
      </div>
      
      <!-- 500 图标 -->
      <div class="error-icon">
        <Icon500 class="icon-svg" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.error-content {
  text-align: center;
  max-width: 600px;
  width: 100%;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.error-content.fade-in {
  opacity: 1;
  transform: translateY(0);
}

/* 500 数字样式 */
.error-number {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
  gap: 10px;
}

.error-number span {
  font-size: 120px;
  font-weight: 800;
  color: #334155;
  text-shadow: 0 4px 20px rgba(51, 65, 85, 0.1);
  animation: bounce 2s infinite;
}

.number-0 {
  animation-delay: 0.2s;
  color: #3b82f6;
}

.number-0:last-child {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 错误信息样式 */
.error-info {
  margin-bottom: 40px;
}

.error-title {
  font-size: 32px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  text-shadow: none;
}

.error-description {
  font-size: 16px;
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

/* 按钮样式 */
.error-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 30px;
}

.action-button {
  min-width: 140px;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.primary-button {
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  border: none;
  color: white;
}

.primary-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

/* 装饰元素 */
.decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.05);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
  background: rgba(59, 130, 246, 0.08);
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
  background: rgba(16, 185, 129, 0.06);
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
  background: rgba(245, 158, 11, 0.07);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 500 图标样式 */
.error-icon {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.icon-svg {
  width: 200px;
  height: auto;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-number span {
    font-size: 80px;
  }

  .error-title {
    font-size: 24px;
  }

  .error-description {
    font-size: 14px;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .action-button {
    width: 200px;
  }
  
  .icon-svg {
    width: 150px;
  }
}

@media (max-width: 480px) {
  .error-container {
    padding: 15px;
  }

  .error-number span {
    font-size: 60px;
  }

  .error-title {
    font-size: 20px;
  }

  .floating-shape {
    display: none;
  }
  
  .icon-svg {
    width: 120px;
  }
}
</style>
