<template>
  <div class="usage-analysis-container">
    <div class="page-header">
      <div class="header-left">
        <n-button text @click="goBack" class="back-button">
          ← 
        </n-button>
        <span class="agent-name">{{ agentName || '智能体名称' }}</span>
      </div>
    </div>
    
    <div class="content-wrapper">
      <n-tabs v-model:value="activeTab" type="line" animated>
        <n-tab-pane name="conversation" tab="会话">
          <UsageOverview :snapshot-agent-id="snapshotAgentId" :agent-id="agentId" />
        </n-tab-pane>
        <n-tab-pane name="analysis" tab="分析">
          <UsageStatistics :snapshot-agent-id="snapshotAgentId" :agent-id="agentId" />
        </n-tab-pane>
      </n-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { NTabs, NTabPane, NButton } from "naive-ui";
import { useRoute, useRouter } from "vue-router";
import UsageOverview from "./components/UsageOverview.vue";
import UsageStatistics from "./components/UsageStatistics.vue";

const activeTab = ref("conversation");
const route = useRoute();
const router = useRouter();
const agentName = ref("");

// 从路由参数获取ID信息
const agentId = ref((route.query.agentId as string) || '');
const snapshotAgentId = ref((route.query.id as string) || ''); // 使用分析页面本身的ID作为snapshotAgentId

const goBack = () => {
  router.back();
};

onMounted(() => {
  // 从路由参数获取智能体名称
  agentName.value = (route.query.name as string) || "";
});
</script>

<style scoped>
.usage-analysis-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-button {
  color: #666;
  font-size: 18px;
}

.back-button:hover {
  color: #1890ff;
}

.agent-name {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.content-wrapper {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.n-tabs-nav) {
  margin-bottom: 24px;
}

:deep(.n-tab-pane) {
  padding: 0;
}
</style>