<template>
  <div class="message-item" :class="{
    'message-user': message.role === 'user',
    'message-assistant': message.role === 'assistant',
    'message-flat': message.role === 'assistant' && aiMessageMode === 'flat'
  }">
    <!-- 头像 (非flat模式时显示) -->
    <div v-if="aiMessageMode !== 'flat' && (message.role === 'user' || message.role === 'assistant')" class="message-avatar">
      <div v-if="message.role === 'assistant'" class="avatar-ai">
        <img :src="aiAvatar" alt="AI" class="avatar-image" />
      </div>
      <div v-else class="avatar-user">
        <img :src="userAvatar" alt="User" class="avatar-image" />
      </div>
    </div>

    <!-- 消息内容 -->
    <div class="message-content">
      <!-- 统一消息气泡 -->
      <div class="message-body" :class="{ 'loading': message.loading }">
        <!-- 流程阶段显示 -->
        <div v-if="message.stages?.length && message.role === 'assistant'" class="message-stages">
          <div
            v-for="stage in message.stages"
            :key="stage.id"
            class="stage-item"
            :class="{
              'stage-running': stage.status === 'running',
              'stage-complete': stage.status === 'complete',
              'stage-error': stage.status === 'error'
            }"
          >
            <div class="stage-header" @click="toggleStage(stage.id)">
              <div class="stage-indicator">
                <div v-if="stage.status === 'running'" class="stage-spinner"></div>
                <div v-else-if="stage.status === 'complete'" class="stage-check">✓</div>
                <div v-else-if="stage.status === 'error'" class="stage-error-icon">✗</div>
                <div v-else class="stage-pending">○</div>
              </div>
              <span class="stage-name">{{ stage.name }}</span>
              <span
                v-if="stage.status === 'complete' && stage.content"
                class="stage-toggle"
                :class="{ 'expanded': !stage.isCollapsed }"
              >
                <img src="/src/assets/chat/rightrow.png" alt="展开" class="toggle-icon" />
              </span>
            </div>

            <!-- 阶段内容 -->
            <div
              v-if="stage.content && (stage.status === 'running' || !stage.isCollapsed)"
              class="stage-content"
              :class="{ 'stage-content-collapsed': stage.isCollapsed }"
            >
              <div class="stage-text" v-html="renderMarkdown(stage.content)"></div>
            </div>
          </div>
        </div>

        <!-- 思考过程（兼容旧版本） -->
        <div v-else-if="message.thinking && message.role === 'assistant'" class="thinking-section">
          <div class="thinking-header" @click="toggleThinking">
            <div class="thinking-indicator">
              <div v-if="!message.isThinkingComplete" class="thinking-spinner"></div>
              <div v-else class="thinking-check">🤔</div>
            </div>
            <span class="thinking-text">思考过程</span>
            <span
              v-if="message.isThinkingComplete"
              class="thinking-toggle"
              :class="{ 'expanded': !isThinkingCollapsed }"
            >
              <img src="/src/assets/chat/rightrow.png" alt="展开" class="toggle-icon" />
            </span>
          </div>

          <div
            v-if="!message.isThinkingComplete || !isThinkingCollapsed"
            class="thinking-content"
            :class="{ 'thinking-content-collapsed': isThinkingCollapsed && message.isThinkingComplete }"
          >
            <div class="thinking-text-content" v-html="renderMarkdown(message.thinking)"></div>
          </div>
        </div>

        <!-- 主要回答内容 -->
        <div class="main-content">
          <div v-if="message.loading && !displayedContent" class="loading-indicator">
            <div class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          <div v-else-if="displayedContent || message.content" class="message-text" v-html="renderMarkdown(getDisplayContent())"></div>

          <!-- 错误状态 -->
          <div v-if="message.error" class="error-message">
            <span class="error-icon">⚠️</span>
            <span>消息发送失败，请重试</span>
          </div>
        </div>
      </div>

      <!-- 附件 -->
      <div v-if="message.attachments?.length" class="message-attachments">
        <div v-for="attachment in message.attachments" :key="attachment.id" class="attachment-item">
          <img v-if="attachment.type === 'image'" :src="attachment.url" :alt="attachment.name" class="attachment-image" />
          <div v-else class="attachment-file">
            <span class="attachment-icon">📄</span>
            <span class="attachment-name">{{ attachment.name }}</span>
          </div>
        </div>
      </div>

      <!-- 用户消息自定义插槽 -->
      <div v-if="message.role === 'user'" class="user-message-extra">
        <slot
          name="user-message-extra"
          :message="message"
          :startEdit="startEdit"
          :showEdit="showEdit"
        />
      </div>

      <!-- 操作按钮 -->
      <div v-if="message.role === 'assistant' && !message.loading" class="message-actions">
        <!-- 默认操作按钮（始终显示） -->
        <button
          class="action-btn"
          :class="{ 'active': message.feedback?.type === 'like' }"
          @click="handleFeedback('like')"
          title="有帮助"
        >
          <img src="/src/assets/toolboxPage/good.png" alt="点赞" class="action-icon" />
        </button>
        <button
          class="action-btn"
          :class="{ 'active': message.feedback?.type === 'dislike' }"
          @click="handleFeedback('dislike')"
          title="没有帮助"
        >
          <img src="/src/assets/toolboxPage/loser.png" alt="点踩" class="action-icon" />
        </button>
        <button class="action-btn" @click="handleCopy" title="复制">
          <img src="/src/assets/chat/copy.png" alt="复制" class="action-icon" />
        </button>
        <button class="action-btn" @click="handleRegenerate" title="重新生成">
          <img src="/src/assets/regenerate.png" alt="重新生成" class="action-icon" />
        </button>

        <!-- 额外的自定义操作按钮插槽 -->
        <slot
          name="message-actions"
          :message="message"
          :handleFeedback="handleFeedback"
          :handleCopy="handleCopy"
          :handleRegenerate="handleRegenerate"
        />
      </div>

      <!-- 用户消息编辑 -->
      <div v-if="message.role === 'user' && showEdit" class="message-edit">
        <textarea v-model="editContent" class="edit-textarea"></textarea>
        <div class="edit-actions">
          <button class="btn-cancel" @click="cancelEdit">取消</button>
          <button class="btn-save" @click="saveEdit">保存</button>
        </div>
      </div>
      <button v-else-if="message.role === 'user'" class="edit-btn" @click="startEdit" title="编辑">
        <img src="/src/assets/edit.png" alt="编辑" class="action-icon" />
      </button>
    </div>
  </div>

  <!-- 反馈弹窗 -->
  <div v-if="showFeedbackModal" class="feedback-modal-overlay" @click="closeFeedbackModal">
    <div class="feedback-modal" @click.stop>
      <h3>请告诉我们原因</h3>
      <div class="feedback-options">
        <label v-for="option in feedbackOptions" :key="option.value" class="feedback-option">
          <input type="radio" v-model="selectedFeedbackReason" :value="option.value" />
          <span>{{ option.label }}</span>
        </label>
      </div>
      <textarea v-model="feedbackComment" placeholder="其他意见（可选）" class="feedback-textarea"></textarea>
      <div class="feedback-actions">
        <button class="btn-cancel" @click="closeFeedbackModal">取消</button>
        <button class="btn-submit" @click="submitFeedback">提交</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue'
import { marked } from 'marked'
import type { Message, MessageFeedback, MessageStage } from '../types'

interface Props {
  message: Message
  aiAvatar?: string
  userAvatar?: string
  aiMessageMode?: 'bubble' | 'flat'  // AI消息展示模式
}

interface Emits {
  (e: 'feedback', messageId: string, feedback: MessageFeedback): void
  (e: 'copy', content: string): void
  (e: 'regenerate', messageId: string): void
  (e: 'edit', messageId: string, content: string): void
}

const props = withDefaults(defineProps<Props>(), {
  aiAvatar: '/src/assets/msgHead.png',
  userAvatar: '/src/assets/usericon.png'
})

const emit = defineEmits<Emits>()

// 逐字符动画相关变量（模仿原有Text组件）
const displayedContent = ref<string>('') // 当前显示的内容
const originalContent = ref<string>('') // 完整的原始内容
const remainContent = ref<string>('') // 剩余待显示内容
const isAnimating = ref<boolean>(false) // 是否正在动画中
const isFinished = ref<boolean>(false) // 是否已完成动画
const isStreamReceiving = ref<boolean>(false) // 是否正在接收流式数据
let lastProcessedLength = 0 // 上次处理的内容长度
let animationFrameId: number | null = null // 动画帧ID

// 编辑功能
const showEdit = ref(false)
const editContent = ref('')

// 反馈功能
const showFeedbackModal = ref(false)
const selectedFeedbackReason = ref('')
const feedbackComment = ref('')

const feedbackOptions = [
  { value: 'inaccurate', label: '答案不准确' },
  { value: 'irrelevant', label: '内容不相关' },
  { value: 'harmful', label: '有害信息' },
  { value: 'other', label: '其他' }
]

// 思考过程折叠功能
const isThinkingCollapsed = ref(true)

const toggleThinking = () => {
  isThinkingCollapsed.value = !isThinkingCollapsed.value
}

// 阶段折叠功能
const toggleStage = (stageId: string) => {
  if (props.message.stages) {
    const stage = props.message.stages.find(s => s.id === stageId)
    if (stage && stage.status === 'complete' && stage.content) {
      stage.isCollapsed = !stage.isCollapsed
    }
  }
}

// 重置动画状态（模仿原有Text组件）
const resetTextAnimation = () => {
  stopTextAnimation()
  displayedContent.value = ''
  originalContent.value = ''
  remainContent.value = ''
  isAnimating.value = false
  isFinished.value = false
  isStreamReceiving.value = false
  lastProcessedLength = 0
}

// 停止动画
const stopTextAnimation = () => {
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId)
    animationFrameId = null
  }
}

// 开始文字动画（模仿原有Text组件）
const startTextAnimation = (fullText: string) => {
  stopTextAnimation()

  originalContent.value = fullText || ''
  displayedContent.value = ''
  remainContent.value = fullText || ''
  isAnimating.value = true
  isFinished.value = false

  animateResponseText()
}

// 更新流式内容（模仿原有Text组件）
const updateStreamContent = (newText: string, oldText: string) => {
  if (newText.length > oldText.length) {
    // 有新内容，更新原始文本并继续动画
    originalContent.value = newText
    const newPart = newText.substring(oldText.length)
    remainContent.value += newPart
    isStreamReceiving.value = true

    if (!isAnimating.value) {
      isAnimating.value = true
      animateResponseText()
    }
  }
}

// 动画函数（修复版本）
const animateResponseText = () => {
  // 如果动画已完成且无新内容，则结束动画
  if (isFinished.value && !isStreamReceiving.value) {
    displayedContent.value = originalContent.value
    animationFrameId = null
    console.log('[MessageItem] 动画完成，最终内容长度:', displayedContent.value.length)
    return
  }

  if (remainContent.value.length > 0) {
    // 简化字符处理，每次处理1-2个字符
    const fetchCount = isStreamReceiving.value ? 1 : Math.min(2, remainContent.value.length)

    const fetchText = remainContent.value.slice(0, fetchCount)
    displayedContent.value += fetchText
    remainContent.value = remainContent.value.slice(fetchCount)

    console.log('[MessageItem] 动画进度:', {
      displayed: displayedContent.value.length,
      remain: remainContent.value.length,
      original: originalContent.value.length,
      isStreamReceiving: isStreamReceiving.value
    })
  }

  // 检查是否需要继续动画
  const shouldContinue = remainContent.value.length > 0 || isStreamReceiving.value

  if (shouldContinue) {
    // 继续动画，使用较慢的速度确保可见效果
    setTimeout(() => {
      animationFrameId = requestAnimationFrame(animateResponseText)
    }, 50) // 50ms延迟，确保可见的打字效果
  } else {
    // 动画结束
    isFinished.value = true
    isAnimating.value = false
    displayedContent.value = originalContent.value
    console.log('[MessageItem] 动画自然结束')
  }
}

// 监听消息内容变化，实现流式动画效果（修复版本）
watch(() => props.message.content, (newContent, oldContent) => {
  console.log('[MessageItem] 内容变化:', {
    role: props.message.role,
    loading: props.message.loading,
    newLength: newContent?.length || 0,
    oldLength: oldContent?.length || 0,
    newContent: newContent?.substring(0, 50) + '...'
  })

  if (props.message.role === 'user') {
    // 用户消息直接显示
    displayedContent.value = newContent || ''
    return
  }

  if (!props.message.loading) {
    // AI消息加载完成，直接显示完整内容
    displayedContent.value = newContent || ''
    resetTextAnimation()
    return
  }

  if (!newContent) {
    // 内容为空，重置状态
    resetTextAnimation()
    return
  }

  // 检查是否为新的对话（内容完全变化）
  const isNewConversation = !oldContent || oldContent.length === 0 || !newContent.startsWith(oldContent)
  if (isNewConversation) {
    console.log('[MessageItem] 新对话，开始动画')
    // 新的对话，重置并开始新动画
    resetTextAnimation()
    startTextAnimation(newContent)
  } else if (newContent !== oldContent && newContent.length > oldContent.length) {
    console.log('[MessageItem] 流式更新')
    // 流式数据更新，追加新内容
    updateStreamContent(newContent, oldContent || '')
  }
}, { immediate: true })

// 监听loading状态变化
watch(() => props.message.loading, (isLoading) => {
  if (!isLoading && props.message.role === 'assistant' && props.message.content) {
    // 加载完成，确保显示完整内容
    if (isAnimating.value) {
      isStreamReceiving.value = false
      isFinished.value = true
    } else {
      displayedContent.value = props.message.content
    }
  }
})

// 组件卸载时清理动画
onUnmounted(() => {
  stopTextAnimation()
})

// 获取要显示的内容
const getDisplayContent = () => {
  // 如果是用户消息或者不是加载状态，直接返回完整内容
  if (props.message.role === 'user' || !props.message.loading) {
    return props.message.content || ''
  }

  // AI消息且正在加载，返回动画显示的内容
  return displayedContent.value || ''
}

// Markdown渲染
const renderMarkdown = (content: string) => {
  return marked(content)
}

// 反馈处理
const handleFeedback = (type: 'like' | 'dislike') => {
  if (type === 'dislike') {
    showFeedbackModal.value = true
  } else {
    emit('feedback', props.message.id, { type })
  }
}

const submitFeedback = () => {
  emit('feedback', props.message.id, {
    type: 'dislike',
    reason: selectedFeedbackReason.value,
    category: feedbackComment.value
  })
  closeFeedbackModal()
}

const closeFeedbackModal = () => {
  showFeedbackModal.value = false
  selectedFeedbackReason.value = ''
  feedbackComment.value = ''
}

// 复制功能
const handleCopy = () => {
  emit('copy', props.message.content)
}

// 重新生成
const handleRegenerate = () => {
  emit('regenerate', props.message.id)
}

// 编辑功能
const startEdit = () => {
  editContent.value = props.message.content
  showEdit.value = true
}

const cancelEdit = () => {
  showEdit.value = false
  editContent.value = ''
}

const saveEdit = () => {
  emit('edit', props.message.id, editContent.value)
  showEdit.value = false
}
</script>

<style scoped>
.message-item {
  display: flex;
  margin-bottom: 32px;
  gap: 20px;
  animation: fadeInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-user {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  position: relative;
}

.avatar-image {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  object-fit: cover;
  box-shadow:
    0 4px 16px rgba(18, 94, 255, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.04);
  border: 3px solid rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.message-avatar::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.15) 0%, rgba(18, 94, 255, 0.08) 100%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.message-item:hover .message-avatar::after {
  opacity: 1;
}

.avatar-ai {
  background: linear-gradient(135deg, #f8faff 0%, #eef4ff 100%);
  border-radius: 50%;
  padding: 8px;
  border: 1px solid rgba(18, 94, 255, 0.12);
  box-shadow:
    0 4px 16px rgba(18, 94, 255, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.02);
}

.avatar-user {
  background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
  border-radius: 50%;
  padding: 6px;
  box-shadow: 0 2px 8px rgba(18, 94, 255, 0.3);
}

.message-content {
  flex: 1;
  max-width: calc(100% - 56px);
}

/* 用户消息内容容器 */
.message-user .message-content {
  display: flex;
  justify-content: flex-end;
}

/* 流程阶段样式 */
.message-stages {
  margin-bottom: 20px;
}

.stage-item {
  margin-bottom: 12px;
  border-radius: 12px;
  border: 1px solid rgba(18, 94, 255, 0.08);
  background: linear-gradient(135deg, rgba(248, 250, 255, 0.6) 0%, rgba(240, 244, 255, 0.6) 100%);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stage-item.stage-running {
  border-color: rgba(18, 94, 255, 0.2);
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.05) 0%, rgba(18, 94, 255, 0.02) 100%);
}

.stage-item.stage-complete {
  border-color: rgba(34, 197, 94, 0.2);
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(34, 197, 94, 0.02) 100%);
}

.stage-item.stage-error {
  border-color: rgba(239, 68, 68, 0.2);
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(239, 68, 68, 0.02) 100%);
}

.stage-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
}

.stage-header:hover {
  background: rgba(18, 94, 255, 0.03);
}

.stage-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.stage-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(18, 94, 255, 0.2);
  border-top: 2px solid #125EFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.stage-check {
  width: 16px;
  height: 16px;
  background: #22c55e;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

.stage-error-icon {
  width: 16px;
  height: 16px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

.stage-pending {
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  color: #9ca3af;
}

.stage-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.stage-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stage-toggle.expanded {
  transform: rotate(90deg);
}

.stage-content {
  padding: 0 16px 16px 48px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  max-height: 1000px;
}

.stage-content-collapsed {
  max-height: 0;
  opacity: 0;
  padding-bottom: 0;
}

.stage-text {
  font-size: 13px;
  line-height: 1.6;
  color: #4b5563;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(18, 94, 255, 0.05);
}

/* 思考过程样式（兼容旧版本） */
.thinking-section {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid rgba(18, 94, 255, 0.08);
  background: linear-gradient(135deg, rgba(248, 250, 255, 0.6) 0%, rgba(240, 244, 255, 0.6) 100%);
  overflow: hidden;
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
}

.thinking-header:hover {
  background: rgba(18, 94, 255, 0.03);
}

.thinking-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.thinking-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(18, 94, 255, 0.2);
  border-top: 2px solid #125EFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.thinking-check {
  font-size: 16px;
}

.thinking-text {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.thinking-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.thinking-toggle.expanded {
  transform: rotate(90deg);
}

.thinking-content {
  padding: 0 16px 16px 48px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  max-height: 1000px;
}

.thinking-content-collapsed {
  max-height: 0;
  opacity: 0;
  padding-bottom: 0;
}

.thinking-text-content {
  font-size: 13px;
  line-height: 1.6;
  color: #4b5563;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(18, 94, 255, 0.05);
}

/* 主要内容区域 */
.main-content:not(:first-child) {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(18, 94, 255, 0.06);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 通用图标样式 */
.toggle-icon {
  width: 12px;
  height: 12px;
  object-fit: contain;
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(97%) contrast(101%);
}

/* 内容样式优化 */
.stage-text :deep(h1),
.stage-text :deep(h2),
.stage-text :deep(h3),
.stage-text :deep(h4),
.stage-text :deep(h5),
.stage-text :deep(h6),
.thinking-text-content :deep(h1),
.thinking-text-content :deep(h2),
.thinking-text-content :deep(h3),
.thinking-text-content :deep(h4),
.thinking-text-content :deep(h5),
.thinking-text-content :deep(h6) {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 8px 0 4px 0;
}

.stage-text :deep(p),
.thinking-text-content :deep(p) {
  margin: 4px 0;
  font-size: 13px;
  line-height: 1.5;
}

.stage-text :deep(ul),
.stage-text :deep(ol),
.thinking-text-content :deep(ul),
.thinking-text-content :deep(ol) {
  margin: 4px 0;
  padding-left: 16px;
  font-size: 13px;
}

.stage-text :deep(li),
.thinking-text-content :deep(li) {
  margin: 2px 0;
}

.stage-text :deep(code),
.thinking-text-content :deep(code) {
  background: rgba(18, 94, 255, 0.1);
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 12px;
  color: #125EFF;
}



.message-body {
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  border: 1px solid rgba(18, 94, 255, 0.08);
  border-radius: 20px;
  padding: 16px 20px;
  position: relative;
  box-shadow:
    0 8px 32px rgba(18, 94, 255, 0.04),
    0 4px 16px rgba(0, 0, 0, 0.02),
    0 2px 8px rgba(0, 0, 0, 0.01);
  font-size: 16px;
  line-height: 1.7;
  color: #1f2937;
  word-wrap: break-word;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.02) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.message-item:hover .message-body::before {
  opacity: 1;
}

.message-user .message-body {
  background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
  color: white;
  border-color: transparent;
  box-shadow:
    0 8px 32px rgba(18, 94, 255, 0.3),
    0 4px 16px rgba(18, 94, 255, 0.2),
    0 2px 8px rgba(18, 94, 255, 0.1);
  /* 用户消息动态宽度 */
  display: inline-block;
  max-width: 100%;
  width: auto;
}

.message-user .message-body::before {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
}

/* 平铺模式样式 */
.message-flat {
  margin-bottom: 24px;
}

.message-flat .message-content {
  max-width: 100%;
  margin-left: 0;
}

.message-flat .message-body {
  background: linear-gradient(135deg, #f8faff 0%, #f1f5f9 100%);
  border: 1px solid rgba(18, 94, 255, 0.06);
  border-radius: 12px;
  padding: 20px 24px;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.02),
    0 2px 8px rgba(18, 94, 255, 0.03);
  color: #374151;
}

.message-flat .message-body::before {
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.01) 0%, transparent 100%);
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
}

.typing-dots {
  display: flex;
  gap: 6px;
  align-items: center;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
  border-radius: 50%;
  animation: typing 1.6s infinite ease-in-out;
  box-shadow: 0 2px 4px rgba(18, 94, 255, 0.3);
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.3s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  30% {
    transform: translateY(-12px) scale(1.1);
    opacity: 1;
  }
}

.message-actions {
  display: flex;
  gap: 10px;
  margin-top: 16px;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(4px);
}

.message-item:hover .message-actions {
  opacity: 1;
  transform: translateY(0);
}

.action-btn {
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  border: 1px solid rgba(18, 94, 255, 0.12);
  border-radius: 10px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #125EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
}

.action-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(97%) contrast(101%);
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: linear-gradient(135deg, #f8faff 0%, #eef4ff 100%);
  border-color: rgba(18, 94, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(18, 94, 255, 0.15);
}

.action-btn:hover .action-icon {
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(120%) contrast(101%);
  transform: scale(1.1);
}

.action-btn.active {
  background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
}

.action-btn.active .action-icon {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

.action-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(18, 94, 255, 0.2);
}

.feedback-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.feedback-modal {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 400px;
  width: 90%;
}

.feedback-options {
  margin: 16px 0;
}

.feedback-option {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  cursor: pointer;
}

.feedback-textarea {
  width: 100%;
  min-height: 80px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 8px;
  resize: vertical;
}

.feedback-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
}

.btn-cancel, .btn-submit, .btn-save {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-submit, .btn-save {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-cancel:hover, .btn-submit:hover, .btn-save:hover {
  opacity: 0.8;
}

.message-edit {
  margin-top: 12px;
}

.edit-textarea {
  width: 100%;
  min-height: 60px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 8px;
  resize: vertical;
}

.edit-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.edit-btn {
  background: none;
  border: none;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
  font-size: 14px;
}

.message-item:hover .edit-btn {
  opacity: 1;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #dc3545;
  font-size: 14px;
}

.message-attachments {
  margin-top: 12px;
}

.attachment-item {
  margin-bottom: 8px;
}

.attachment-image {
  max-width: 200px;
  border-radius: 8px;
}

.attachment-file {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .message-item {
    margin-bottom: 16px;
  }

  .message-content {
    max-width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .user-message .message-content {
    margin-left: 20px;
  }

  .assistant-message .message-content {
    margin-right: 20px;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    min-width: 32px;
  }

  .message-avatar img {
    width: 32px;
    height: 32px;
  }

  .message-body {
    padding: 10px 14px;
    border-radius: 16px;
  }

  .message-text {
    font-size: 14px;
    line-height: 1.5;
  }

  .message-actions {
    gap: 8px;
    margin-top: 8px;
  }

  .action-btn {
    width: 32px;
    height: 32px;
    min-width: 32px;
    padding: 6px;
  }

  .action-icon {
    width: 16px;
    height: 16px;
  }

  .thinking-section {
    margin-bottom: 12px;
  }

  .thinking-header {
    padding: 10px 12px;
    border-radius: 12px;
  }

  .thinking-text {
    font-size: 13px;
  }

  .thinking-content {
    padding: 8px 12px;
    border-radius: 8px;
    margin-top: 8px;
  }

  .thinking-text-content {
    font-size: 13px;
    line-height: 1.4;
  }

  .message-stages .stage-item {
    margin-bottom: 8px;
  }

  .stage-header {
    padding: 8px 12px;
    border-radius: 10px;
  }

  .stage-name {
    font-size: 13px;
  }

  .stage-content {
    padding: 8px 12px;
    margin-top: 6px;
    border-radius: 8px;
  }

  .stage-text {
    font-size: 13px;
    line-height: 1.4;
  }

  .message-edit {
    margin-top: 12px;
  }

  .edit-textarea {
    font-size: 14px;
    padding: 12px;
    border-radius: 12px;
  }

  .edit-actions {
    gap: 8px;
    margin-top: 8px;
  }

  .edit-btn {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 8px;
  }

  .message-timestamp {
    font-size: 11px;
    margin-top: 6px;
  }

  .message-feedback {
    margin-top: 8px;
  }

  .feedback-buttons {
    gap: 8px;
  }

  .feedback-btn {
    width: 28px;
    height: 28px;
    min-width: 28px;
  }

  .feedback-icon {
    width: 14px;
    height: 14px;
  }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
  .message-item {
    margin-bottom: 12px;
  }

  .message-content {
    max-width: calc(100% - 40px);
  }

  .user-message .message-content {
    margin-left: 10px;
  }

  .assistant-message .message-content {
    margin-right: 10px;
  }

  .message-avatar {
    width: 28px;
    height: 28px;
    min-width: 28px;
  }

  .message-avatar img {
    width: 28px;
    height: 28px;
  }

  .message-body {
    padding: 8px 12px;
    border-radius: 14px;
  }

  .message-text {
    font-size: 13px;
  }

  .message-actions {
    gap: 6px;
    margin-top: 6px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
    min-width: 28px;
    padding: 4px;
  }

  .action-icon {
    width: 14px;
    height: 14px;
  }

  .thinking-header {
    padding: 8px 10px;
    border-radius: 10px;
  }

  .thinking-text {
    font-size: 12px;
  }

  .thinking-content {
    padding: 6px 10px;
    margin-top: 6px;
  }

  .thinking-text-content {
    font-size: 12px;
  }

  .stage-header {
    padding: 6px 10px;
    border-radius: 8px;
  }

  .stage-name {
    font-size: 12px;
  }

  .stage-content {
    padding: 6px 10px;
    margin-top: 4px;
  }

  .stage-text {
    font-size: 12px;
  }
}
</style>
