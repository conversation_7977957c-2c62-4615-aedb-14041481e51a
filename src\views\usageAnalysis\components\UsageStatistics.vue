<template>
	<div class="usage-statistics">
		<!-- 时间范围选择器 -->
		<div class="flex justify-end items-center mb-6">
			<n-select
				v-model:value="selectedPeriod"
				:options="timePeriodOptions"
				style="width: 120px"
				size="small"
				:loading="loading"
			/>
		</div>

		<!-- 加载状态 -->
		<div v-if="loading" class="text-center py-8">
			<n-spin size="medium" />
			<div class="mt-2 text-gray-500">正在加载统计数据...</div>
		</div>

		<!-- 使用分析 -->
		<div v-show="!loading" class="section-container">
			<h3 class="section-title">使用分析</h3>
			<div class="metrics-grid">
				<div
					v-for="metric in usageMetrics"
					:key="metric.name"
					class="metric-card"
				>
					<div class="metric-header">
						<span class="metric-name">{{ metric.name }}</span>
					</div>
					<div class="metric-value">{{ metric.value }}</div>
					<div class="metric-change" :class="metric.changeType">
						<span class="change-icon">{{
							metric.changeType === "increase" ? "↗" : "↘"
						}}</span>
						<span>{{ metric.change }}</span>
						<span class="change-period">{{ metric.period }}</span>
					</div>
				</div>
			</div>
		</div>

		<!-- 资源分析 -->
		<div v-show="!loading" class="section-container">
			<h3 class="section-title">资源分析</h3>
			<div class="metrics-grid">
				<div
					v-for="metric in resourceMetrics"
					:key="metric.name"
					class="metric-card"
				>
					<div class="metric-header">
						<span class="metric-name">{{ metric.name }}</span>
					</div>
					<div class="metric-value">{{ metric.value }}</div>
					<div class="metric-change" :class="metric.changeType">
						<span class="change-icon">{{
							metric.changeType === "increase" ? "↗" : "↘"
						}}</span>
						<span>{{ metric.change }}</span>
						<span class="change-period">{{ metric.period }}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { NSelect, NSpin } from "naive-ui";
import { getUseAnalysis, getResourceAnalysis } from "@/api/workShop";

// 定义props
interface Props {
	snapshotAgentId?: string;
	agentId?: string;
}

const props = withDefaults(defineProps<Props>(), {
	snapshotAgentId: "",
	agentId: "",
});

const selectedPeriod = ref("3");
const loading = ref(false);

const timePeriodOptions = [
	{ label: "今天", value: "0" },
	{ label: "近7天", value: "1" },
	{ label: "近30天", value: "2" },
	{ label: "全部", value: "3" },
];

const usageMetrics = ref([
	{
		name: "累计用户 (人)",
		value: "1,126",
		change: "20.1%",
		period: "较昨日",
		changeType: "increase",
	},
	{
		name: "今日对话 (次)",
		value: "1,126",
		change: "20.1%",
		period: "较昨日",
		changeType: "increase",
	},
	{
		name: "对话轮数 (轮)",
		value: "1,126",
		change: "20.1%",
		period: "较昨日",
		changeType: "increase",
	},
	{
		name: "今日对话时长 (分钟)",
		value: "2.22",
		change: "20.1%",
		period: "较昨日",
		changeType: "increase",
	},
	{
		name: "对话满意度 (%)",
		value: "66",
		change: "20.1%",
		period: "较昨日",
		changeType: "increase",
	},
]);

const resourceMetrics = ref([
	{
		name: "Token消耗总量",
		value: "0",
		change: "0%",
		period: "较昨日",
		changeType: "unchanged",
	},
	{
		name: "输入Token消耗",
		value: "0",
		change: "0%",
		period: "较昨日",
		changeType: "unchanged",
	},
	{
		name: "输出Token消耗",
		value: "0",
		change: "0%",
		period: "较昨日",
		changeType: "unchanged",
	},
	{
		name: "Token输出速度",
		value: "0/s",
		change: "0%",
		period: "较昨日",
		changeType: "unchanged",
	},
	{
		name: "平均响应耗时",
		value: "0ms",
		change: "0%",
		period: "较昨日",
		changeType: "unchanged",
	},
]);

onMounted(() => {
	loadStatisticsData();
});

// 监听时间范围变化
watch(selectedPeriod, () => {
	loadStatisticsData();
});

async function loadStatisticsData() {
	try {
		loading.value = true;

		// 构建与conversation_list一致的参数
		const params = {
			page_num: 1,
			page_size: 10,
			timeRange: selectedPeriod.value,
			agentId: props.agentId,
			snapshotAgentId: props.snapshotAgentId,
			title: "",
			category: "1", // 默认查询正式会话
		};

		console.log("Props值:", {
			agentId: props.agentId,
			snapshotAgentId: props.snapshotAgentId,
		});
		console.log("调用分析接口，参数:", params);

		// 依次调用两个接口
		const useAnalysisResult = await getUseAnalysis(params);
		const resourceAnalysisResult = await getResourceAnalysis(params);

		// 处理使用分析数据
		if (
			(useAnalysisResult as any).code === "0" ||
			(useAnalysisResult as any).code === 0
		) {
			updateUsageMetrics((useAnalysisResult as any).data);
		} else {
			console.error(
				"加载使用分析数据失败:",
				(useAnalysisResult as any).message
			);
		}

		// 处理资源分析数据
		if (
			(resourceAnalysisResult as any).code === "0" ||
			(resourceAnalysisResult as any).code === 0
		) {
			updateResourceMetrics((resourceAnalysisResult as any).data);
		} else {
			console.error(
				"加载资源分析数据失败:",
				(resourceAnalysisResult as any).message
			);
		}
	} catch (error: any) {
		console.error("加载统计数据失败:", error);
		console.error("错误详情:", {
			message: error?.message,
			status: error?.status,
			response: error?.response,
		});

		// 如果是401错误，给出更明确的提示
		if (error?.status === 401) {
			console.warn("认证失败，请检查token是否有效");
			window.$message?.error("认证失败，请重新登录");
		}
	} finally {
		loading.value = false;
	}
}

// 更新使用分析指标
function updateUsageMetrics(data: any) {
	if (!data) return;

	console.log("使用分析数据:", data);

	// 获取环比变化类型的显示文本
	const getChangeType = (changeFlag: string) => {
		switch (changeFlag) {
			case "0":
				return "increase";
			case "1":
				return "decrease";
			case "2":
				return "unchanged";
			default:
				return "unchanged";
		}
	};

	// 格式化数字显示
	const formatNumber = (num: number) => {
		if (num >= 1000) {
			return (num / 1000).toFixed(1) + "k";
		}
		return num.toString();
	};

	// 格式化百分比
	const formatRatio = (ratio: number) => {
		return Math.abs(ratio).toFixed(1) + "%";
	};

	// 更新使用指标数据
	usageMetrics.value = [
		{
			name: "使用人次",
			value: formatNumber(data.numberOfUsers || 0),
			change: formatRatio(data.numberOfUsersRatio || 0),
			period: "环比",
			changeType: getChangeType(data.numberOfUsersRatioChange),
		},
		{
			name: "会话数",
			value: formatNumber(data.numberOfConversations || 0),
			change: formatRatio(data.numberOfConversationsRatio || 0),
			period: "环比",
			changeType: getChangeType(data.numberOfConversationsRatioChange),
		},
		{
			name: "对话次数",
			value: formatNumber(data.numberOfDialogues || 0),
			change: formatRatio(data.numberOfDialoguesRatio || 0),
			period: "环比",
			changeType: getChangeType(data.numberOfDialoguesRatioChange),
		},
		{
			name: "平均交互次数",
			value: (data.averageNumberOfInteractions || 0).toFixed(1),
			change: formatRatio(data.averageNumberOfInteractionsRatio || 0),
			period: "环比",
			changeType: getChangeType(data.averageNumberOfInteractionsRatioChange),
		},
		{
			name: "对话失败次数",
			value: formatNumber(data.numberOfFailedDialogues || 0),
			change: formatRatio(data.numberOfFailedDialoguesRatio || 0),
			period: "环比",
			changeType: getChangeType(data.numberOfFailedDialoguesRatioChange),
		},
	];
}

// 更新资源分析指标
function updateResourceMetrics(data: any) {
	if (!data) return;

	console.log("资源分析数据:", data);

	// 获取环比变化类型的显示文本
	const getChangeType = (changeFlag: string) => {
		switch (changeFlag) {
			case "0":
				return "increase";
			case "1":
				return "decrease";
			case "2":
				return "unchanged";
			default:
				return "unchanged";
		}
	};

	// 格式化数字显示
	const formatNumber = (num: number) => {
		if (num >= 1000000) {
			return (num / 1000000).toFixed(1) + "M";
		} else if (num >= 1000) {
			return (num / 1000).toFixed(1) + "k";
		}
		return num.toString();
	};

	// 格式化百分比
	const formatRatio = (ratio: number) => {
		return Math.abs(ratio).toFixed(1) + "%";
	};

	// 格式化响应时间（毫秒转秒）
	const formatResponseTime = (ms: number) => {
		if (ms >= 1000) {
			return (ms / 1000).toFixed(2) + "s";
		}
		return ms.toFixed(0) + "ms";
	};

	// 格式化Token输出速度
	const formatSpeed = (speed: number) => {
		return speed.toFixed(1) + " /s";
	};

	// 根据resource_analysis接口的实际返回数据结构更新资源指标
	resourceMetrics.value = [
		{
			name: "Token消耗总量",
			value: formatNumber(data.tokenConsumptionTotal || 0),
			change: formatRatio(data.tokenConsumptionTotalRatio || 0),
			period: "环比",
			changeType: getChangeType(data.tokenConsumptionTotalRatioChange || "2"),
		},
		{
			name: "输入Token消耗",
			value: formatNumber(data.inputTokenConsumption || 0),
			change: formatRatio(data.inputTokenConsumptionRatio || 0),
			period: "环比",
			changeType: getChangeType(data.inputTokenConsumptionRatioChange || "2"),
		},
		{
			name: "输出Token消耗",
			value: formatNumber(data.outputTokenConsumption || 0),
			change: formatRatio(data.outputTokenConsumptionRatio || 0),
			period: "环比",
			changeType: getChangeType(data.outputTokenConsumptionRatioChange || "2"),
		},
		{
			name: "Token输出速度",
			value: formatSpeed(data.tokenOutputSpeed || 0),
			change: formatRatio(data.tokenOutputSpeedRatio || 0),
			period: "环比",
			changeType: getChangeType(data.tokenOutputSpeedRatioChange || "2"),
		},
		{
			name: "平均响应耗时",
			value: formatResponseTime(data.averageResponseTime || 0),
			change: formatRatio(data.averageResponseTimeRatio || 0),
			period: "环比",
			changeType: getChangeType(data.averageResponseTimeRatioChange || "2"),
		},
	];
}
</script>

<style lang="less" scoped>
.usage-statistics {
	padding: 20px;
	background-color: #f5f5f5;
	min-height: 100vh;

	.section-container {
		margin-bottom: 32px;
	}

	.section-title {
		font-size: 16px;
		font-weight: 600;
		color: #333;
		margin-bottom: 16px;
	}

	.metrics-grid {
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		gap: 16px;

		@media (max-width: 1200px) {
			grid-template-columns: repeat(3, 1fr);
		}

		@media (max-width: 768px) {
			grid-template-columns: repeat(2, 1fr);
		}

		@media (max-width: 480px) {
			grid-template-columns: 1fr;
		}
	}

	.metric-card {
		background: white;
		border-radius: 8px;
		padding: 20px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;

		&:hover {
			box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
			transform: translateY(-2px);
		}
	}

	.metric-header {
		margin-bottom: 12px;
	}

	.metric-name {
		font-size: 12px;
		color: #666;
		font-weight: 500;
	}

	.metric-value {
		font-size: 24px;
		font-weight: 700;
		color: #333;
		margin-bottom: 8px;
	}

	.metric-change {
		display: flex;
		align-items: center;
		gap: 4px;
		font-size: 12px;

		&.increase {
			color: #52c41a;
		}

		&.decrease {
			color: #ff4d4f;
		}
	}

	.change-icon {
		font-weight: bold;
	}

	.change-period {
		color: #999;
	}
}
</style>
