import type {Router} from 'vue-router'
import {getToken, getTarget} from '@/utils/microFrontEnd'
import {get_network_info} from 'eucp-baselib';
import {AdapterFactory} from 'eucp-adapter';
import request from '@/utils/request/reportLog'
import {auth} from "@/utils/auth";

export function setupPageGuard(router: Router) {
	pathValidation(router)
	createPermissionGuard(router)
}

function pathValidation(router: Router) {
	router.beforeEach(async (to, from, next) => {
		to.meta.from = from.path;
		// 临时完全绕过权限检查
		// next()
		// return;

		if (to.path === '/unauthorized') {
			next()
		} else {
			// 临时绕过权限检查，允许访问测试页面
			if (to.path === '/ai-chat-demo' || to.path === '/test-chat' || to.path === '/simple-test') {
				next()
				return;
			}

			if (!getToken()) {
				next('/unauthorized')
			}

			let target: any = getTarget()
			console.log(target)
			if (target?.target === 'agent' && to.path !== '/tankChat') {
				next({
					path: '/tankChat',
					query: {
						...target
					}
				})
				return;
			}

			next()
		}
	})
}

function createPermissionGuard(router: Router) {
	// 添加页面进入时间变量
	let pageEnterTime = Date.now();
	router.beforeEach(async (to, from, next) => {
		// if (to.path !== '/login') {

		// 获取当前的adapter
		const adapter = AdapterFactory.getCurrentAdapter();
		// 计算页面停留时间(毫秒)
		const stayTime = Date.now() - pageEnterTime;
		try {
			// 异步获取网络信息
			const networkInfo = await get_network_info();
			// 上报日志
			adapter.reportLog?.(request, {
				user_id: auth.getUserInfo()?.id,
				// @ts-ignore
				tenant_id: auth.getUserInfo()?.tenantId,
				timestamp: new Date().getTime().toString(),
				page_url: to.path,
				ip: JSON.parse(networkInfo).ip,
				app_name: 'PC-MX',
				device_id: auth.getBrowserFingerprint(),
				// @ts-ignore
				user_agent: navigator.userAgent,
				page_title: document.title,
				stay_time: stayTime,
			});
		} catch (error) {
			console.error('获取网络信息失败:', error);
		}

		// 更新页面进入时间
		pageEnterTime = Date.now();
		next()
		// }
	})
}
