<template>
  <div class="h-full flex flex-col">
    <!-- 搜索区域 -->
    <div class="flex gap-4 mb-[20px] items-center search">
      <n-input
        v-model:value="searchForm.name"
        placeholder="搜索"
        clearable
        @input="handleSearch"
        :style="{
          width: '248px',
        }"
      >
        <template #suffix>
          <div class="search-icon-wrapper">
            <n-icon :size="16" style="color: #8f8f98">
              <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0 0 11.6 0l43.6-43.5a8.2 8.2 0 0 0 0-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                  fill="#8F8F98"
                />
              </svg>
            </n-icon>
          </div>
        </template>
      </n-input>
    </div>

    <!-- 列表区域 -->
    <div class="flex-1">
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        striped
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from "vue";
import { useRouter } from "vue-router";
import { NDataTable, NButton, NInput, useMessage, NIcon } from "naive-ui";
import { getPromptList, createPrompt } from "@/api/promptManagement";

const router = useRouter();
const message = useMessage();

// 搜索表单
const searchForm = reactive({
  name: "",
});

// 状态
const loading = ref(false);
const tableData = ref([]);

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  onChange: (page) => {
    pagination.page = page;
    loadData();
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    loadData();
  },
});

// 表格列定义
const columns = [
  {
    title: "提示词名称",
    key: "name",
    width: 200,
  },
  {
    title: "提示词ID",
    key: "id",
    width: 200,
  },
  {
    title: "创建人",
    key: "createdName",
    width: 120,
  },
  {
    title: "版本",
    key: "version",
    width: 100,
    render: (row) => {
      return row.version ? "v" + row.version : "—";
    },
  },
  {
    title: "更新时间",
    key: "updatedAt",
    width: 180,
  },
  {
    title: "创建时间",
    key: "createdAt",
    width: 180,
  },
  {
    title: "操作",
    key: "actions",
    width: 200,
    render: (row) => [
      h(
        NButton,
        {
          size: "small",
          type: "primary",
          text: true,
          style: "color: #125EFF",
          onClick: () => handleView(row),
        },
        "查看"
      ),
      h(
        NButton,
        {
          size: "small",
          type: "primary",
          text: true,
          style: "margin-left: 8px; color: #125EFF",
          onClick: () => handleCopy(row),
        },
        "复制到我的提示词"
      ),
    ],
  },
];

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      page_size: pagination.pageSize,
      page_num: pagination.page,
      name: searchForm.name,
      permission: "all",
    };
    const result = await getPromptList(params);
    tableData.value = result.data?.items;
    pagination.itemCount = result.data?.total;
  } catch (error) {
    message.error("加载数据失败");
    console.error("加载数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.page = 1;
  loadData();
};

// 查看
const handleView = (row) => {
  router.push({
    name: "promptEdit",
    query: { id: row.id, mode: "view" },
  });
};

// 复制到我的提示词
const handleCopy = async (row) => {
  try {
    await createPrompt({
      name: row.name,
      content: row.content || "",
    });
    message.success("复制成功");
  } catch (error) {
    message.error("复制失败");
  }
};

onMounted(() => {
  loadData();
});
</script>

<style lang="less" scoped>
.n-data-table {
  flex: 1;
}
/* 输入框和下拉框样式 */

.search {
  /deep/ .n-input {
    background: #ffffff !important;
    border-radius: 8px !important;
    height: 40px !important;
  }

  /deep/.n-base-selection {
    background: #ffffff !important;
    border-radius: 8px !important;
    height: 40px !important;
  }
  /deep/ .n-base-selection .n-base-selection-label {
    height: 40px !important;
    line-height: 40px !important;
  }

  /deep/ .n-input .n-input__input-el {
    height: 40px;
    line-height: 40px;
  }
}

/* 搜索图标样式 */
.search-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-left: 1px solid #e0e0e0;
  border-radius: 0 6px 6px 0;
  margin-right: -11px;
  background: #f6f8fb;
}
</style>
