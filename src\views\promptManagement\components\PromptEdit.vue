<template>
  <div class="h-full flex flex-col p-6">
    <!-- 顶部标题栏 -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center gap-3">
        <img
          class="w-[36px] cursor-pointer"
          src="@/assets/workShopPage/rest.png"
          alt=""
          @click="goBack"
        />

        <!-- 提示词名称 -->
        <div v-if="!nameEdit" class="flex items-center gap-2">
          <div class="flex items-center">
            <p class="text-[20px]">
              {{ formData.name }}
            </p>
            <SvgIcon
              v-if="mode !== 'view'"
              class="cursor-pointer ml-[14px] text-[26px] text-[#999]"
              icon="line-md:edit"
              @click="nameEdit = true"
            />
          </div>
          <span class="text-sm text-gray-500 ml-2" v-if="formData.version"
            >版本: v{{ formData.version }}</span
          >
        </div>

        <!-- 编辑名称状态 -->
        <div v-else class="flex items-center gap-2">
          <n-input
            v-model:value="formData.name"
            style="width: 200px"
            @keyup.enter="handleConfirmNameEdit"
          />
          <SvgIcon
            class="cursor-pointer ml-[4px] text-[32px] text-[#999]"
            icon="line-md:confirm"
            @click="handleConfirmNameEdit"
          />
        </div>
      </div>

      <!-- 右侧按钮 -->
      <div class="flex gap-3">
        <n-button
          v-if="mode !== 'create' && mode !== 'view'"
          @click="showVersionDrawer = true"
          class="version-management-btn"
        >
          版本管理
        </n-button>
        <n-button
          v-if="mode !== 'view'"
          type="primary"
          @click="showPublishModal = true"
          class="publish-btn"
        >
          发布
        </n-button>
      </div>
    </div>

    <!-- 主要内容区域 - 三栏布局 -->
    <div class="flex-1 flex gap-6 main-content">
      <!-- 左侧 - 系统提示词 -->
      <div class="w-1/3 flex flex-col system-prompt-section relative">
        <!-- 系统提示词标题 -->
        <div class="section-title">
          <div class="title-bar"></div>
          <span class="title-text">系统提示词</span>
        </div>

        <n-form-item
          label="系统提示词"
          required
          class="prompt custom-form-item"
        >
          <template #label>
            <div class="">系统提示词</div>
            <div class="absolute right-0 bottom-1" v-if="mode !== 'view'">
              <n-button
                :loading="optimizeLoading"
                :disabled="
                  !formData.systemPrompt?.length || !formData.modelId?.length
                "
                color="#125EFF"
                text
                @click="handleOptimize"
              >
                一键优化
              </n-button>
            </div>
          </template>
          <n-input
            v-model:value="formData.systemPrompt"
            type="textarea"
            placeholder="请输入系统提示词，使用 {{变量名}} 定义变量"
            :autosize="{ minRows: 30, maxRows: 50 }"
            :readonly="mode === 'view'"
            @input="handleSystemPromptChange"
            class="custom-input"
          />
        </n-form-item>
      </div>
      <div class="w-[1px] bg-[#E0E0E0] mx-[20px]" />
      <!-- 中间 - 模型配置和变量 -->
      <div class="w-1/3 flex-col gap-4 config-section">
        <!-- 模型配置标题 -->
        <div class="section-title">
          <div class="title-bar"></div>
          <span class="title-text">模型配置</span>
        </div>

        <!-- 模型配置 -->
        <n-form-item
          label=""
          class="custom-form-item"
          style="grid-template-rows: auto; margin-bottom: 30px"
        >
          <div class="flex items-center gap-2 w-full">
            <n-select
              v-model:value="formData.modelId"
              :options="modelOptions"
              value-field="id"
              label-field="name"
              placeholder="请选择模型"
              class="flex-1 custom-select"
            />
            <img
              class="cursor-pointer"
              src="@/assets/setting1.png"
              alt="配置"
              style="width: 20px; height: 20px"
              @click="openModelParams"
            />
          </div>
        </n-form-item>

        <!-- 提示词变量标题 -->
        <div class="section-title">
          <div class="title-bar"></div>
          <span class="title-text">提示词变量</span>
        </div>

        <!-- 提示词变量 -->
        <n-form-item
          label=""
          class="custom-form-item"
          style="grid-template-rows: auto"
        >
          <div class="w-full space-y-3">
            <div
              v-if="promptVariables.length === 0"
              class="text-gray-400 text-sm p-3 bg-gray-50 rounded border-dashed border-2"
            >
              {{ placeholderText }}
            </div>
            <div
              v-for="variable in promptVariables"
              :key="variable"
              class="flex flex-col gap-1"
            >
              <n-input
                v-model:value="formData.variables[variable]"
                :placeholder="`请输入${variable}的值`"
                class="custom-input"
              >
                <template #prefix>
                  <div class="variable-prefix">
                    {{ variable }}
                  </div>
                </template>
              </n-input>
            </div>
          </div>
        </n-form-item>
      </div>

      <!-- 右侧 - 预览区域 -->
      <div class="w-1/3 preview-section">
        <div class="h-full rounded-lg">
          <ChatPreview
            :user-prompt="formData.userPrompt"
            :system-prompt="formData.systemPrompt"
            :prompt-variables="formData.variables"
            :model-id="formData.modelId"
            :model-temp="modelParams.temperature"
            :max-length="modelParams.maxLength"
            :prompt-template="processedSystemPrompt"
            agentId="preview_prompt"
          />
        </div>
      </div>
    </div>

    <!-- 版本管理抽屉 -->
    <n-drawer v-model:show="showVersionDrawer" width="450px">
      <n-drawer-content title="版本管理" class="version-drawer">
        <div class="h-full flex flex-col" style="background: #fcfcfc">
          <!-- 时间轴容器 -->
          <div
            ref="timelineContainerRef"
            class="flex-1 overflow-y-auto pr-2"
            style="height: 100vh"
          >
            <div class="relative">
              <!-- 时间轴线  -->
              <div
                class="absolute top-0 bottom-0"
                style="left: 13px; width: 2px; border-left: 2px dashed #e5e5e5"
              ></div>

              <!-- 版本节点 -->
              <div
                v-for="version in sortedVersionList"
                :key="version.id"
                :data-version-id="version.id"
                class="relative w-[365px]"
              >
                <!-- 时间轴节点  -->
                <div
                  class="absolute"
                  style="
                    left: 0px;
                    top: 14px;
                    width: 30px;
                    height: 30px;
                    background: #fff;
                  "
                >
                  <div
                    class="rounded-full"
                    :class="{
                      'timeline-node current': version.isCurrent,
                      'timeline-node': !version.isCurrent,
                    }"
                    style="margin: 0 auto; margin-top: 7px"
                  ></div>
                </div>

                <!-- 版本信息卡片 -->
                <div
                  class="ml-8 p-4 rounded-lg cursor-pointer transition-all duration-200 version-card"
                  :class="{
                    current: version.isCurrent && !selectedVersion,
                    selected:
                      selectedVersion?.id === version.id && !version.isCurrent,
                  }"
                  @click="selectVersion(version)"
                >
                  <div class="flex items-center justify-between mb-2">
                    <span
                      class="version-title"
                      :class="{ 'current-version-text': version.isCurrent }"
                    >
                      {{
                        version.isDraft
                          ? "当前版本: 草稿"
                          : `版本v${version.version}`
                      }}
                    </span>
                  </div>
                  <div
                    v-if="version.description && !version.isDraft"
                    class="version-description mb-2"
                  >
                    {{ version.description }}
                  </div>
                  <div class="version-time">
                    更新时间: {{ version.updateTime }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <template #footer>
          <div class="w-full">
            <n-button
              v-if="selectedVersion && !selectedVersion.isCurrent"
              type="primary"
              block
              size="large"
              @click="restoreVersion"
              :loading="restoreLoading"
              class="w-full restore-button"
            >
              还原此版本
            </n-button>
          </div>
        </template>
      </n-drawer-content>
    </n-drawer>

    <!-- 发布弹窗 -->
    <n-modal v-model:show="showPublishModal">
      <n-card
        style="width: 644px; border-radius: 8px !important"
        title="发布提示词"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        class="publish-modal"
      >
        <n-form ref="publishFormRef" :model="publishForm">
          <n-form-item label="发布说明" class="custom-form-item">
            <n-input
              v-model:value="publishForm.description"
              type="textarea"
              placeholder="请输入发布说明..."
              :autosize="{ minRows: 6, maxRows: 9 }"
              maxlength="200"
              show-count
              class="custom-input"
            />
          </n-form-item>
          <div class="specialItem">
            <n-form-item
              label="发布到平台"
              class="custom-form-item"
              label-placement="left"
              style="margin-bottom: 27px"
            >
              <n-switch v-model:value="publishForm.publishToPlatform" />
            </n-form-item>
          </div>
        </n-form>

        <template #footer>
          <div class="flex justify-end gap-3">
            <n-button
              @click="showPublishModal = false"
              secondary
              class="cancel-btn"
              >取消</n-button
            >
            <n-button
              type="primary"
              @click="handlePublish"
              :loading="publishLoading"
              class="publish-submit-btn"
            >
              发布
            </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>

    <!-- 模型参数弹窗 -->
    <n-modal v-model:show="showModelParams">
      <n-card
        style="width: 644px; border-radius: 8px !important"
        title="模型参数"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
        class="model-params-modal"
      >
        <n-form :model="modelParams">
          <n-form-item
            label="模型风格"
            class="custom-form-item"
            style="margin-bottom: 10px"
          >
            <n-radio-group
              v-model:value="modelParams.style"
              @update:value="updateModelParams"
            >
              <n-radio
                v-for="style in modelStyleOptions"
                :key="style.dictKey"
                :value="style.dictKey"
              >
                {{ style.dictValue }}
              </n-radio>
            </n-radio-group>
          </n-form-item>
          <n-form-item
            label="模型温度"
            class="custom-form-item"
            style="margin-bottom: 10px"
          >
            <n-input-number
              v-model:value="modelParams.temperature"
              :min="0"
              :max="2"
              :step="0.1"
              :readonly="true"
              style="width: 100%"
            />
          </n-form-item>
          <n-form-item
            label="Top P"
            class="custom-form-item"
            style="margin-bottom: 10px"
          >
            <n-input-number
              v-model:value="modelParams.topP"
              :min="0"
              :max="1"
              :step="0.1"
              :readonly="true"
              style="width: 100%"
            />
          </n-form-item>
          <n-form-item
            label="最大输出长度"
            class="custom-form-item"
            style="margin-bottom: 10px"
          >
            <n-input-number
              v-model:value="modelParams.maxLength"
              :min="1"
              :max="100000"
              :readonly="true"
              style="width: 100%"
            />
          </n-form-item>
        </n-form>

        <template #footer>
          <div class="flex justify-end gap-3">
            <n-button @click="cancelModelParams" secondary class="cancel-btn"
              >取消</n-button
            >
            <n-button
              type="primary"
              @click="saveModelParams"
              class="publish-submit-btn"
            >
              保存
            </n-button>
          </div>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { SvgIcon } from "@/components/common";
import {
  NButton,
  NIcon,
  NInput,
  NFormItem,
  NSelect,
  NInputNumber,
  NRadioGroup,
  NRadio,
  NDrawer,
  NDrawerContent,
  NModal,
  NCard,
  NForm,
  NSwitch,
  useMessage,
} from "naive-ui";
// import {
//   ArrowBack,
//   Edit,
//   Checkmark,
//   Settings
// } from '@vicons/ionicons5'
import ChatPreview from "./PromptChatPreview.vue";
import {
  getPromptDetail,
  releasePrompt,
  saveDraft,
  getVersionList,
} from "@/api/promptManagement";
import { agentParam, museModels, optimize } from "@/api/workShop";

const route = useRoute();
const router = useRouter();
const message = useMessage();

// 基础状态
const saveLoading = ref(false);
const nameEdit = ref(false);
const optimizeLoading = ref(false);
const publishLoading = ref(false);
const restoreLoading = ref(false);

// 弹窗状态
const showVersionDrawer = ref(false);
const showPublishModal = ref(false);
const showModelParams = ref(false);

// 获取路由参数
const promptId = computed(() => route.query.id);
const mode = computed(() => route.query.mode || "edit");

// 表单数据
const formData = reactive({
  name: "",
  version: "",
  systemPrompt: "",
  userPrompt: "",
  modelId: "",
  variables: {},
  status: "0",
  updatedAt: "",
});

// 用于检测系统提示词是否有变化
const originalSystemPrompt = ref("");

// 模型选项（从API获取）
const modelOptions = ref([]);

// 模型风格选项（从API获取）
const modelStyleOptions = ref([]);

// 默认模型参数（从API获取）
const defaultModelParams = reactive({
  topP: 0.8,
  maxLength: 4000,
  modelStyle: [],
});

const placeholderText = "请输入系统提示词，使用 {{变量名}} 定义变量";

// 提示词变量解析
const promptVariables = computed(() => {
  if (!formData.systemPrompt) return [];
  const matches = formData.systemPrompt.match(/\{\{([^}]+)\}\}/g);
  if (!matches) return [];
  return [...new Set(matches.map((match) => match.slice(2, -2)))];
});

// 处理后的系统提示词（替换变量）
const processedSystemPrompt = computed(() => {
  let processed = formData.systemPrompt;
  Object.entries(formData.variables).forEach(([key, value]) => {
    const regex = new RegExp(`\\{\\{${key}\\}\\}`, "g");
    processed = processed.replace(regex, value || `{{${key}}}`);
  });
  return processed;
});

// 监听变量变化，初始化变量值
watch(
  promptVariables,
  (newVars) => {
    const newVariables = {};
    newVars.forEach((variable) => {
      newVariables[variable] = formData.variables[variable] || "";
    });
    formData.variables = newVariables;
  },
  { immediate: true }
);

// 监听系统提示词变化，更新草稿节点的content
watch(
  () => formData.systemPrompt,
  (newValue) => {
    const draftVersion = versionList.value.find((v) => v.isDraft);
    if (draftVersion) {
      draftVersion.content = newValue;
    }
  }
);

// 发布表单
const publishForm = reactive({
  description: "",
  publishToPlatform: false,
});

// 模型参数
const modelParams = reactive({
  style: "1", // 默认为平衡（1）
  temperature: 0.7,
  topP: 0.8,
  maxLength: 4000,
});

// 临时模型参数（用于弹窗取消时恢复）
const tempModelParams = reactive({
  style: "1",
  temperature: 0.7,
  topP: 0.8,
  maxLength: 4000,
});

// 更新模型参数
const updateModelParams = (style) => {
  const selectedStyle = modelStyleOptions.value.find(
    (s) => s.dictKey === style
  );
  if (selectedStyle) {
    modelParams.temperature = Number(style);
    modelParams.topP = defaultModelParams.topP;
    modelParams.maxLength = defaultModelParams.maxLength;
  }
};

// 版本列表（从API获取）
const versionList = ref([]);

const selectedVersion = ref(null);
const timelineContainerRef = ref(null);

// 计算属性：按时间顺序排序的版本列表（最新的在下面）
const sortedVersionList = computed(() => {
  return [...versionList.value].sort(
    (a, b) => new Date(a.updateTime) - new Date(b.updateTime)
  );
});

// 当前版本
const currentVersion = computed(() => {
  return versionList.value.find((v) => v.isCurrent);
});

// 滚动到当前版本
const scrollToCurrentVersion = async () => {
  await nextTick();
  if (!timelineContainerRef.value || !currentVersion.value) return;

  const currentVersionElement = timelineContainerRef.value.querySelector(
    `[data-version-id="${currentVersion.value.id}"]`
  );
  if (currentVersionElement) {
    currentVersionElement.scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  }
};

// 监听抽屉显示状态，自动滚动到当前版本
watch(showVersionDrawer, (newVal) => {
  if (newVal) {
    scrollToCurrentVersion();
  }
});

// 处理系统提示词变化
const handleSystemPromptChange = () => {
  // 这里可以添加实时解析变量的逻辑
};

// 确认名称编辑
const handleConfirmNameEdit = async () => {
  if (!formData.name.trim()) {
    message.error("提示词名称不能为空");
    return;
  }

  if (!promptId.value) {
    message.error("缺少提示词ID");
    return;
  }

  try {
    await saveDraft(promptId.value, {
      name: formData.name,
      status: 0,
    });

    nameEdit.value = false;
    message.success("名称保存成功");
  } catch (error) {
    message.error("名称保存失败");
    console.error("名称保存失败:", error);
  }
};

// 一键优化
const handleOptimize = async () => {
  if (!formData.systemPrompt) return;

  optimizeLoading.value = true;
  try {
    const res = await optimize({
      modelTemp: modelParams.temperature,
      promptTemplate: formData.systemPrompt,
      modelId: formData.modelId,
      maxLength: modelParams.maxLength,
    });

    formData.systemPrompt = res.data;
    message.success("提示词优化完成");
  } catch (error) {
    message.error("优化失败");
    console.error("优化失败:", error);
  } finally {
    optimizeLoading.value = false;
  }
};

// 选择版本
const selectVersion = (version) => {
  if (version.isDraft) {
    selectedVersion.value = null;
    return;
  }

  // 如果点击的是当前版本
  if (version.isCurrent) {
    // 如果当前没有选中其他版本，则不做任何操作（保持当前版本的选中状态）
    // 如果选中了其他版本，则取消选中，让当前版本恢复默认选中状态
    selectedVersion.value = null;
    return;
  }

  // 如果点击的是当前选中的其他版本，取消选中
  if (selectedVersion.value?.id === version.id) {
    selectedVersion.value = null;
    return;
  }

  // 选中新版本
  selectedVersion.value = version;
}; // 还原版本
const restoreVersion = async () => {
  if (!selectedVersion.value) return;

  restoreLoading.value = true;
  try {
    // 使用选中版本的content替换当前的systemPrompt
    formData.systemPrompt = selectedVersion.value.content || "";

    // 更新当前版本标识
    versionList.value.forEach((v) => {
      v.isCurrent = v.id === selectedVersion.value.id;
    });

    formData.version = selectedVersion.value.version;

    message.success("版本还原成功");
    showVersionDrawer.value = false;
    selectedVersion.value = null;
  } catch (error) {
    message.error("版本还原失败");
  } finally {
    restoreLoading.value = false;
  }
};

// 发布
const handlePublish = async () => {
  // 简单验证
  if (!formData.name || !formData.systemPrompt) {
    message.error("请完善必填信息");
    return;
  }

  if (!promptId.value) {
    message.error("缺少提示词ID");
    return;
  }

  publishLoading.value = true;
  try {
    await releasePrompt({
      id: promptId.value,
      name: formData.name,
      content: formData.systemPrompt,
      isPublishingPlatform: publishForm.publishToPlatform,
      versionDescription: publishForm.description,
    });

    message.success("发布成功");
    showPublishModal.value = false;

    // 发布成功后返回列表页
    setTimeout(() => {
      router.push("/promptManagement");
    }, 1000);
  } catch (error) {
    message.error("发布失败");
    console.error("发布失败:", error);
  } finally {
    publishLoading.value = false;
  }
};

// 保存模型参数
const saveModelParams = () => {
  // 根据选择的风格更新参数
  updateModelParams(modelParams.style);
  message.success("模型参数保存成功");
  showModelParams.value = false;
};

// 取消模型参数修改
const cancelModelParams = () => {
  // 恢复原参数值
  modelParams.style = tempModelParams.style;
  modelParams.temperature = tempModelParams.temperature;
  modelParams.topP = tempModelParams.topP;
  modelParams.maxLength = tempModelParams.maxLength;
  showModelParams.value = false;
};

// 打开模型参数弹窗
const openModelParams = () => {
  // 保存当前参数值
  tempModelParams.style = modelParams.style;
  tempModelParams.temperature = modelParams.temperature;
  tempModelParams.topP = modelParams.topP;
  tempModelParams.maxLength = modelParams.maxLength;
  showModelParams.value = true;
};

// 返回
const goBack = async () => {
  // 检查系统提示词是否有变化
  if (formData.systemPrompt !== originalSystemPrompt.value) {
    try {
      // 调用保存草稿接口
      await saveDraft(promptId.value, {
        content: formData.systemPrompt,
        status: 0,
      });
      message.success("草稿已保存");
    } catch (error) {
      message.error("保存草稿失败");
      console.error("保存草稿失败:", error);
      return; // 如果保存失败，不进行跳转
    }
  }

  router.push("/promptManagement");
};

// 保存
const handleSave = async () => {
  // 表单验证
  if (!formData.name || !formData.systemPrompt) {
    message.error("请完善必填信息");
    return;
  }

  saveLoading.value = true;
  try {
    // 模拟保存API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));
    message.success("保存成功");
  } catch (error) {
    message.error("保存失败");
  } finally {
    saveLoading.value = false;
  }
};

// 加载版本列表数据
const loadVersionList = async () => {
  try {
    const result = await getVersionList(promptId.value);

    if (result.data) {
      versionList.value = result.data.map((item, index, array) => ({
        id: item.id,
        version: item.version,
        description: item.versionDescription || "",
        updateTime: item.updatedAt,
        isCurrent: index === array.length - 1, // 永远取列表中的最后一个作为当前版本
        content: item.content, // 添加content字段用于还原
      }));

      // 如果status为0（草稿状态），在数据末尾添加一条当前版本数据
      if (formData.status === "0") {
        // 将原有的最后一个版本的isCurrent设为false
        if (versionList.value.length > 0) {
          versionList.value[versionList.value.length - 1].isCurrent = false;
        }

        versionList.value.push({
          id: "current_draft",
          version: "草稿",
          description: "",
          updateTime: formData.updatedAt || new Date().toLocaleString(),
          isCurrent: true,
          isDraft: true, // 标识为草稿节点
          content: formData.systemPrompt,
        });
      }
    }
  } catch (error) {
    console.error("加载版本列表失败:", error);
    message.error("加载版本列表失败");
  }
};

// 加载数据
const loadData = async () => {
  // 首先加载模型和参数数据
  try {
    // 获取模型列表
    const modelsResult = await museModels();
    console.log(modelsResult, "modelsResult");
    modelOptions.value = modelsResult.data;

    // 获取模型参数
    const paramsResult = await agentParam();
    modelStyleOptions.value = paramsResult.data.model_style;
    defaultModelParams.topP = Number(paramsResult.data.model_topp);
    defaultModelParams.maxLength = Number(paramsResult.data.model_max_length);

    // 设置默认模型参数 - 选择第一个模型风格
    if (modelStyleOptions.value.length > 0) {
      const defaultStyle = modelStyleOptions.value[0];

      modelParams.style = defaultStyle.dictKey;
      modelParams.temperature = Number(defaultStyle.dictKey);
      modelParams.topP = defaultModelParams.topP;
      modelParams.maxLength = defaultModelParams.maxLength;

      // 同步更新临时参数
      tempModelParams.style = defaultStyle.dictKey;
      tempModelParams.temperature = Number(defaultStyle.dictKey);
      tempModelParams.topP = defaultModelParams.topP;
      tempModelParams.maxLength = defaultModelParams.maxLength;
    }
  } catch (error) {
    console.error("加载模型数据失败:", error);
    message.error("加载模型数据失败");
  }

  // 默认模型为第一个选项
  if (modelOptions.value.length > 0) {
    formData.modelId = modelOptions.value[0].id;
  }

  if (!promptId.value) {
    message.error("缺少提示词ID");
    return;
  }

  try {
    // 调用API获取提示词详情
    const result = await getPromptDetail(promptId.value);

    if (result.data) {
      // 填充表单数据
      formData.name = result.data.name || "";
      formData.version = result.data.version;
      formData.systemPrompt = result.data.content || ""; // 将content赋值给systemPrompt
      formData.status = result.data.status || "0";
      formData.updatedAt = result.data.updatedAt || "";

      // 保存原始系统提示词用于检测变化
      originalSystemPrompt.value = formData.systemPrompt;
    }
  } catch (error) {
    message.error("加载数据失败");
    console.error("加载提示词详情失败:", error);
  }

  // 加载版本列表
  await loadVersionList();
};

onMounted(() => {
  loadData();
});
</script>

<style lang="less" scoped>
.prompt-edit-container {
  height: 100vh;
  overflow: hidden;
}

/* 确保三栏布局等高 */
.main-content {
  height: calc(100vh - 120px);
}

/* 左栏系统提示词区域 */
.system-prompt-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.system-prompt-section :deep(.n-form-item-blank) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.system-prompt-section :deep(.n-input__textarea-el) {
  flex: 1 !important;
  min-height: 500px !important;
}

/* 中间栏配置区域 */
.config-section {
  height: 100%;
  overflow-y: auto;
}

/* 右栏预览区域 */
.preview-section {
  height: 100%;
}

/* 版本管理抽屉样式 */

.version-drawer :deep(.n-drawer-footer) {
  border-top: none !important;
  background: #fcfcfc;
}

.version-item {
  transition: all 0.2s ease;
}

.version-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 时间轴样式 */
.timeline-container {
  position: relative;
}

.timeline-line {
  position: absolute;
  left: 13px;
  top: 0;
  bottom: 0;
  width: 2px;
  border-left: 2px dashed #e5e5e5;
  background: transparent;
}

.timeline-node {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #d2d2d2;
  border: 2.33px solid #eeeeee;
  z-index: 2;
  transition: all 0.2s ease;
}

.timeline-node.current {
  background: #125eff;
  border: 2.33px solid #dae6ff;
}

.version-card {
  margin-left: 32px;
  transition: all 0.2s ease;
  background: white;
}

.version-card.current {
  background: #f0f4fe !important;
  border-color: #125eff !important;
}

.version-card.selected {
  background: #f0f0f0 !important;
  border-color: #6b7280 !important;
}

/* 版本标题样式 */
.version-title {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 16px;
  color: #303133;
}

.version-title.current-version-text {
  color: #125eff;
}

/* 版本描述样式 */
.version-description {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #4e5056;
  text-align: justify;
}

/* 更新时间样式 */
.version-time {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #00000066;
}

/* 还原按钮样式 */
.restore-button {
  height: 46px !important;
  background: #125eff !important;
  border-radius: 10px !important;
  border: none !important;
}

.restore-button:hover {
  background: #0d4ed4 !important;
}

.prompt :deep(.n-form-item-label) {
  position: relative;
}

/* 发布弹窗样式 */
.publish-modal :deep(.n-card__header) {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 17px;
  color: #1f1f1f;
}

/* 模型参数弹窗样式 */
.model-params-modal :deep(.n-card__header) {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 17px;
  color: #1f1f1f;
}

.custom-form-item :deep(.n-form-item-label) {
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 15px;
  color: #323233;
  letter-spacing: 0;
}

.custom-form-item {
  /deep/ .n-input {
    background: #ffffff !important;
    border-radius: 8px !important;
    margin-bottom: 8px;
  }

  /deep/ .n-input__textarea-el {
    background: #ffffff !important;
    border: none !important;
  }
}

.cancel-btn {
  width: 80px;
  height: 36px;
  background: #ffffff !important;
  border: 1px solid #ebebeb !important;
  border-radius: 10px !important;
  color: #666 !important;
}

.cancel-btn:hover {
  background: #f5f5f5 !important;
}

.publish-submit-btn {
  width: 80px;
  height: 36px;
  background: #125eff !important;
  border-radius: 10px !important;
  border: none !important;
}

.publish-submit-btn:hover {
  background: #0f52e6 !important;
}
/deep/ .n-card-header {
  border-bottom: 1px solid #ebebeb !important;
  padding: 18px 0 16px 26px !important;
  margin-bottom: 24px;
}
/deep/ .n-card__content {
  padding-left: 26px;
  padding-bottom: 3px;
}
/deep/ .specialItem .n-form-item .n-form-item-blank {
  justify-content: end !important;
}
/deep/ .n-form-item-feedback-wrapper {
  display: none !important;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    height: auto;
  }

  .system-prompt-section,
  .config-section,
  .preview-section {
    width: 100%;
    height: auto;
    min-height: 400px;
  }
}

/* 新增样式 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 8px;
}

.title-bar {
  width: 6px;
  height: 18px;
  background-image: linear-gradient(180deg, #82fba5 0%, #058dfc 100%);
  border-radius: 3px;
}

.title-text {
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 18px;
  color: #2f3033;
  letter-spacing: 0;
}

.custom-input {
  /deep/ .n-input {
    background: #ffffff !important;
    border: 1px solid #e6e6e6 !important;
    border-radius: 8px !important;
  }

  /deep/ .n-input__textarea-el {
    background: #ffffff !important;
    border: none !important;
    border-radius: 8px !important;
  }
}

.custom-select {
  /deep/ .n-base-selection {
    background: #ffffff !important;
    border-radius: 8px !important;
  }
}

.variable-prefix {
  width: 101px;
  height: 24px;
  background: #125eff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 15px;
  color: #ffffff;
  letter-spacing: 0;
  margin-right: 8px;
  flex-shrink: 0;
}

/* 顶部按钮样式 */
.version-management-btn {
  width: 108px !important;
  height: 40px !important;
  border: 1px solid #125eff !important;
  border-radius: 10px !important;
  background: #ffffff !important;
  color: #125eff !important;
}

.version-management-btn:hover {
  background: #f0f4fe !important;
}

.publish-btn {
  width: 80px !important;
  height: 40px !important;
  background: #125eff !important;
  border-radius: 10px !important;
  border: none !important;
  color: #ffffff !important;
}

.publish-btn:hover {
  background: #0f52e6 !important;
}
</style>
