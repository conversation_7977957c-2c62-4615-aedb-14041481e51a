<template>
  <div>
    <n-form-item label-placement="left" class="setHeight mt-[21px] mb-[13px]">
      <template #label>
        <div class="rowstit"><span class="rowicon"></span> 输入</div>
      </template>
    </n-form-item>
    <n-form-item label-placement="left" class="setHeight mt-[24px] mb-[12px]">
      <template #label>
        <div class="rowstit">变量聚合</div>
      </template>
      <div class="flex justify-end w-full">
        文本
        <img
          @click="addaggregationListfun"
          class="w-[16px] h-[16px] ml-[10px] cursor-pointer"
          src="@/assets/agentOrchestration/yituzhishi.png"
        />
      </div>
    </n-form-item>
    <div
      class="flex items-center mb-[12px] text-[#565756]"
      v-for="(item, index) in formData.config.aggregationlist"
      :key="index"
    >
      <div class="w-full mr-[6px] h-[38px] outputrow">
        <AggregationSelector
          v-model="item.value"
          :options="computedAggregationOptions"
          placeholder="请选择变量"
          @change="handleAggregationChange"
        />
      </div>
      <img
        @click="delaggregationListfun(index)"
        class="w-[16px] h-[16px] cursor-pointer"
        src="@/assets/agentOrchestration/delIcon2.png"
      />
    </div>
    <n-form-item label-placement="left" class="setHeight mt-[24px]">
      <template #label>
        <div class="rowstit"><span class="rowicon"></span>输出</div>
      </template>
    </n-form-item>

    <n-form-item label-placement="left" class="setHeight mt-[6px]">
      <div class="w-full flex text-[#C7C7C7]">
        <div class="w-[50%]">变量名称</div>
        <div>数据类型</div>
      </div>
    </n-form-item>

    <n-form-item label-placement="left" class="setHeight mt-[14px]">
      <div class="w-full flex text-[#565756] items-center">
        <div class="w-[50%]">聚合结果</div>
        <div>文本</div>
      </div>
    </n-form-item>
  </div>
</template>

<script lang="ts" setup>
import { NForm, NFormItem } from "naive-ui";
import AggregationSelector from "../AggregationSelector.vue";
import { deepClone } from "@/utils/is";
import { computed } from "vue";
interface FormData {
  config: {
    aggregationVariable?: string;
    inputKey?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

interface Props {
  formData: FormData;
  node: {
    type: Object;
    required: true;
  };
  aggregationOptions: Array<any>;
}

const props = withDefaults(defineProps<Props>(), {
  aggregationOptions: () => [],
});

var computedAggregationOptions = computed(() => {

  const aggregationList = props.formData.config.aggregationlist || [];
  var aggregationOptions = deepClone(props.aggregationOptions);
  // 如果没有聚合项，返回所有选项
  if (aggregationList.length === 0) {
    return aggregationOptions;
  }
  
  // 获取第一个聚合项的变量值
  const firstVariableValue = aggregationList[0]?.value;
  if (!firstVariableValue) {
    return aggregationOptions;
  }
  
  // 在aggregationOptions中查找第一个变量对应的类型
  let firstVariableType = null;
  for (const option of aggregationOptions) {
    if (option.variables && Array.isArray(option.variables)) {
      const foundVariable = option.variables.find(v => v.id === firstVariableValue || v.value === firstVariableValue);
      if (foundVariable && foundVariable.valueType) {
        firstVariableType = foundVariable.valueType;
        break;
      }
    }
  }
  
  // 如果没有找到类型，返回所有选项
  if (!firstVariableType) {
    return aggregationOptions;
  }
  
  // 过滤出相同类型的变量
  let aggregationArr = aggregationOptions.filter((item) => {
    // 检查变量类型是否匹配
    let hasMatchingType = false;
    if (item.variables && Array.isArray(item.variables)) {
      hasMatchingType = item.variables.some(variable => 
        variable.valueType === firstVariableType
      );
    }
    
    // 如果类型不匹配，直接过滤掉
    if (!hasMatchingType) {
      return false;
    }
    
    // 检查是否已存在
    if(hasCommonProperty(aggregationList, item.variables, 'value')){
      item.disabled = true;
    } else {
      item.disabled = false;
    }
    
    return true;
  });
  
  return aggregationArr;
});
const hasCommonProperty=(arr1, arr2, prop)=>{
  if(!arr1){
    return false
  }
 const values = new Set(arr1.map(item => item['value']));
  return arr2.some(item => values.has(item['id']));
}
const addaggregationListfun = () => {
  if (!props.formData.config.aggregationlist) {
    props.formData.config.aggregationlist = [];
  }
  props.formData.config.aggregationlist.push({ key: "", value: "" });
};
const delaggregationListfun = (index: number) => {
  props.formData.config.aggregationlist.splice(index, 1);
};
const emit = defineEmits<{
  "update:formData": [value: FormData];
  handleAggregationChange: [value: string, variable: any, group: any];
}>();

// 处理聚合变量变更
const handleAggregationChange = (value: string, variable: any, group: any) => {
  const newFormData = { ...props.formData };

  emit("update:formData", newFormData);
  // emit('handleAggregationChange', value, variable, group);
};
</script>

<style scoped>
@import "./nodeConfigStyle.less";
</style>