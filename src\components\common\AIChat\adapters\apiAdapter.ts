// API适配器 - 兼容不同的API接口
import type { 
  ChatAPIParams, 
  StreamResponse, 
  Message, 
  AIChatConfig 
} from '../types'
import { fetchChatAPI } from '@/api'
import { post } from '@/utils/request'

// AI聊天组件专用的API函数
export function fetchAIChatAPIProcess<T = any>(
  params: {
    signal?: AbortSignal
    question: string
    conversationId: string
    category: string
    agentId: string
    multiTurnFlag: number
    onDownloadProgress?: (progressEvent: any) => void
  }
) {
  const data = {
    question: params.question,
    conversationId: params.conversationId,
    category: params.category,
    agentId: params.agentId,
    multiTurnFlag: params.multiTurnFlag
  }

  return post<T>({
    url: '/emind/conversationContent/text_answers',
    data,
    signal: params.signal,
    onDownloadProgress: params.onDownloadProgress,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export class APIAdapter {
  private config: AIChatConfig

  constructor(config: AIChatConfig = {}) {
    this.config = config
  }

  // 统一的消息发送接口
  async sendMessage(
    params: ChatAPIParams,
    onStream?: (response: StreamResponse) => void
  ): Promise<StreamResponse> {
    // 根据配置选择API
    if (this.config.compatibility?.useOriginalAPI) {
      return this.callOriginalAPI(params, onStream)
    } else {
      return this.callNewAPI(params, onStream)
    }
  }

  // 调用原有的tankChat API
  private async callOriginalAPI(
    params: ChatAPIParams,
    onStream?: (response: StreamResponse) => void
  ): Promise<StreamResponse> {
    try {
      const apiParams = {
        question: params.question || params.content || params.prompt || '',
        conversationId: params.conversationId || this.config.compatibility?.conversationId || '',
        category: params.category || this.config.compatibility?.category || '',
        agentId: params.agentId || this.config.compatibility?.agentId || '',
        modelId: params.modelId || this.config.compatibility?.modelId || '',
        modelTemp: params.modelTemp || this.config.compatibility?.modelTemp || 0.7,
        maxLength: params.maxLength || this.config.compatibility?.maxLength || 2000,
        promptTemplate: params.promptTemplate || this.config.compatibility?.promptTemplate || '',
        multiTurnFlag: params.multiTurnFlag ?? this.config.compatibility?.multiTurnFlag ?? true,
        signal: params.signal,
        onDownloadProgress: ({ event }: any) => {
          if (params.onDownloadProgress) {
            params.onDownloadProgress({ event })
          }

          // 处理SSE流式响应
          if (onStream && event.target?.responseText) {
            try {
              const xhr = event.target
              const { responseText } = xhr

              // 按行分割响应文本，模仿原来的chat实现
              const lines = responseText.split('\n').filter((line: string) => line.trim() !== '')

              // 重置累积文本，避免重复累加（模仿原来的实现）
              let currentContent = ''

              // 处理每一行数据
              for (const line of lines) {
                // 移除 "data: " 前缀并解析JSON
                const trimmedLine = line.replace(/^data:\s*/, '').trim()

                if (trimmedLine === '[DONE]' || trimmedLine === '') {
                  // 流结束标记
                  onStream({
                    content: currentContent,
                    thinking: '',
                    finished: true,
                    text: currentContent,
                    answerList: [],
                    endstatus: 1,
                    conversationId: apiParams.conversationId
                  })
                  break
                }

                try {
                  const data = JSON.parse(trimmedLine)

                  // 解析新的SSE数据格式
                  if (data.choices && data.choices[0] && data.choices[0].message) {
                    const messageContent = data.choices[0].message.content || ''
                    const isFinished = data.choices[0].finish_reason === 'stop' || data.choices[0].finish_reason === 'STOP'

                    // 累积内容（模仿原来的实现方式）
                    currentContent += messageContent

                    const streamResponse: StreamResponse = {
                      content: currentContent,
                      thinking: '',
                      finished: isFinished,
                      // 兼容原有格式
                      text: currentContent,
                      answerList: [],
                      endstatus: isFinished ? 1 : 0,
                      error: undefined,
                      conversationId: data.id || apiParams.conversationId
                    }

                    onStream(streamResponse)
                  }
                } catch (parseError) {
                  console.warn('Failed to parse SSE line:', trimmedLine, parseError)
                }
              }
            } catch (error) {
              console.warn('Failed to process stream response:', error)
            }
          }
        }
      }

      const response = await fetchAIChatAPIProcess({
        signal: apiParams.signal,
        question: apiParams.question,
        conversationId: apiParams.conversationId,
        category: apiParams.category,
        agentId: apiParams.agentId,
        multiTurnFlag: apiParams.multiTurnFlag ? 1 : 0,  // 转换为数字
        onDownloadProgress: apiParams.onDownloadProgress
      })
      const responseData = response.data || response

      return {
        content: responseData.text || responseData.content || responseData.answer || '',
        thinking: responseData.thinking,
        finished: true,
        text: responseData.text,
        answerList: responseData.answerList,
        endstatus: responseData.endstatus,
        error: responseData.error,
        conversationId: responseData.conversationId
      }
    } catch (error) {
      console.error('Original API call failed:', error)
      throw error
    }
  }

  // 调用新的API
  private async callNewAPI(
    params: ChatAPIParams,
    onStream?: (response: StreamResponse) => void
  ): Promise<StreamResponse> {
    try {
      const response = await fetchChatAPI(
        params.content || params.prompt || params.question || '',
        {
          conversationId: params.conversationId,
          parentMessageId: params.parentMessageId
        },
        params.signal
      )

      const responseData = response.data || response
      const streamResponse: StreamResponse = {
        content: responseData.content || responseData.text || responseData.answer || '',
        thinking: responseData.thinking,
        finished: true,
        workflow: responseData.workflow
      }

      if (onStream) {
        onStream(streamResponse)
      }

      return streamResponse
    } catch (error) {
      console.error('New API call failed:', error)
      throw error
    }
  }

  // 消息格式转换
  convertToStandardMessage(data: any): Message {
    return {
      id: data.id || this.generateId(),
      content: data.content || data.text || '',
      role: data.role || (data.inversion ? 'user' : 'assistant'),
      timestamp: data.timestamp || (data.dateTime ? new Date(data.dateTime).getTime() : Date.now()),
      loading: data.loading,
      error: data.error,
      thinking: data.thinking,
      attachments: data.attachments,
      feedback: data.feedback,
      // 保留原有字段
      dateTime: data.dateTime,
      text: data.text,
      inversion: data.inversion,
      conversationOptions: data.conversationOptions,
      requestOptions: data.requestOptions,
      answerList: data.answerList,
      endstatus: data.endstatus,
      conversationId: data.conversationId || data.conversationOptions?.conversationId,
      category: data.category,
      agentId: data.agentId,
      modelId: data.modelId,
      annotation: data.annotation,
      questionArr: data.questionArr,
      problem: data.problem,
      questionState: data.questionState
    }
  }

  // 批量转换消息
  convertMessages(messages: any[]): Message[] {
    return messages.map(msg => this.convertToStandardMessage(msg))
  }

  // 提交反馈
  async submitFeedback(messageId: string, feedback: any): Promise<any> {
    try {
      const response = await post({
        url: '/emind/conversation/feedback',
        data: {
          messageId,
          feedbackType: feedback.type,
          reason: feedback.reason,
          comment: feedback.category,
          timestamp: Date.now()
        },
        headers: {
          'Content-Type': 'application/json'
        }
      })

      return response
    } catch (error) {
      console.error('提交反馈失败:', error)
      throw error
    }
  }

  // 文件上传
  async uploadFile(file: File): Promise<any> {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('name', file.name)
      formData.append('guid', new Date().getTime().toString())
      formData.append('chunk', '0')
      formData.append('chunks', '1')
      formData.append('storeId', '')

      const response = await post({
        url: '/resource/files/upload',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      return response
    } catch (error) {
      console.error('文件上传失败:', error)
      throw error
    }
  }

  // 语音识别
  async voiceToText(audioData: any): Promise<any> {
    try {
      const response = await post({
        url: '/sse_api/speech_recognition',
        data: audioData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      return response
    } catch (error) {
      console.error('语音识别失败:', error)
      throw error
    }
  }

  // 文本转语音
  async textToSpeech(text: string, conversationId?: string): Promise<any> {
    try {
      const response = await post({
        url: '/sse_api/text_to_speach',
        data: {
          msg: text,
          conversationId
        },
        headers: {
          'Content-Type': 'application/json'
        }
      })

      return response
    } catch (error) {
      console.error('文本转语音失败:', error)
      throw error
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 11)
  }
}

// 默认适配器实例
export const defaultAPIAdapter = new APIAdapter()

// 工厂函数
export function createAPIAdapter(config: AIChatConfig): APIAdapter {
  return new APIAdapter(config)
}
