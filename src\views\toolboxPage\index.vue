<script setup>
import { onMounted, ref } from "vue";
import { NInput, NSpin } from "naive-ui";

import { useRouter } from "vue-router";
import icon1 from "@/assets/toolboxPage/icon1.png";
import icon2 from "@/assets/toolboxPage/icon2.png";
import icon3 from "@/assets/toolboxPage/icon3.png";
import icon4 from "@/assets/toolboxPage/icon4.png";
import icon5 from "@/assets/toolboxPage/icon5.png";
import icon6 from "@/assets/toolboxPage/icon6.png";
import icon7 from "@/assets/toolboxPage/icon7.png";
import icon8 from "@/assets/toolboxPage/icon8.png";
import icon9 from "@/assets/toolboxPage/icon9.png";
import icon10 from "@/assets/toolboxPage/icon10.png";
import icon11 from "@/assets/toolboxPage/icon11.png";
import icon12 from "@/assets/toolboxPage/icon12.png";
import icon13 from "@/assets/toolboxPage/icon13.png";
import icon14 from "@/assets/toolboxPage/icon14.png";
import icon15 from "@/assets/toolboxPage/icon15.png";
import icon16 from "@/assets/toolboxPage/icon16.png";
import icon17 from "@/assets/toolboxPage/icon17.png";
import icon19 from "@/assets/toolboxPage/icon19.png";
import icon18 from "@/assets/toolboxPage/kejian.png";
import degreeofheat from "@/assets/toolboxPage/degreeofheat.png";
import notbookmarked from "@/assets/toolboxPage/notbookmarked.png";
import collected from "@/assets/toolboxPage/collected.png";
import { getListApi } from "@/api/tools";
import {
	delcollectionApi,
	setcollectionApi,
	setheatApi,
} from "@/api/applicationPage";
import { useToolsStore } from "@/store";
import { useToolboxPageStore } from "@/store/modules/toolboxPage";
import { debounce } from "@/utils/functions/debounce";

const router = useRouter();
const identitytype = ref(0);
function changeidentitytype(type) {
	identitytype.value = type;
}
const loadingshow = ref(false);
const imglist = ref({
	icon1,
	icon2,
	icon3,
	icon4,
	icon5,
	icon6,
	icon7,
	icon8,
	icon9,
	icon10,
	icon11,
	icon12,
	icon13,
	icon14,
	icon15,
	icon16,
	icon17,
	icon18,
	icon19,
});
const searchvalue = ref("");
const moretagarr = ref([
	{ name: "全部", ischeck: true, value: 0 },
	// { name: "教研教学", ischeck: false, value: 1 },
	// { name: "自适应学习", ischeck: false, value: 2 },
	// { name: "校务服务", ischeck: false, value: 3 },
]);
// 修改这一行
const applicationarr = ref([]);
const ToolsStore = useToolsStore();
const toolboxPageStore = useToolboxPageStore();

function jumpfun(item) {
	console.log(item);
	setheatApi({ mainBodyId: item.id, category: "0", id: item.heatScaleId }).then(
		(res) => {
			if (item.category == "99") {
				router.push({
					path: "/iframePage",
					query: {
						name: item.name,
					},
				});
				return;
			}
			if (item.id == 12) {
				router.push({
					path: "/teachPlanContentGen",
				});
				return;
			}
			if (item.category == 9) {
				router.push({
					path: "/courseware",
				});
				ToolsStore.updateToolInfo(item);
				return;
			}
			if (item.category == 14) {
				router.push({
					path: "/videoStudyHelper",
				});
				ToolsStore.updateToolInfo(item);
				return;
			}
			ToolsStore.updateToolInfo(item);
			jumpPage("/chat");
		}
	);
}
function jumpPage(url) {
	if (url) router.push(url);
	else router.go(-1);
}
const changecollectfun = (record) => {
	loadingshow.value = true;
	if (record.isCollection) {
		delcollectionApi({ collectionId: record.collectionId }).then((res) => {
			getlistfun();
		});
	} else {
		setcollectionApi({ mainBodyId: record.id, category: "0" }).then((res) => {
			getlistfun();
		});
	}
};

const changmoretagfun = (index) => {
	moretagarr.value.forEach((item, i) => {
		item.ischeck = i === index;
	});
};
// 修改 getlistfun 支持 name 参数
const getlistfun = (isSave = false) => {
	if (!loadingshow.value) loadingshow.value = true;

	getListApi({ name: searchvalue.value })
		.then((res) => {
			loadingshow.value = false;
			console.log(res);
			res.data.forEach((item) => {
				item.icon = imglist.value[item.icon];
			});
			applicationarr.value = res.data;
			console.log(applicationarr.value, "toolboxPage");

			if (isSave)
				toolboxPageStore.updateToolboxData({
					applicationList: applicationarr.value,
				});
		})
		.catch(() => {
			loadingshow.value = false;
		});
};
const searchTap = debounce(getlistfun, 500);
onMounted(() => {
	getlistfun(true);
});
</script>

<template>
	<div class="app p-8 pr-9 pl-10">
		<NSpin :show="loadingshow">
			<header class="bothends flex justify-between items-center">
				<div
					class="title h-9 font-semibold text-[26px] text-[#2f3033] leading-9 flex items-center"
				>
					<img
						class="w-[22px] h-[22px] mr-2"
						src="@/assets/toolboxPage/titicon.png"
						alt=""
					/>
					智能体广场
				</div>
				<div class="w-[400px] h-12">
					<NInput
						v-model:value="searchvalue"
						clearable
						round
						placeholder="搜索智能体"
						size="large"
						class="modern-search-input"
						@update:value="getlistfun"
					>
						<template #prefix>
							<div class="search-prefix-icon">
								<img
									class="w-[18px] h-[18px] opacity-60"
									src="../../assets/toolboxPage/SearchOutline.png"
								/>
							</div>
						</template>
						<template #suffix>
							<div class="search-suffix-btn" @click="searchTap">
								<img
									class="w-[18px] h-[18px]"
									src="../../assets/toolboxPage/SearchOutline.png"
								/>
							</div>
						</template>
					</NInput>
				</div>
			</header>

			<div class="section-title">
				<img class="w-6 h-6 mr-3" src="@/assets/toolboxPage/collecticon.png" />
				我的收藏
			</div>

			<div class="collectbox flex flex-wrap">
				<div
					v-for="(item, index) in applicationarr.filter(
						(item) => item.isCollection
					)"
					:key="index"
					class="collect-tag"
					@click="jumpfun(item)"
				>
					<img class="collect-icon" :src="item.icon" />
					<p>{{ item.name }}</p>
				</div>
			</div>

			<div class="section-title section-title-top">
				<img class="w-6 h-6 mr-3" src="@/assets/toolboxPage/moreicon.png" />
				发现更多
			</div>

			<div class="collectbox flex flex-wrap">
				<div
					v-for="(item, index) in moretagarr"
					:key="index"
					class="category-tag"
					:class="{ 'category-tag-active': item.ischeck }"
					@click="changmoretagfun(index)"
				>
					{{ item.name }}
				</div>
			</div>

			<div class="applicationrow" v-if="applicationarr.length">
				<div
					v-for="(item, index) in applicationarr"
					:key="index"
					class="application-card"
					@click="jumpfun(item)"
				>
					<div class="card-content">
						<div class="app-icon-wrapper">
							<img class="app-icon" :src="item.icon" />
						</div>
						<div class="app-info">
							<h3 class="app-name">
								{{ item.name }}
							</h3>
							<div class="app-meta">
								<div class="heat-info">
									<img class="meta-icon" :src="degreeofheat" />
									<span>热度 {{ item.heatScaleNum || 0 }}</span>
								</div>
								<div
									class="collect-action"
									@click.stop="changecollectfun(item)"
								>
									<img
										class="meta-icon"
										:src="item.isCollection ? collected : notbookmarked"
									/>
									<span>{{ item.isCollection ? "已收藏" : "收藏" }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div v-else class="empty-state">
				<span class="empty-svg">
					<svg
						width="96"
						height="96"
						viewBox="0 0 96 96"
						fill="none"
						xmlns="http://www.w3.org/2000/svg"
					>
						<rect x="12" y="28" width="72" height="48" rx="16" fill="#eaf1ff" />
						<rect x="24" y="40" width="48" height="24" rx="8" fill="#b6d0ff" />
						<circle cx="36" cy="52" r="4" fill="#fff" />
						<circle cx="60" cy="52" r="4" fill="#fff" />
						<rect x="44" y="64" width="8" height="4" rx="2" fill="#b6d0ff" />
						<rect x="40" y="20" width="16" height="12" rx="6" fill="#b6d0ff" />
						<rect x="46" y="12" width="4" height="8" rx="2" fill="#b6d0ff" />
					</svg>
				</span>
				<div class="empty-text">暂无智能体！</div>
			</div>
		</NSpin>
	</div>
</template>

<style scoped lang="less">
// 现代化搜索框样式
.modern-search-input {
	:deep(.n-input-wrapper) {
		padding-right: 4px;
		padding-left: 16px;
		border: none;
		background: transparent;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
		transition: all 0.3s ease;
		border-radius: 3rem;

		&:hover {
			box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
		}

		&.n-input-wrapper--focus {
			box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.15),
				0 4px 16px rgba(0, 0, 0, 0.1);
		}
	}

	:deep(.n-input__input-el) {
		font-size: 15px;
		color: #2f3033;

		&::placeholder {
			color: #9ca3af;
			font-weight: 400;
		}
	}
}

.search-prefix-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 8px;
}

.search-suffix-btn {
	width: 36px;
	height: 36px;
	background: linear-gradient(135deg, #125eff 0%, #1e7fff 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.2s ease;

	&:hover {
		transform: scale(1.05);
		box-shadow: 0 4px 12px rgba(18, 94, 255, 0.3);
	}

	&:active {
		transform: scale(0.98);
	}

	img {
		filter: brightness(0) invert(1);
	}
}

// 章节标题样式
.section-title {
	height: 36px;
	font-weight: 600;
	font-size: 20px;
	color: #1f2937;
	line-height: 36px;
	display: flex;
	align-items: center;
	margin-top: 32px;
	margin-bottom: 16px;

	&.section-title-top {
		margin-top: 40px;
	}
}

// 收藏标签样式
.collect-tag {
	min-width: 120px;
	height: 40px;
	background: #125eff14;
	border: 1px solid #125eff26;
	border-radius: 10px;

	line-height: 40px;
	text-align: center;
	margin-right: 12px;
	margin-bottom: 12px;
	padding: 0 16px;
	cursor: pointer;
	transition: all 0.2s ease;
	display: flex;
	justify-content: space-evenly;
	.collect-icon {
		width: 22px;
		height: 22px;
		margin-top: 8px;
		margin-right: 4px;
	}
	> p {
		font-weight: 500;
		font-size: 14px;
	}
	&:hover {
		background: linear-gradient(135deg, #125eff 0%, #1e7fff 100%);
		color: #ffffff;
		transform: translateY(-1px);
		box-shadow: 0 4px 12px rgba(18, 94, 255, 0.25);
	}
}

// 分类标签样式
.category-tag {
	width: 128px;
	height: 40px;
	background: #ffffff;
	border: 1px solid #e5e7eb;
	border-radius: 20px;
	font-weight: 500;
	font-size: 14px;
	color: #6b7280;
	line-height: 40px;
	text-align: center;
	margin-right: 12px;
	cursor: pointer;
	transition: all 0.2s ease;

	&:hover {
		border-color: #125eff;
		color: #125eff;
		box-shadow: 0 2px 8px rgba(18, 94, 255, 0.1);
	}

	&.category-tag-active {
		background: linear-gradient(135deg, #125eff 0%, #1e7fff 100%);
		border-color: #125eff;
		color: #ffffff;
		box-shadow: 0 4px 12px rgba(18, 94, 255, 0.25);
	}
}

// 应用卡片网格布局
.applicationrow {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(330px, 1fr));
	gap: 20px;
	margin-top: 32px;
}

// 现代化应用卡片设计
.application-card {
	background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
	border: 1px solid #e8ecf0;
	border-radius: 16px;
	padding: 0;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

	&:hover {
		background: #ffffff;
		border-color: #125eff;
		box-shadow: 0 8px 32px rgba(18, 94, 255, 0.12);
		transform: translateY(-2px);
	}
}

.card-content {
	display: flex;
	align-items: center;
	padding: 24px;
	height: 112px;
}

.app-icon-wrapper {
	position: relative;
	margin-right: 20px;

	// 默认状态的微妙背景
	&::before {
		content: "";
		position: absolute;
		top: -6px;
		left: -6px;
		right: -6px;
		bottom: -6px;
		background: linear-gradient(
			135deg,
			rgba(18, 94, 255, 0.05) 0%,
			rgba(30, 127, 255, 0.02) 100%
		);
		border-radius: 18px;
		opacity: 1;
		transition: all 0.3s ease;
	}

	.application-card:hover &::before {
		background: linear-gradient(
			135deg,
			rgba(18, 94, 255, 0.15) 0%,
			rgba(30, 127, 255, 0.08) 100%
		);
		transform: scale(1.05);
	}
}

.app-icon {
	width: 64px;
	height: 64px;
	border-radius: 12px;
	position: relative;
	z-index: 1;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;

	.application-card:hover & {
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
		transform: scale(1.02);
	}
}

.app-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.app-name {
	font-size: 18px;
	font-weight: 600;
	color: #1f2937;
	line-height: 24px;
	margin: 0 0 12px 0;
	transition: color 0.2s ease;

	.application-card:hover & {
		color: #125eff;
	}
}

.app-meta {
	display: flex;
	align-items: center;
	gap: 20px;
}

.heat-info,
.collect-action {
	display: flex;
	align-items: center;
	font-size: 13px;
	color: #6b7280;
	transition: color 0.2s ease;

	.meta-icon {
		width: 14px;
		height: 14px;
		margin-right: 6px;
		opacity: 0.8;
	}
}

.collect-action {
	cursor: pointer;
	padding: 4px 8px;
	border-radius: 6px;
	transition: all 0.2s ease;

	&:hover {
		background: rgba(18, 94, 255, 0.08);
		color: #125eff;

		.meta-icon {
			opacity: 1;
		}
	}
}

.empty-state {
	width: 100%;
	min-height: 320px;
	padding: 48px 0;
	margin: 40px auto;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #f8faff 0%, #eef4ff 100%);
	border-radius: 24px;
	box-shadow: 0 4px 16px rgba(18, 94, 255, 0.08);
	.empty-svg {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20px;
		svg {
			display: block;
			width: 96px;
			height: 96px;
			opacity: 0.85;
		}
	}
	.empty-text {
		color: #7a8ca3;
		font-size: 18px;
		font-weight: 500;
		text-align: center;
		letter-spacing: 1px;
	}
}

// 响应式设计
@media (max-width: 1200px) {
	.searchbox {
		width: 350px !important;
	}
}

@media (max-width: 768px) {
	.app {
		padding: 16px 20px;
	}

	.bothends {
		flex-direction: column;
		gap: 20px;
		align-items: flex-start;
	}

	.searchbox {
		width: 100% !important;
	}

	.applicationrow {
		grid-template-columns: 1fr;
	}

	.card-content {
		padding: 20px;
	}

	.section-title {
		font-size: 18px;
		margin-top: 24px;
	}
}
</style>
