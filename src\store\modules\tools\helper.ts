import { ss } from '@/utils/storage'

const LOCAL_NAME = 'toolsStorage'

export interface ToolInfos {
  apiUrl: string
  des: string
  heatNum: number
  icon: string
  id: number
  isTeacher: boolean
  iscollect: boolean
  name: string
  openingQuestionArr: string[]
  title: string
  capacityCode: string
  agent_conversation?: string
  category?: string
  agentId?: string
}

export interface ToolLod {
  loding: boolean
  current: number | string
}

export interface ToolsState {
  ToolInfo: ToolInfos
  ToolLoding?: ToolLod
}

export function defaultSetting(): ToolsState {
  return {
    ToolInfo: {
      apiUrl: '',
      des: '',
      heatNum: 0,
      icon: '',
      id: 0,
      isTeacher: false,
      iscollect: false,
      name: '',
      openingQuestionArr: [],
      title: '',
      capacityCode: '',
      agent_conversation: '',
    },

  }
}

export function getLocalState(): ToolsState {
  const localSetting: ToolsState | undefined = ss.get(LOCAL_NAME)
  return { ...defaultSetting(), ...localSetting }
}

export function setLocalState(setting: ToolsState): void {
  ss.set(LOCAL_NAME, setting)
}
