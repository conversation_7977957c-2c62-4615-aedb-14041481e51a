<script setup lang="ts">
import { computed, ref } from "vue";
import { NButton, NInput, NSpin } from "naive-ui";
import { Message } from "@/views/tankChat/components";
import { useScroll } from "@/views/tankChat/hooks/useScroll";
import { useBasicLayout } from "@/hooks/useBasicLayout";
import { t } from "@/locales";
import { fetchChatAPIProcess } from "@/api/tankChat";
import aiAvatar from "@/assets/applicationPage/icon1.png";

interface Props {
  // 用户提示词内容
  userPrompt?: string;
  // 系统提示词
  systemPrompt?: string;
  // 提示词变量
  promptVariables?: Record<string, string>;
  loading?: boolean;
  // 添加聊天所需的参数
  agentId?: string;
  modelId?: string;
  modelTemp?: number;
  maxLength?: number;
  promptTemplate?: string;
}

const props = withDefaults(defineProps<Props>(), {
  userPrompt: "",
  systemPrompt: "",
  promptVariables: () => ({}),
  loading: false,
  agentId: "",
  modelId: "",
  modelTemp: 0.7,
  maxLength: 2000,
  promptTemplate: "",
});

defineOptions({
  name: "PromptChatPreview",
});

const { scrollRef, scrollToBottom, scrollToBottomIfAtBottom } = useScroll();
const { isMobile } = useBasicLayout();

const prompt = ref("");
const chatLoading = ref(false);
const dataSources = ref<any[]>([]);
let controller = new AbortController();
let conversationId = ref<string>("");

// 添加聊天消息
const addChat = (chat: any) => {
  dataSources.value.push(chat);
};

// 更新聊天消息
const updateChat = (index: number, chat: any) => {
  if (index >= 0 && index < dataSources.value.length) {
    dataSources.value[index] = { ...dataSources.value[index], ...chat };
  }
};

// 清空聊天记录和重置组件
const handleRefresh = () => {
  if (chatLoading.value) return;
  dataSources.value = [];
  prompt.value = "";
  chatLoading.value = false;
  conversationId.value = "";
};

// 处理提交 - 真实API调用
const handleSubmit = async () => {
  if (!prompt.value.trim() || chatLoading.value) return;

  const message = prompt.value;

  // 添加用户消息
  addChat({
    dateTime: new Date().toLocaleString(),
    text: message,
    inversion: true,
    error: false,
    loading: false,
    answerList: [],
    endstatus: 1,
    conversationOptions: null,
    requestOptions: { prompt: message, options: null },
  });

  prompt.value = "";
  chatLoading.value = true;
  controller = new AbortController();

  // 添加AI思考中的消息
  addChat({
    dateTime: new Date().toLocaleString(),
    text: t("chat.thinking"),
    inversion: false,
    error: false,
    loading: true,
    answerList: [],
    endstatus: 1,
    conversationOptions: null,
  });

  scrollToBottom();

  try {
    let lastText = "";

    // 如果没有对话ID，创建一个临时的
    if (!conversationId.value) {
      conversationId.value = "preview_prompt_" + Date.now();
    }

    // 处理提示词变量替换
    let processedPromptTemplate = props.promptTemplate || props.systemPrompt;
    Object.entries(props.promptVariables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, "g");
      processedPromptTemplate = processedPromptTemplate.replace(regex, value);
    });
    console.log(processedPromptTemplate, "processedPromptTemplate");

    const fetchChatAPIOnce = async () => {
      await fetchChatAPIProcess({
        signal: controller.signal,
        question: message,
        conversationId: conversationId.value,
        category: "0",
        agentId: props.agentId,
        modelId: props.modelId,
        modelTemp: props.modelTemp,
        maxLength: props.maxLength,
        promptTemplate: processedPromptTemplate,
        onDownloadProgress: ({ event }) => {
          const xhr = event.target;
          const { responseText } = xhr;

          // 按行分割响应文本
          const lines = responseText
            .split("\n")
            .filter((line: string) => line.trim() !== "");

          // 重置文本,避免重复累加
          lastText = "";

          // 处理每一行数据
          for (const line of lines) {
            const trimmedLine = line.replace(/^data: /, "").trim();

            try {
              const data = JSON.parse(trimmedLine?.substring(5));

              // 直接使用当前响应文本,不进行累加
              const deltaContent = data.choices[0].message.content || "";
              lastText += deltaContent;

              updateChat(dataSources.value.length - 1, {
                dateTime: new Date().toLocaleString(),
                text: lastText,
                inversion: false,
                error: false,
                loading: true,
                conversationOptions: {
                  conversationId:
                    data.conversationContentId || conversationId.value,
                  parentMessageId: data.id || "",
                },
              });

              scrollToBottomIfAtBottom();

              if (
                data.choices[0].finish_reason === "stop" ||
                data.choices[0].finish_reason === "STOP"
              ) {
                updateChat(dataSources.value.length - 1, {
                  loading: false,
                  answerList: data.answerList || [],
                });
                chatLoading.value = false;
              }
            } catch (error) {
              console.error("Parse response error:", error);
            }
          }
        },
      });
    };

    await fetchChatAPIOnce();
  } catch (error: any) {
    console.error("Chat API error:", error);

    // 更新最后一条消息为错误状态
    if (dataSources.value.length > 0) {
      updateChat(dataSources.value.length - 1, {
        text: "抱歉，发生了错误，请稍后重试。",
        loading: false,
        error: true,
      });
    }
  } finally {
    chatLoading.value = false;
    scrollToBottom();
  }
};

// 处理Enter键
const handleEnter = (event: KeyboardEvent) => {
  if (!isMobile.value) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSubmit();
    }
  } else {
    if (event.key === "Enter" && event.ctrlKey) {
      event.preventDefault();
      handleSubmit();
    }
  }
};

// 计算是否显示开场白
const showWelcome = computed(() => dataSources.value.length === 0);

// 检查是否可以发送消息
const canSend = computed(() => {
  return prompt.value.trim() && !chatLoading.value;
});
</script>

<template>
  <div
    class="flex flex-col w-full h-full bg-[#fcfcfc]"
    style="border: 1px solid #dadada; border-radius: 16px; overflow: hidden"
  >
    <!-- 头部标题 -->
    <header class="p-4 bg-white flex-shrink-0">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <img
            class="w-5 mr-2"
            src="@/assets/workShopPage/test-tit.png"
            alt=""
          />
          <p class="text-[16px] font-medium">调试预览</p>
        </div>
        <!-- 刷新按钮 -->
        <img
          src="@/assets/sync1.png"
          :class="chatLoading ? 'resetBtn disable' : 'resetBtn able'"
          @click="handleRefresh"
          alt=""
        />
      </div>
    </header>

    <!-- 主聊天区域 -->
    <main class="flex-1 overflow-hidden">
      <div
        id="scrollRef"
        ref="scrollRef"
        class="h-full overflow-hidden overflow-y-auto"
      >
        <NSpin :show="loading">
          <!-- 聊天消息 -->
          <div class="w-full max-w-screen-xl m-auto p-4">
            <Message
              v-for="(item, index) of dataSources"
              :key="index"
              :date-time="item.dateTime"
              :text="item.text"
              :inversion="item.inversion"
              :error="item.error"
              :loading="item.loading"
              :custom-avatar="!item.inversion ? aiAvatar : undefined"
              @delete="() => dataSources.splice(index, 1)"
            />
          </div>
        </NSpin>
      </div>
    </main>

    <!-- 底部输入区域 - 固定在底部 -->
    <footer class="flex-shrink-0 p-4 bg-[#fcfdfd]">
      <div class="max-w-screen-xl m-auto">
        <div class="input-container">
          <NInput
            class="custom-input"
            v-model:value="prompt"
            placeholder="有什么问题可以尽管问我哦…"
            size="large"
            :disabled="chatLoading"
            @keypress="handleEnter"
          >
            <template #suffix>
              <NButton
                color="#125EFF"
                class="!p-[8px] !rounded-[8px]"
                :disabled="!canSend"
                @click="handleSubmit"
              >
                <img
                  class="w-[20px] h-[20px]"
                  src="@/assets/workShopPage/test-btn.png"
                  alt=""
                />
              </NButton>
            </template>
          </NInput>
        </div>
        <p class="disclaimer-text">内容由AI生成，请以最新政策文件为准。</p>
      </div>
    </footer>
  </div>
</template>

<style lang="less" scoped>
.input-container {
  .custom-input {
    height: 60px;
    background: #f6f6f7;
    border-radius: 12px;

    :deep(.n-input-wrapper) {
      padding-left: 20px;
      background: #f6f6f7;
      border-radius: 12px;
    }

    :deep(.n-input__input-el) {
      background: #f6f6f7;
    }
  }
}

.disclaimer-text {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 12px;
  color: #3232334d;
  letter-spacing: 0;
  text-align: center;
  margin-top: 12px;
}

.resetBtn {
  width: 20px;
  height: 20px;
}
.resetBtn.able {
  cursor: pointer;
}
.resetBtn.disable {
  cursor: not-allowed;
}
/* 自定义滚动条 */
:deep(.n-scrollbar-rail) {
  right: 2px !important;
}

/deep/ .n-input .n-input__input-el {
  height: 60px;
}
</style>
