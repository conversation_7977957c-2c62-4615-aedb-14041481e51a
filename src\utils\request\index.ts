import type {AxiosProgressEvent, AxiosResponse, GenericAbortSignal} from 'axios'
import request from './axios'

export interface HttpOption {
	url: string
	data?: any
	method?: string
	headers?: any
	onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
	signal?: GenericAbortSignal
	beforeRequest?: () => void
	afterRequest?: () => void
}

export interface Response<T = any> {
	data: T
	message: string | null
	status: string
}

function http<T = any>(
	{url, data, method, headers, onDownloadProgress, signal, beforeRequest, afterRequest}: HttpOption,
) {
	const successHandler = (res: AxiosResponse<Response<T>>) => {
		// const authStore = useAuthStore()

		// if (res.data.status === 'Success' || typeof res.data === 'string')
		//   return res.data
		//
		// if (res.data.status === 'Unauthorized') {
		//   authStore.removeToken()
		//   window.location.reload()
		// }
		return res.data
		// return Promise.reject(res.data)
	}

	// 定义自定义错误类型
	class HttpError extends Error {
		status?: number;
		response?: any;

		constructor(message: string, status?: number, response?: any) {
			super(message);
			this.name = 'HttpError';
			this.status = status;
			this.response = response;
		}
	}

	const failHandler = (error: Response<Error>) => {
		console.log(error);

		afterRequest?.()
		// 从 axios 错误对象中提取信息
		const message = error?.response?.data?.message ||
			error?.message ||
			'请求失败';
		const status = error?.response?.status;
		const response = error?.response?.data;

		// 抛出自定义错误，携带更多信息
		throw new HttpError(message, status, response);
	}

	beforeRequest?.()

	method = method || 'GET'

	const params = Object.assign(typeof data === 'function' ? data() : data ?? {}, {})

	return method === 'GET'
		? request.get(url, {params, signal, onDownloadProgress}).then(successHandler, failHandler)
		: request.post(url, params, {headers, signal, onDownloadProgress}).then(successHandler, failHandler)
}

export function get<T = any>(
	{url, data, method = 'GET', onDownloadProgress, signal, beforeRequest, afterRequest}: HttpOption,
): Promise<Response<T>> {
	return http<T>({
		url,
		method,
		data,
		onDownloadProgress,
		signal,
		beforeRequest,
		afterRequest,
	})
}

export function post<T = any>(
	{url, data, method = 'POST', headers, onDownloadProgress, signal, beforeRequest, afterRequest}: HttpOption,
): Promise<Response<T>> {
	return http<T>({
		url,
		method,
		data,
		headers,
		onDownloadProgress,
		signal,
		beforeRequest,
		afterRequest,
	})
}

export default post
