<template>
  <div class="knowledge-node" :class="{ 'selected': selected, 'running': isRunning }">
    <!-- 输入连接点 -->
    <Handle
      type="target"
      :position="Position.Left"
      :id="`${id}-input`"
      class="input-handle"
    />

    <!-- 节点主体 -->
    <div class="node-body">
      <!-- 状态指示器 -->
      <div class="status-indicator" :class="statusClass"></div>

      <!-- 节点头部 -->
      <div class="node-header">
        <img class="node-icon" src="@/assets/agentOrchestration/knowledgeRetrievalIcon.png" alt="知识检索">
        <div class="node-title">{{ data.label || '知识检索' }}</div>
      </div>

      <!-- 节点描述信息 -->
      <div v-if="data.description" class="node-description">
        {{ data.description }}
      </div>

      <!-- 知识库名称 -->
      <div class="database-name" v-if="displayDatabases.length == 0">请选择知识库</div>

      <!-- 知识库列表 - 多输出模式 -->
      <div v-if="!singleOutput" class="categories-list">
        <div
          v-for="(database, index) in displayDatabases"
          :key="database.id || index"
          class="category-item"
        >
          <span class="category-label">{{ database.name || database }}</span>
          <!-- 每个知识库的连接点 -->
      
        </div>
            <Handle
            type="source"
            :position="Position.Right"
           :id="`${id}-output`"
            class="output-handle database-handle"
          />
        <!-- <div v-if="databases.length > maxDisplayDatabases" class="more-indicator">
          其他 {{ databases.length - maxDisplayDatabases }} 个知识库
          <Handle
            v-for="(database, index) in hiddenDatabases"
            :key="`hidden-${database.id || index}`"
            type="source"
            :position="Position.Right"
            :id="`${id}-${database.id || index}`"
            class="output-handle database-handle hidden-handle"
          />
        </div> -->
      </div>
    </div>

    <!-- 单一输出连接点 - 单输出模式 -->
    <Handle
      type="source"
      :position="Position.Right"
      :id="`${id}-output`"
      class="output-handle"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import { NodeStatus } from '@/store/modules/orchestration'

interface KnowledgeNodeProps {
  id: string
  data: {
    label?: string
    description?: string
    status?: NodeStatus
    config?: {
      database?: string
      searchType?: string
      maxResults?: number
      [key: string]: any
    }
    [key: string]: any
  }
  selected?: boolean
}

const props = defineProps<KnowledgeNodeProps>()

// 计算属性
const isRunning = computed(() => props.data.status === NodeStatus.RUNNING)

const statusClass = computed(() => {
  switch (props.data.status) {
    case NodeStatus.RUNNING:
      return 'status-running'
    case NodeStatus.SUCCESS:
      return 'status-success'
    case NodeStatus.ERROR:
      return 'status-error'
    default:
      return 'status-idle'
  }
})

// 判断是否为单一输出模式
const singleOutput = computed(() => {
  return props.data.config?.singleOutput === true || props.data.config?.retrievalMode === 'single'
})

const databases = computed(() => {
  return props.data.config?.databases || []
})

const maxDisplayDatabases = 3

const displayDatabases = computed(() => {
  return databases.value.slice(0, maxDisplayDatabases)
})

const hiddenDatabases = computed(() => {
  return databases.value.slice(maxDisplayDatabases)
})
</script>

<style scoped lang="less">
@import './styles/unified-node-styles.less';

.knowledge-node {
  .rectangular-node-style();
  .unified-handle-style();

  .node-body {
    .node-header {
      .node-header-style();

      .node-icon {
        width: 16px;
        height: 16px;
        color: #6b7280;
      }
    }

    .node-description {
      .node-description-style();
    }

    .database-name {
      .node-subtitle-style();
      background: #f3f4f6;
      padding: 4px 8px;
      border-radius: 4px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;
    }

    .categories-list {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .category-item {
        .node-list-item-style();
        position: relative;

        .category-label {
          font-size: 11px;
          color: #6b7280;
        }

        // 知识库连接点样式 - 显示在节点卡片上方，位置与知识库对齐
        :deep(.database-handle) {
          position: absolute;
          right: -16px; // 放在卡片外侧，视觉上更明显
          top: 50%;
          transform: translateY(-50%);
          width: 10px;
          height: 10px;
          background: #3b82f6;
          border: 2px solid #ffffff;
          border-radius: 50%;
          transition: all 0.2s ease;
          z-index: 100; // 高层级，显示在卡片上方
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover {
            width: 12px;
            height: 12px;
            background: #2563eb;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          }

          &.vue-flow__handle-connecting {
            background: #125EFF;
            box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.3);
          }
        }
      }

      .more-indicator {
        .node-label-style();
        font-style: italic;
        text-align: center;
        padding: 2px 4px;
        color: #9ca3af;
        position: relative;

        // 隐藏知识库的连接点样式
        :deep(.hidden-handle) {
          position: absolute;
          right: -12px; // 放在卡片外侧，视觉上更明显
          top: 50%;
          transform: translateY(-50%);
          width: 10px;
          height: 10px;
          background: #3b82f6;
          border: 2px solid #ffffff;
          border-radius: 50%;
          transition: all 0.2s ease;
          z-index: 100; // 高层级，显示在卡片上方
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover {
            width: 12px;
            height: 12px;
            background: #2563eb;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          }

          &.vue-flow__handle-connecting {
            background: #125EFF;
            box-shadow: 0 0 0 3px rgba(18, 94, 255, 0.3);
          }
        }
      }
    }
  }

  .status-indicator {
    .status-indicator-style();
  }
}
</style>
