<template>
  <!-- 右侧滑出配置面板 -->
  <div
    class="config-panel-overlay"
    v-show="visible"
    @click="handleOverlayClick"
  >
    <div class="config-panel" :class="{ 'panel-open': visible }" @click.stop>
      <!-- 面板头部 -->
      <header class="panel-header">
        <div class="header-content">
          <div class="flex items-center">
            <img
              class="w-5 mr-2"
              src="@/assets/workShopPage/test-tit.png"
              alt=""
            />
            <p class="text-[16px] font-medium">测试预览</p>
          </div>
          <button class="close-btn" @click="handleClose">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </header>

      <!-- 面板内容 -->
      <div class="panel-content">
        <div class="flex flex-col w-full h-full bg-[#F7F9FF]">
          <!-- 主聊天区域 -->
          <main class="flex-1 overflow-hidden pl-[12px] pr-[12px]">
                <div id="scrollRef" ref="scrollRef" class="overflow-y-scroll h-[62vh]" v-if="dataSources.length > 0">
        <Message
          v-for="(item, index) of dataSources"
          :key="index"
          :answer-list="item.answerList"
          :category="item.category"
          :conversation-content-id="item.conversationOptions?.conversationId"
          :date-time="item.dateTime"
          :endstatus="item.endstatus"
          :error="item.error"
          :inversion="item.inversion"
          :loading="item.loading"
          :text="item.text"
        />
      </div>
            <div class="h-full overflow-hidden overflow-y-auto" v-if="dataSources.length == 0">
              <!-- 开场白区域 -->
              <div
                class="w-full m-auto kaichangbai headpadding"
                style="min-height: 60vh"
              >
                <div class="flex flex-col items-center justify-center p-8">
                  <img
                    class="w-16 h-16 mb-4"
                    src="@/assets/applicationPage/icon1.png"
                    alt=""
                  />
                  <div class="title text-xl font-medium mb-2">
                    你好~我是{{ props.currentFlow?.name }}
                  </div>
                  <div class="des text-gray-600 mb-8 text-center">
                    我是您的智能助手，有什么可以帮助您的吗？
                  </div>
                </div>
              </div>
            </div>
          </main>

          <!-- 底部输入区域 -->
          <footer class="flex-shrink-0 p-4 bg-white border-t border-gray-200">
            <div class="max-w-screen-xl m-auto">
              <div class="gradient-border relative">
                <div class="gradient-border-cen">
                  <NInput
                    class="!rounded-[16px]"
                    v-model:value="prompt"
                    :placeholder="
                      props.currentFlow?.name
                        ? `向${props.currentFlow?.name}提问`
                        : '有什么问题可以尽管问我哦…'
                    "
                    size="large"
                    :disabled="chatLoading"
                    @keypress="handleEnter"
                  >
                    <template #suffix>
                      <NButton
                        color="#125EFF"
                        class="!p-[8px] !rounded-[8px]"
                        :disabled="!prompt.trim() || chatLoading"
                        @click="handleSubmit"
                      >
                        <img
                          class="w-[20px] h-[20px]"
                          src="@/assets/workShopPage/test-btn.png"
                          alt=""
                        />
                      </NButton>
                    </template>
                  </NInput>
                </div>
              </div>
              <p class="text-center text-[#3232334d] text-[12px] mt-[12px]">
                内容由AI生成，请以最新政策文件为准。
              </p>
            </div>
          </footer>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { NInput, NButton } from "naive-ui";
import Message from "@/views/workflowChat/components/Message/index.vue";
import { useScroll } from "@/views/workflowChat/hooks/useScroll";
import { t } from "@/locales";
import type { FlowNode } from "@/store/modules/orchestration";
import { testRunApiProcess } from "@/api/agentOrchestration";
import { useOrchestrationStore, NodeType,NodeStatus } from "@/store/modules/orchestration";
const { scrollRef, scrollToBottom, scrollToBottomIfAtBottom } = useScroll();

const props = defineProps<{
  show: boolean;
  currentFlow: FlowNode | null;
}>();

const orchestrationStore = useOrchestrationStore();
var backendData = ref(orchestrationStore.exportBackendFlow());
// 添加一个对象来存储节点开始的时间戳
const nodeStartTimeMap = ref<Record<string, number>>({});

const emit = defineEmits<{
  "update:show": [value: boolean];
  save: [config: any];
}>();

// 双向绑定
const visible = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

// 聊天相关字段
const prompt = ref("");
const chatLoading = ref(false);
const dataSources: any = ref([]);
const addChat = (uuid: any, chat: any) => {
  dataSources.value.push({
    uuid,
    ...chat,
  });
};
const updateChat = (uuid: any, index: any, chat: any) => {
  if (!uuid || uuid === 0) {
    if (dataSources.value.length) dataSources.value[0].data[index] = chat;
    return;
  }
  dataSources.value[index] = chat;
};
// 监听页面是否显示
watch(
  () => visible,
  (newVisible) => {
    if (newVisible) {
      backendData.value = orchestrationStore.exportBackendFlow();
    }
  },
  { immediate: true, deep: true }
);

// 方法
const handleClose = () => {
  visible.value = false;
};

const handleOverlayClick = () => {
  handleClose();
};
// 流程运行相关方法
const runFlow = async () => {
  try {
    await orchestrationStore.runFlow();
    // orchestrationStore.runNode
  } catch (error) {
    console.log(error);
  }
};

const stopFlow = () => {
  orchestrationStore.stopFlow();
};

// 处理Enter键
const handleEnter = (event: KeyboardEvent) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    handleSubmit();
  }
};
let controller = new AbortController();

const runTextFlow = async () => {
    var query=prompt.value;

    addChat(props.currentFlow?.id, {
    dateTime: new Date().toLocaleString(),
    text: query,
    answerList: [],
    endstatus: 1,
    inversion: true,
    error: false,
    conversationOptions: null,
    requestOptions: { prompt: query, options: null },
  });
    prompt.value = "";
      addChat(props.currentFlow?.id, {
    dateTime: new Date().toLocaleString(),
    text: t("chat.thinking"),
    loading: true,
    answerList: [],
    endstatus: 1,
    inversion: false,
    error: false,
    conversationOptions: null,
  });
  await scrollToBottom();
  orchestrationStore.resetAllNodeStatus();
  controller = new AbortController();
  // 清空节点开始时间记录
  nodeStartTimeMap.value = {};

  const testRunApiOnce = async () => {
    await testRunApiProcess({
      signal: controller.signal,
      query: query,
      id: props.currentFlow?.id,
      onDownloadProgress: ({ event }) => {
        const xhr = event.target;
        const { responseText } = xhr;
        // 按行分割响应文本
        const lines = responseText
          .split("\n")
          .filter((line) => line.trim() !== "");
        console.log(lines);
        // 处理每一行数据
        for (const line of lines) {
          const trimmedLine = line.replace(/^data: /, "").trim();
          try {
            const data = JSON.parse(trimmedLine?.substring(5));

            // 直接使用当前响应文本,不进行累加
            const deltaContent = JSON.parse(data.info);
            console.log(data);
            console.log(deltaContent);

            if (data.event === "workflow_started") {
              orchestrationStore.setRunning(true);
              // 找到开始节点
              const startNode = props.currentFlow?.nodes.find(
                (n) => n.type === NodeType.START
              );
              if (!startNode) {
                throw new Error("未找到开始节点");
              }
              // 从开始节点开始执行
              // orchestrationStore.runNode({node:{nodeId:startNode.id}});
            } else if (data.event === "workflow_end") {
              chatLoading.value = false;

              orchestrationStore.setRunning(false);
              const endNode = props.currentFlow?.nodes.find(
                (n) => n.type === NodeType.END
              );
              if (!endNode) {
                throw new Error("未找到结束节点");
              }
              // 执行结束节点
              orchestrationStore.runNode({node:{nodeId:endNode.id},data:{inputs:deltaContent.data?.inputs,outputs:deltaContent.data?.outputs}});
            try {
                updateChat(props.currentFlow?.id, dataSources.value.length - 1, {
                dateTime: new Date().toLocaleString(),
                text: deltaContent.data?.outputs,
                inversion: false,
                error: false,
                loading: false,
                conversationOptions: {
                  conversationId: data.conversationContentId || "",
                  parentMessageId: data.id || "",
                },
              });
              
            } catch (error) {
              console.log(error);
            }
           
          
          }
            if (data.event === "node_started") {
              // 记录节点开始时间
              const nodeId = deltaContent.node?.nodeId;
              const timestamp = deltaContent.timestamp;
              if (nodeId && timestamp) {
                nodeStartTimeMap.value[nodeId] = timestamp;
              }
                orchestrationStore.updateNodeStatus(nodeId, NodeStatus.RUNNING)

            }
            if (data.event === "node_finished") {
              // 检查是否有该节点的开始时间
              const nodeId = deltaContent.node?.nodeId;
              const starttimestamp = nodeStartTimeMap.value[nodeId];
                deltaContent.starttimestamp= starttimestamp;
              orchestrationStore.runNode(deltaContent);
            }
          } catch (error) {
            orchestrationStore.setRunning(false);
            chatLoading.value = false;
          }
        }
      },
    });
  };

  await testRunApiOnce();
};
// 处理提交
const handleSubmit = () => {
  console.log("提交按钮被点击", prompt.value);
  if (!prompt.value.trim() || chatLoading.value) {
    console.log("输入为空或正在加载中");
    return;
  }
  runTextFlow();
  // testRunApi({
  //   graph: props.currentFlow,
  //   query: prompt.value,
  // }).then((res) => {
  //   console.log(res);
  // });
  // 这里可以添加实际的提交逻辑
  console.log("发送消息:", prompt.value);
  // prompt.value = "";
};
</script>

<style scoped lang="less">
.config-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  transition: opacity 0.3s ease;
}

.config-panel {
  position: fixed;
  top: 50%;
  right: -500px;
  width: 540px;
  height: 80%;
  background: white;
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
  transform: translateY(-50%);
  border-radius: 12px;
  overflow: hidden;
}

.config-panel.panel-open {
  right: 0;
}

.panel-header {
  height: 60px;
  padding: 0 20px;
  border-bottom: 1px solid #e2e8f0;
  background: white;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.close-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #64748b;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #f1f5f9;
  color: #1a202c;
}

.panel-content {
  flex: 1;
  overflow: hidden;
}

// 聊天预览样式
.kaichangbai {
  .title {
    height: 25px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    font-size: 18px;
    color: #323233;
    letter-spacing: 0;
    text-align: center;
    margin-top: 19px;
    margin-bottom: 10px;
  }

  .des {
    height: 22px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 16px;
    color: #606266;
    letter-spacing: 0;
    text-align: center;
    margin-bottom: 30px;
  }
}

.headpadding {
  padding: 0 20px;
}

// 输入框样式
.gradient-border {
  border-radius: 17px;
  // background-image: linear-gradient(114deg, #ca82ff 0%, #5479f5 100%);

  .gradient-border-cen {
    position: absolute;
    width: calc(100% - 4px);
    background: #fff;
    border-radius: 16px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border: 1px solid #ededed;
    box-shadow: 0 0 15px 0 #d8d8d866;
    border-radius: 16px;
    display: flex;
    overflow: hidden;
    align-items: center;
    // padding-left: 20px;
    // padding-right: 8px;
  }
}

.input-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  height: 48px;
}

.input-field {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  color: #323233;

  &::placeholder {
    color: #c7c7c7;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.send-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #125eff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: #0d47a1;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}
</style>
  