import { markRaw } from 'vue'
import StartNode from './StartNode.vue'
import EndNode from './EndNode.vue'
import QuestionClassifierNode from './QuestionClassifierNode.vue'
import LLMNode from './llmNode.vue'
import ConditionNode from './ConditionNode.vue'
import APINode from './APINode.vue'
import ChatNode from './ChatNode.vue'
import KnowledgeNode from './KnowledgeNode.vue'
import intentionRecognitionNode from './intentionRecognitionNode.vue'
import aggregationNode from './aggregationNode.vue'
import assignerNode from './assignerNode.vue'
import directReplyNode from './directReplyNode.vue'
import toolNode from './toolNode.vue'
import cycleNode from './cycleNode.vue'
// 导出所有节点组件
export {
  StartNode,
  EndNode,
  QuestionClassifierNode,
  LLMNode,
  ConditionNode,
  APINode,
  ChatNode,
  KnowledgeNode,
  intentionRecognitionNode,
  aggregationNode,
  assignerNode,
  directReplyNode,
  toolNode,
  cycleNode
}

// 创建节点类型映射，用于Vue Flow
export const nodeTypes = {
  start: markRaw(StartNode),
  end: markRaw(EndNode),
  'question-classifier': markRaw(intentionRecognitionNode),
  llm: markRaw(LLMNode),
  condition: markRaw(ConditionNode),
  api: markRaw(APINode),
  chat: markRaw(ChatNode),
  knowledge: markRaw(KnowledgeNode),
  questionClassifier: markRaw(QuestionClassifierNode),
  aggregation: markRaw(aggregationNode),
  assigner: markRaw(assignerNode),
  directReply: markRaw(directReplyNode),
  tool: markRaw(toolNode),
  cycle: markRaw(cycleNode),
}

// 节点类型配置
export const nodeTypeConfigs = {
  start: {
    label: '开始节点',
    description: '流程的起始点',
    color: '#52c41a',
    icon: '▶️',
    category: '基础节点',
    handles: {
      targets: [],
      sources: ['output']
    }
  },
  end: {
    label: '结束节点',
    description: '流程的终止点',
    color: '#ff4d4f',
    icon: '⏹️',
    category: '基础节点',
    handles: {
      targets: ['input'],
      sources: []
    }
  },
    'question-classifier': {
    label: '意图识别',
    description: '识别用户的意图和目标',
    color: '#2f54eb',
    icon: '🔀',
    category: '意图识别',
    handles: {
      targets: ['input'],
      sources: [] // 动态生成，根据分类数量
    }
  },
  llm: {
    label: '文本生成',
    description: '根据用户的意图和目标生成文本',
    color: '#2f54eb',
    icon: '🔀',
    category: '文本生成',
    handles: {
      targets: ['input'],
      sources: [] // 动态生成，根据分类数量
    }
  },
  condition: {
    label: '条件判断',
    description: '条件分支控制',
    color: '#fa8c16',
    icon: '◆',
    category: '流程控制',
    handles: {
      targets: ['input'],
      sources: ['true', 'false']
    }
  },
  api: {
    label: 'API调用',
    description: '外部API接口调用',
    color: '#722ed1',
    icon: '🔗',
    category: '数据处理',
    handles: {
      targets: ['input'],
      sources: ['output']
    }
  },
  chat: {
    label: '对话节点',
    description: '与用户进行多轮对话交互',
    color: '#13c2c2',
    icon: '💬',
    category: 'AI处理',
    handles: {
      targets: ['input'],
      sources: ['output']
    }
  },
  knowledge: {
    label: '知识检索',
    description: '从知识库中检索相关信息',
    color: '#2f54eb',
    icon: '📚',
    category: 'AI处理',
    handles: {
      targets: ['input'],
      sources: ['output']
    }
  },
  questionClassifier: {
    label: '问题分类器',
    description: '根据问题内容自动分类并路由到不同处理节点',
    color: '#fa8c16',
    icon: '🔀',
    category: '流程控制',
    handles: {
      targets: ['input'],
      sources: [] // 动态生成，根据分类数量
    }
  },
  aggregation: {
    label: '聚合',
    description: '将多个文本合并为一个',
    color: '#2f54eb',
    icon: '🔀',
    category: '聚合',
    handles: {
      targets: ['input'],
      sources: [] // 动态生成，根据分类数量
    }
  },
  assigner: {
    label: '变量赋值',
    description: '将文本内容赋值给变量',
    color: '#2f54eb',
    icon: '🔀',
    category: '文本生成',
    handles: {
      targets: ['input'],
      sources: [] // 动态生成，根据分类数量
    }
  },
  directReply: {
    label: '直接回复',
    description: '直接回复的描述',
    color: '#2f54eb',
    icon: '🔀',
    category: '直接回复',
    handles: {
      targets: ['input'],
      sources: [] // 动态生成，根据分类数量
    }
  },
  tool: {
    label: '工具',
    description: '工具的描述',
    color: '#2f54eb',
    icon: '🔀',
    category: '工具',
    handles: {
      targets: ['input'],
      sources: [] // 动态生成，根据分类数量
    }
  },
  cycle: {
    label: '循环',
    description: '循环的描述',
    color: '#2f54eb',
    icon: '🔀',
    category: '循环',
    handles: {
      targets: ['input'],
      sources: [] // 动态生成，根据分类数量
    }
  }
  
}

// 获取节点配置的工具函数
export function getNodeConfig(nodeType: string) {
  return nodeTypeConfigs[nodeType as keyof typeof nodeTypeConfigs]
}

// 获取所有可用的节点类型
export function getAvailableNodeTypes() {
  return Object.keys(nodeTypeConfigs)
}

// 检查节点类型是否有效
export function isValidNodeType(nodeType: string): boolean {
  return nodeType in nodeTypeConfigs
}

// 获取节点的默认数据
export function getDefaultNodeData(nodeType: string) {
  const config = getNodeConfig(nodeType)
  if (!config) return {}
  
  return {
    label: config.label,
    description: config.description,
    color: config.color,
    icon: config.icon,
    category: config.category,
    status: 'idle',
    config: {}
  }
}
