<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import Icon403 from '@/icons/403.vue'

const isVisible = ref(false)

// 添加进入动画
onMounted(() => {
  setTimeout(() => {
    isVisible.value = true
  }, 100)
})
</script>

<template>
  <div class="error-container">
    <div class="error-content" :class="{ 'fade-in': isVisible }">
      <!-- 未授权图标显示 -->
      <div class="error-icon">
        <Icon403 />
      </div>

      <!-- 错误信息 -->
      <div class="error-info">
        <h1 class="error-title">未授权访问</h1>
        <p class="error-description">
          抱歉，您没有权限访问此页面。
          <br>
          请检查您的账户权限，或联系系统管理员。
        </p>
      </div>

      <!-- 装饰元素 -->
      <div class="decoration">
        <div class="floating-shape shape-1"></div>
        <div class="floating-shape shape-2"></div>
        <div class="floating-shape shape-3"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.error-content {
  text-align: center;
  max-width: 600px;
  width: 100%;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.error-content.fade-in {
  opacity: 1;
  transform: translateY(0);
}

/* 未授权图标样式 */
.error-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
  animation: bounce 2s infinite;
}

.error-icon :deep(svg) {
  width: 120px;
  height: 120px;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 错误信息样式 */
.error-info {
  margin-bottom: 50px;
}

.error-title {
  font-size: 32px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  text-shadow: none;
}

.error-description {
  font-size: 16px;
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

/* 装饰元素 */
.decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.05);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
  background: rgba(59, 130, 246, 0.08);
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
  background: rgba(16, 185, 129, 0.06);
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
  background: rgba(245, 158, 11, 0.07);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-icon :deep(svg) {
    width: 80px;
    height: 80px;
  }

  .error-title {
    font-size: 24px;
  }

  .error-description {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .error-container {
    padding: 15px;
  }

  .error-icon :deep(svg) {
    width: 60px;
    height: 60px;
  }

  .error-title {
    font-size: 20px;
  }

  .floating-shape {
    display: none;
  }
}
</style>