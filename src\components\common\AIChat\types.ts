// AI对话组件类型定义
export interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: number
  loading?: boolean
  error?: boolean
  thinking?: string
  attachments?: Attachment[]
  feedback?: MessageFeedback
  // 流程阶段支持
  stages?: MessageStage[]
  currentStage?: string
  isThinkingComplete?: boolean
  isGenerating?: boolean
  // 兼容原有chat组件字段
  dateTime?: string
  text?: string
  inversion?: boolean
  conversationOptions?: ConversationRequest | null
  requestOptions?: { prompt: string; options?: ConversationRequest | null }
  // 扩展字段
  conversationId?: string
  category?: string
  agentId?: string
  modelId?: string
  answerList?: any[]
  endstatus?: number
  annotation?: any
  questionArr?: any[]
  problem?: boolean
  questionState?: number
}

export interface MessageStage {
  id: string
  name: string
  status: 'pending' | 'running' | 'complete' | 'error'
  content?: string
  startTime?: number
  endTime?: number
  isCollapsed?: boolean
}

export interface Attachment {
  id: string
  name: string
  type: 'image' | 'document' | 'video' | 'audio'
  url: string
  size: number
}

export interface MessageFeedback {
  type: 'like' | 'dislike' | null
  reason?: string
  category?: string
}

export interface QuickPrompt {
  id: string
  text: string
  category?: string
}

export interface KnowledgeBase {
  id: string
  name: string
  description?: string
  icon?: string
}

export interface AIChatConfig {
  // 界面配置
  theme?: 'light' | 'dark' | 'auto'
  layout?: 'desktop' | 'mobile' | 'responsive'
  
  // 功能开关
  features?: {
    header?: boolean
    feedback?: boolean
    voiceInput?: boolean
    fileUpload?: boolean
    knowledgeBase?: boolean
    workflow?: boolean
    thinking?: boolean
    messageEdit?: boolean
    stopGeneration?: boolean
    // 新增功能开关
    copy?: boolean
    regenerate?: boolean
    export?: boolean
    quickPrompts?: boolean
    streaming?: boolean
    audio?: boolean
    translation?: boolean
    search?: boolean
    annotation?: boolean
    multiTurn?: boolean
    contextMemory?: boolean
  }
  
  // 样式定制
  styles?: {
    primaryColor?: string
    backgroundColor?: string
    messageStyle?: 'bubble' | 'card'
    aiMessageMode?: 'bubble' | 'flat'  // AI消息展示模式：气泡模式或平铺模式
    maxWidth?: string | number
    maxHeight?: string | number
    // 新增样式配置
    avatarStyle?: 'circle' | 'square'
    colorScheme?: string
    fontSize?: 'small' | 'medium' | 'large'
    messageSpacing?: 'compact' | 'normal' | 'loose'
    borderRadius?: 'none' | 'small' | 'medium' | 'large'
    animation?: 'none' | 'fade' | 'slide' | 'bounce'
  }
  
  // 业务配置
  api?: {
    chatEndpoint?: string
    uploadEndpoint?: string
    feedbackEndpoint?: string
    // 新增API配置
    baseUrl?: string
    voiceEndpoint?: string
    audioEndpoint?: string
    translationEndpoint?: string
    headers?: Record<string, string>
    timeout?: number
  }

  // 占位符文本
  placeholders?: {
    input?: string
    thinking?: string
    // 新增占位符
    uploading?: string
    recording?: string
    generating?: string
  }

  // 欢迎提示配置（猜你想问）
  welcomePrompts?: {
    enabled?: boolean
    title?: string
    prompts?: Array<{
      id: string
      text: string
      description?: string
    }>
  }

  // 行为配置
  behavior?: {
    autoScroll?: boolean
    showTimestamp?: boolean
    enableMarkdown?: boolean
    maxMessages?: number
    typingSpeed?: number
    // 新增行为配置
    autoSave?: boolean
    contextLength?: number
    retryAttempts?: number
    debounceDelay?: number
  }

  // 兼容原有chat组件配置
  compatibility?: {
    useOriginalAPI?: boolean
    enableLegacyMode?: boolean
    conversationId?: string
    category?: string
    agentId?: string
    modelId?: string
    modelTemp?: number
    maxLength?: number
    promptTemplate?: string
    multiTurnFlag?: boolean
  }
}

export interface ChatEvents {
  onSendMessage: (content: string, attachments?: Attachment[]) => void
  onFeedback: (messageId: string, feedback: MessageFeedback) => void
  onEditMessage: (messageId: string, newContent: string) => void
  onStopGeneration: () => void
  onUploadFile: (file: File) => Promise<Attachment>
  onSelectKnowledgeBase: (knowledgeBase: KnowledgeBase) => void
}

export interface WorkflowNode {
  id: string
  title: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  description?: string
  result?: string
}

export interface StreamResponse {
  content: string
  thinking?: string
  finished: boolean
  workflow?: WorkflowNode[]
  // 兼容原有响应格式
  text?: string
  answerList?: any[]
  endstatus?: number
  error?: boolean
  conversationId?: string
  done?: boolean
  stage?: string
}

// 兼容原有chat组件的请求接口
export interface ConversationRequest {
  conversationId?: string
  parentMessageId?: string
}

// 扩展的API请求参数
export interface ChatAPIParams {
  // 基础参数
  question?: string
  prompt?: string
  content?: string

  // 会话参数
  conversationId?: string
  parentMessageId?: string

  // 模型参数
  category?: string
  agentId?: string
  modelId?: string
  modelTemp?: number
  maxLength?: number
  promptTemplate?: string
  multiTurnFlag?: boolean

  // 控制参数
  signal?: AbortSignal
  onDownloadProgress?: (progressEvent: any) => void

  // 扩展参数
  attachments?: Attachment[]
  knowledgeBaseId?: string
  context?: any[]
  metadata?: Record<string, any>
}

// 音频相关接口
export interface AudioConfig {
  sampleRate?: number
  channels?: number
  bitDepth?: number
  format?: 'wav' | 'mp3' | 'pcm'
}

export interface VoiceInputConfig {
  language?: string
  continuous?: boolean
  interimResults?: boolean
  maxAlternatives?: number
}

// 文件上传配置
export interface UploadConfig {
  maxSize?: number
  allowedTypes?: string[]
  multiple?: boolean
  compress?: boolean
}

// 扩展的事件接口
export interface ExtendedChatEvents extends ChatEvents {
  onRegenerate?: (messageId: string) => void
  onCopy?: (content: string) => void
  onExport?: (format: 'json' | 'markdown' | 'txt') => void
  onVoiceStart?: () => void
  onVoiceEnd?: (text: string) => void
  onAudioPlay?: (messageId: string) => void
  onTranslate?: (messageId: string, targetLang: string) => void
  onAnnotate?: (messageId: string, annotation: any) => void
}
