import type { AxiosProgressEvent, GenericAbortSignal } from 'axios'

import { get, post } from '@/utils/request'
//上传流程接口
export function uploadApiProcess<T = any>(
  params: {
    signal?: GenericAbortSignal
    fileUrl: any
    fileId: any
    code: string
    knowledgeBaseId: string
    onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
  },
) {
  const data: Record<string, any> = {
    fileUrl: params.fileUrl,
    fileId: params.fileId,
    code:params.code,
    knowledgeBaseId:params.knowledgeBaseId,
    subjectId:"123456"
  }
  return post<T>({
    url: '/emind/assistant/file_analysis',
    data,
    signal: params.signal,
    onDownloadProgress: params.onDownloadProgress,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
