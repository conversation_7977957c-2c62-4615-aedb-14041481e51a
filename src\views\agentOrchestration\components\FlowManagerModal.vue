<template>
  <n-modal v-model:show="visible">
    <n-card 
      style="width: 800px; max-height: 600px;" 
      title="流程管理" 
      :bordered="false" 
      size="huge"
      closable
      @close="handleClose"
    >
      <div class="flow-manager">
        <!-- 搜索栏 -->
        <div class="search-bar mb-4">
          <n-input 
            v-model:value="searchValue" 
            placeholder="搜索流程名称" 
            clearable
          >
            <template #prefix>
              <!-- <n-icon><SearchOutline /></n-icon> -->
            </template>
          </n-input>
        </div>

        <!-- 流程列表 -->
        <div class="flow-list">
          <div v-if="filteredFlows.length === 0" class="empty-state text-center py-8">
            <div class="text-4xl text-gray-300 mb-4">📁</div>
            <p class="text-gray-500">暂无流程</p>
            <p class="text-sm text-gray-400 mt-2">创建您的第一个智能体编排流程</p>
          </div>
          
          <div v-else class="flow-items space-y-3">
            <div 
              v-for="flow in filteredFlows" 
              :key="flow.id"
              class="flow-item p-4 bg-gray-50 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
            >
              <div class="flex items-center justify-between">
                <div class="flow-info flex-1">
                  <div class="flex items-center mb-2">
                    <h4 class="flow-name text-lg font-medium text-gray-800 mr-3">
                      {{ flow.name }}
                    </h4>
                    <n-tag 
                      :type="flow.id === currentFlowId ? 'primary' : 'default'" 
                      size="small"
                    >
                      {{ flow.id === currentFlowId ? '当前' : '已保存' }}
                    </n-tag>
                  </div>
                  
                  <p class="flow-description text-sm text-gray-600 mb-2" v-if="flow.description">
                    {{ flow.description }}
                  </p>
                  
                  <div class="flow-meta flex items-center space-x-4 text-xs text-gray-500">
                    <span>节点: {{ flow.nodes.length }}</span>
                    <span>连接: {{ flow.edges.length }}</span>
                    <span>创建: {{ formatDate(flow.createdAt) }}</span>
                    <span>更新: {{ formatDate(flow.updatedAt) }}</span>
                  </div>
                </div>
                
                <div class="flow-actions flex items-center space-x-2">
                  <n-button
                    size="small"
                    type="primary"
                    @click="handleLoadFlow(flow.id)"
                    :disabled="flow.id === currentFlowId"
                  >
                    📂 加载
                  </n-button>
                  
                  <n-button
                    size="small"
                    @click="handleRenameFlow(flow)"
                  >
                    ✏️ 重命名
                  </n-button>

                  <n-button
                    size="small"
                    @click="handleDuplicateFlow(flow)"
                  >
                    📋 复制
                  </n-button>

                  <n-button
                    size="small"
                    type="error"
                    @click="handleDeleteFlow(flow)"
                    :disabled="flow.id === currentFlowId"
                  >
                    🗑️ 删除
                  </n-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between">
          <div class="text-sm text-gray-500">
            共 {{ flows.length }} 个流程
          </div>
          <n-button @click="handleClose">关闭</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>

  <!-- 重命名弹窗 -->
  <n-modal v-model:show="showRenameModal">
    <n-card style="width: 400px" title="重命名流程" :bordered="false" size="huge">
      <n-form ref="renameFormRef" :model="renameForm" :rules="renameRules">
        <n-form-item label="流程名称" path="name">
          <n-input v-model:value="renameForm.name" placeholder="请输入新的流程名称" />
        </n-form-item>
        <n-form-item label="流程描述" path="description">
          <n-input 
            v-model:value="renameForm.description" 
            type="textarea" 
            placeholder="请输入流程描述（可选）"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      
      <template #footer>
        <div class="flex justify-end space-x-2">
          <n-button @click="showRenameModal = false">取消</n-button>
          <n-button type="primary" @click="handleConfirmRename">确定</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  NModal, 
  NCard, 
  NInput, 
  NButton, 
  NIcon, 
  NTag,
  NForm,
  NFormItem,
  useDialog,
  useMessage 
} from 'naive-ui'
// 使用简单的文本图标替代，避免依赖问题
import { useOrchestrationStore } from '@/store'
import type { FlowData } from '@/store/modules/orchestration'

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  'update:show': [value: boolean]
  loadFlow: [flowId: string]
  deleteFlow: [flowId: string]
}>()

const orchestrationStore = useOrchestrationStore()
const dialog = useDialog()
const message = useMessage()

const searchValue = ref('')
const showRenameModal = ref(false)
const renameFormRef = ref()
const renameForm = ref({
  name: '',
  description: ''
})
const currentRenameFlow = ref<FlowData | null>(null)

const renameRules = {
  name: [
    { required: true, message: '请输入流程名称', trigger: 'blur' }
  ]
}

// 双向绑定
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 计算属性
const flows = computed(() => orchestrationStore.flows)
const currentFlowId = computed(() => orchestrationStore.currentFlow?.id)

const filteredFlows = computed(() => {
  if (!searchValue.value) {
    return flows.value
  }
  return flows.value.filter(flow => 
    flow.name.toLowerCase().includes(searchValue.value.toLowerCase()) ||
    (flow.description && flow.description.toLowerCase().includes(searchValue.value.toLowerCase()))
  )
})

// 方法
const handleClose = () => {
  visible.value = false
  searchValue.value = ''
}

const handleLoadFlow = (flowId: string) => {
  emit('loadFlow', flowId)
}

const handleDeleteFlow = (flow: FlowData) => {
  dialog.warning({
    title: '删除确认',
    content: `确定要删除流程 "${flow.name}" 吗？此操作不可撤销。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: () => {
      emit('deleteFlow', flow.id)
    }
  })
}

const handleRenameFlow = (flow: FlowData) => {
  currentRenameFlow.value = flow
  renameForm.value = {
    name: flow.name,
    description: flow.description || ''
  }
  showRenameModal.value = true
}

const handleConfirmRename = async () => {
  try {
    await renameFormRef.value?.validate()
    
    if (currentRenameFlow.value) {
      // 更新流程信息
      const updatedFlow = {
        ...currentRenameFlow.value,
        name: renameForm.value.name,
        description: renameForm.value.description,
        updatedAt: new Date().toISOString()
      }
      
      // 更新store中的流程
      const flowIndex = orchestrationStore.flows.findIndex(f => f.id === currentRenameFlow.value!.id)
      if (flowIndex !== -1) {
        orchestrationStore.flows[flowIndex] = updatedFlow
        
        // 如果是当前流程，也要更新当前流程
        if (orchestrationStore.currentFlow?.id === currentRenameFlow.value.id) {
          orchestrationStore.currentFlow = updatedFlow
        }
        
        orchestrationStore.recordState()
        message.success('流程重命名成功')
      }
    }
    
    showRenameModal.value = false
    currentRenameFlow.value = null
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleDuplicateFlow = (flow: FlowData) => {
  const duplicatedFlow = {
    ...flow,
    id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    name: `${flow.name} (副本)`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  orchestrationStore.flows.push(duplicatedFlow)
  orchestrationStore.recordState()
  message.success('流程复制成功')
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped lang="less">
.flow-manager {
  .search-bar {
    .n-input {
      border-radius: 8px;
    }
  }
  
  .flow-list {
    max-height: 400px;
    overflow-y: auto;
    
    .flow-item {
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
    
    .flow-name {
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .flow-description {
      max-width: 400px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .empty-state {
    padding: 40px 20px;
  }
}

:deep(.n-card-header) {
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.n-button) {
  border-radius: 6px;
}
</style>
