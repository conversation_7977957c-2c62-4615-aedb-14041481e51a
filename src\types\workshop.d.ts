// 智能体工坊相关类型定义
export interface AgentItem {
  id: string
  name: string
  description: string
  buildCategory: number
  scenceCategory: string
  status: number
  icon: string
  iconUrl?: string
  agentKnowledgeNum: number
}

export interface ApiResponse<T = any> {
  code: string
  message: string
  data: T
}

export interface AgentListResponse {
  items: AgentItem[]
  total: number
}

export interface ModelForm {
  buildCategory: string
  scenceCategory: string | null
  name?: string
  description?: string
  id?: string
}

export interface IssueContent {
  jurisdiction: string
  note?: string
  apiVisitStatus?: string
  publicVisitStatus?: string
}

export interface MenuOption {
  label: string
  value: string
  disabled: boolean
}