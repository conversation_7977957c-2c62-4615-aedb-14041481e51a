import { get, post } from '@/utils/request'

// 提示词管理相关接口

// 获取提示词列表
export function getPromptList(params: {
    page_size: number;
    page_num: number;
    name?: string;
    status?: string;
    permission?: string;
}) {
    return get({
        url: '/emind/prompt',
        data: params,
    })
}

// 获取我的提示词列表
export function getMyPromptList(data: any) {
    return post({
        url: '/prompt/my/list',
        data,
    })
}

// 获取平台提示词列表
export function getPlatformPromptList(data: any) {
    return post({
        url: '/prompt/platform/list',
        data,
    })
}

// 创建提示词
export function createPrompt(data: { name: string; content?: string }) {
    return post({
        url: '/emind/prompt',
        data: {
            name: data.name,
            status: "0",
            agentId: "1950834085600997400",
            permission: 'user',
            content: data.content || ''
        },
    })
}

// 获取提示词详情
export function getPromptDetail(id: string) {
    return get({
        url: `/emind/prompt/${id}`,
    })
}

// 更新提示词
export function updatePrompt(data: any) {
    return post({
        url: '/prompt/update',
        data,
    })
}

// 删除提示词
export function deletePrompt(id: string) {
    return post({
        url: `/emind/prompt/${id}/delete`,
        data: {},
    })
}

// 复制平台提示词到我的提示词
export function copyPromptToMy(id: string) {
    return post({
        url: `/prompt/copy/${id}`,
    })
}

// 保存草稿
export function saveDraft(id: string, data: {
    content?: string;
    name?: string;
    status: number;
}) {
    return post({
        url: `/emind/prompt/${id}/update`,
        data,
    })
}

// 获取版本列表
export function getVersionList(promptId: string) {
    return get({
        url: '/emind/promptHistoryVersion/all',
        data: {
            promptId,
            ascs: 'version'
        }
    })
}

// 发布提示词
export function releasePrompt(data: {
    id: string;
    name: string;
    content: string;
    isPublishingPlatform: boolean;
    versionDescription: string;
}) {
    return post({
        url: '/emind/prompt/release',
        data,
    })
}
