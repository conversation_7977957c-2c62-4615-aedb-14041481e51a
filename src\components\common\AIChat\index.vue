<template>
  <div class="ai-chat-container" :class="containerClass" :style="containerStyle">
    <!-- 头部 -->
    <div v-if="config.features?.header !== false" class="chat-header">
      <div class="header-info">
        <h3 class="chat-title">{{ title || 'AI助手' }}</h3>
        <p v-if="subtitle" class="chat-subtitle">{{ subtitle }}</p>
      </div>
      <div class="header-actions">
        <button v-if="config.features?.clear" class="header-btn" @click="handleClear" title="清空对话">
          <img src="/src/assets/chat/delIcon.png" alt="清空对话" class="header-icon" />
        </button>
        <button v-if="config.features?.export" class="header-btn" @click="handleExport" title="导出对话">
          <img src="/src/assets/export.png" alt="导出对话" class="header-icon" />
        </button>
      </div>
    </div>

    <!-- 消息列表 -->
    <div ref="messagesContainer" class="messages-container">
      <!-- 欢迎消息 -->
      <div v-if="!messages.length && welcomeMessage" class="welcome-message">
        <div class="welcome-avatar">
          <img :src="config.aiAvatar || defaultAiAvatar" alt="AI" />
        </div>
        <div class="welcome-content">
          <h4>{{ welcomeMessage.title || '你好！' }}</h4>
          <p>{{ welcomeMessage.content || '我是AI助手，有什么可以帮助您的吗？' }}</p>
        </div>
      </div>

      <!-- 欢迎提示块（猜你想问） -->
      <div v-if="!messages.length && config.welcomePrompts?.enabled && config.welcomePrompts?.prompts?.length" class="welcome-prompts">
        <div class="welcome-prompts-header">
          <h5>{{ config.welcomePrompts.title || '猜你想问' }}</h5>
        </div>
        <div class="welcome-prompts-grid">
          <button
            v-for="prompt in config.welcomePrompts.prompts"
            :key="prompt.id"
            class="welcome-prompt-item"
            @click="handleWelcomePrompt(prompt.text)"
          >
            <div class="prompt-text">{{ prompt.text }}</div>
            <div v-if="prompt.description" class="prompt-description">{{ prompt.description }}</div>
          </button>
        </div>
      </div>

      <!-- 消息列表 -->
      <div v-for="message in messages" :key="message.id" class="message-wrapper">
        <MessageItem
          :message="message"
          :ai-avatar="config.aiAvatar || defaultAiAvatar"
          :user-avatar="config.userAvatar || defaultUserAvatar"
          :ai-message-mode="config.styles?.aiMessageMode || 'bubble'"
          @feedback="handleFeedback"
          @copy="handleCopy"
          @regenerate="handleRegenerate"
          @edit="handleEditMessage"
        >
          <!-- 传递操作按钮插槽 -->
          <template #message-actions="{ message, handleFeedback, handleCopy, handleRegenerate }">
            <slot
              name="message-actions"
              :message="message"
              :handleFeedback="handleFeedback"
              :handleCopy="handleCopy"
              :handleRegenerate="handleRegenerate"
            />
          </template>

          <!-- 传递用户消息额外内容插槽 -->
          <template #user-message-extra="{ message, startEdit, showEdit }">
            <slot
              name="user-message-extra"
              :message="message"
              :startEdit="startEdit"
              :showEdit="showEdit"
            />
          </template>
        </MessageItem>
      </div>

      <!-- 工作流模式 -->
      <div v-if="workflowNodes?.length" class="workflow-container">
        <h4>任务执行进度</h4>
        <div class="workflow-nodes">
          <div 
            v-for="node in workflowNodes" 
            :key="node.id" 
            class="workflow-node"
            :class="node.status"
          >
            <div class="node-icon">
              <span v-if="node.status === 'completed'">✅</span>
              <span v-else-if="node.status === 'failed'">❌</span>
              <span v-else-if="node.status === 'running'" class="loading-spinner">⏳</span>
              <span v-else>⏸️</span>
            </div>
            <div class="node-content">
              <div class="node-title">{{ node.title }}</div>
              <div v-if="node.description" class="node-description">{{ node.description }}</div>
              <div v-if="node.result" class="node-result">{{ node.result }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载指示器 -->
      <div v-if="isLoading" class="loading-message">
        <div class="loading-avatar">
          <img :src="config.aiAvatar || defaultAiAvatar" alt="AI" />
        </div>
        <div class="loading-content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <p class="loading-text">{{ loadingText || 'AI正在思考中...' }}</p>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <InputArea
      :config="config"
      :quick-prompts="quickPrompts"
      :knowledge-bases="knowledgeBases"
      :selected-knowledge-base="selectedKnowledgeBase"
      :attachments="attachments"
      :is-generating="isGenerating"
      :has-messages="messages.length > 0"
      :prompt-position="promptPosition"
      @send="handleSendMessage"
      @upload-file="handleUploadFile"
      @select-knowledge-base="handleSelectKnowledgeBase"
      @remove-knowledge-base="handleRemoveKnowledgeBase"
      @remove-attachment="handleRemoveAttachment"
      @stop-generation="handleStopGeneration"
      @voice-input="handleVoiceInput"
    >
      <!-- 传递输入工具插槽 -->
      <template #input-tools="{ config, triggerFileUpload, showKnowledgeSelector }">
        <slot
          name="input-tools"
          :config="config"
          :triggerFileUpload="triggerFileUpload"
          :showKnowledgeSelector="showKnowledgeSelector"
        />
      </template>
    </InputArea>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch, onMounted } from 'vue'
import MessageItem from './components/MessageItem.vue'
import InputArea from './components/InputArea.vue'
import type {
  Message,
  AIChatConfig,
  QuickPrompt,
  KnowledgeBase,
  Attachment,
  MessageFeedback,
  WorkflowNode
} from './types'

interface Props {
  // 基础配置
  config?: AIChatConfig
  title?: string
  subtitle?: string

  // 消息数据
  messages?: Message[]

  // 功能数据
  quickPrompts?: QuickPrompt[]
  knowledgeBases?: KnowledgeBase[]

  // 状态
  isLoading?: boolean
  isGenerating?: boolean
  loadingText?: string

  // 工作流
  workflowNodes?: WorkflowNode[]

  // 欢迎消息
  welcomeMessage?: {
    title?: string
    content?: string
  }

  // UI配置
  promptPosition?: 'top' | 'bottom'

  // 扩展配置
  // 兼容原有chat组件
  conversationId?: string
  category?: string
  agentId?: string
  modelId?: string
  modelTemp?: number
  maxLength?: number
  promptTemplate?: string
  multiTurnFlag?: boolean

  // 头像配置
  aiAvatar?: string
  userAvatar?: string

  // 主题配置
  theme?: 'light' | 'dark' | 'auto'
  primaryColor?: string
  backgroundColor?: string
  maxWidth?: string | number
  maxHeight?: string | number

  // 行为配置
  autoScroll?: boolean
  showTimestamp?: boolean
  enableMarkdown?: boolean
  maxMessages?: number
  typingSpeed?: number

  // 功能开关
  enableCopy?: boolean
  enableRegenerate?: boolean
  enableEdit?: boolean
  enableExport?: boolean
  enableVoice?: boolean
  enableUpload?: boolean
  enableFeedback?: boolean

  // 占位符文本
  inputPlaceholder?: string
  thinkingPlaceholder?: string
  uploadingPlaceholder?: string
  recordingPlaceholder?: string

  // 事件回调
  onApiCall?: (params: any) => Promise<any>
  onError?: (error: Error) => void
  onSuccess?: (response: any) => void
}

interface Emits {
  (e: 'send-message', content: string, attachments?: Attachment[]): void
  (e: 'feedback', messageId: string, feedback: MessageFeedback): void
  (e: 'edit-message', messageId: string, content: string): void
  (e: 'regenerate', messageId: string): void
  (e: 'upload-file', file: File): Promise<Attachment>
  (e: 'select-knowledge-base', kb: KnowledgeBase): void
  (e: 'stop-generation'): void
  (e: 'clear-chat'): void
  (e: 'export-chat'): void
  (e: 'voice-input', text: string): void

  // 扩展事件
  (e: 'copy', content: string): void
  (e: 'translate', messageId: string, targetLang: string): void
  (e: 'annotate', messageId: string, annotation: any): void
  (e: 'audio-play', messageId: string): void
  (e: 'voice-start'): void
  (e: 'voice-end', text: string): void
  (e: 'message-added', message: Message): void
  (e: 'message-updated', messageId: string, updates: Partial<Message>): void
  (e: 'message-removed', messageId: string): void
  (e: 'conversation-started', conversationId: string): void
  (e: 'conversation-ended', conversationId: string): void
  (e: 'error', error: Error): void
  (e: 'success', response: any): void
  (e: 'loading-start'): void
  (e: 'loading-end'): void
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({}),
  messages: () => [],
  quickPrompts: () => [],
  knowledgeBases: () => [],
  isLoading: false,
  isGenerating: false,
  workflowNodes: () => [],
  promptPosition: 'top',
  // 扩展默认值
  theme: 'light',
  maxWidth: '100%',
  maxHeight: '80vh',
  autoScroll: true,
  showTimestamp: true,
  enableMarkdown: true,
  maxMessages: 100,
  typingSpeed: 50,
  enableCopy: true,
  enableRegenerate: true,
  enableEdit: true,
  enableExport: true,
  enableVoice: true,
  enableUpload: true,
  enableFeedback: true,
  inputPlaceholder: '请输入您的问题...',
  thinkingPlaceholder: '正在思考中...',
  uploadingPlaceholder: '正在上传文件...',
  recordingPlaceholder: '正在录音...'
})

const emit = defineEmits<Emits>()

// 默认头像
const defaultAiAvatar = '/src/assets/msgHead.png'
const defaultUserAvatar = '/src/assets/usericon.png'

// 内部状态
const messagesContainer = ref<HTMLElement>()
const selectedKnowledgeBase = ref<KnowledgeBase | null>(null)
const attachments = ref<Attachment[]>([])

// 计算属性
const containerClass = computed(() => {
  return [
    `theme-${props.config.theme || 'light'}`,
    `layout-${props.config.layout || 'responsive'}`,
    props.config.styles?.messageStyle === 'card' ? 'message-style-card' : 'message-style-bubble'
  ]
})

// 动态样式计算
const containerStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.config.styles?.maxWidth) {
    style.maxWidth = typeof props.config.styles.maxWidth === 'number' ? `${props.config.styles.maxWidth}px` : props.config.styles.maxWidth
  }

  if (props.config.styles?.maxHeight) {
    style.maxHeight = typeof props.config.styles.maxHeight === 'number' ? `${props.config.styles.maxHeight}px` : props.config.styles.maxHeight
  }

  if (props.config.styles?.primaryColor) {
    style['--primary-color'] = props.config.styles.primaryColor
  }

  if (props.config.styles?.backgroundColor) {
    style['--background-color'] = props.config.styles.backgroundColor
  }

  return style
})

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      // 使用smooth滚动，提供更好的用户体验
      messagesContainer.value.scrollTo({
        top: messagesContainer.value.scrollHeight,
        behavior: 'smooth'
      })
    }
  })
}

// 监听消息变化，自动滚动
watch(() => props.messages, () => {
  scrollToBottom()
}, { deep: true, immediate: true })

// 监听消息内容变化，确保流式更新时也能滚动
watch(() => props.messages.map(m => m.content).join(''), () => {
  scrollToBottom()
}, { immediate: true })

// 事件处理
const handleSendMessage = (content: string) => {
  emit('send-message', content, attachments.value.length > 0 ? [...attachments.value] : undefined)
  // 清空附件
  attachments.value = []
}

const handleFeedback = (messageId: string, feedback: MessageFeedback) => {
  emit('feedback', messageId, feedback)
}

const handleCopy = async (content: string) => {
  try {
    // 清理内容，移除HTML标签
    const cleanContent = content.replace(/<[^>]*>/g, '').trim()

    if (navigator.clipboard && window.isSecureContext) {
      // 现代浏览器的clipboard API
      await navigator.clipboard.writeText(cleanContent)
    } else {
      // 降级方案：使用传统的document.execCommand
      const textArea = document.createElement('textarea')
      textArea.value = cleanContent
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
    }

    console.log('复制成功')
    // 可以在这里添加成功提示，比如toast
  } catch (err) {
    console.error('复制失败:', err)
    // 可以在这里添加失败提示
  }
}

const handleRegenerate = (messageId: string) => {
  // 向上传递重新生成事件，由父组件处理具体逻辑
  emit('regenerate', messageId)
}

const handleEditMessage = (messageId: string, content: string) => {
  emit('edit-message', messageId, content)
}

// 处理欢迎提示点击
const handleWelcomePrompt = (promptText: string) => {
  // 发送欢迎提示作为用户消息
  emit('send-message', promptText)
}

const handleUploadFile = async (file: File) => {
  try {
    const attachment = await emit('upload-file', file)
    attachments.value.push(attachment)
  } catch (error) {
    console.error('文件上传失败:', error)
  }
}

const handleSelectKnowledgeBase = (kb: KnowledgeBase) => {
  selectedKnowledgeBase.value = kb
  emit('select-knowledge-base', kb)
}

const handleRemoveKnowledgeBase = () => {
  selectedKnowledgeBase.value = null
}

const handleRemoveAttachment = (id: string) => {
  attachments.value = attachments.value.filter(att => att.id !== id)
}

const handleStopGeneration = () => {
  emit('stop-generation')
}

const handleClear = () => {
  emit('clear-chat')
}

const handleExport = () => {
  emit('export-chat')
}

const handleVoiceInput = (text: string) => {
  emit('voice-input', text)
}

onMounted(() => {
  scrollToBottom()
})
</script>

<style scoped>
.ai-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 80vh; /* 设置最大高度，防止内容区域把输入框顶没 */
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(18, 94, 255, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04),
    0 2px 8px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(18, 94, 255, 0.08);
  position: relative;
}

.ai-chat-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(18, 94, 255, 0.2) 50%, transparent 100%);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px;
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  border-bottom: 1px solid rgba(18, 94, 255, 0.08);
  backdrop-filter: blur(20px);
  position: relative;
}

.chat-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 28px;
  right: 28px;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(18, 94, 255, 0.1) 50%, transparent 100%);
}

.header-info {
  flex: 1;
}

.chat-title {
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  color: #1f2937;
  letter-spacing: -0.03em;
  background: linear-gradient(135deg, #1f2937 0%, #125EFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.chat-subtitle {
  margin: 8px 0 0 0;
  font-size: 15px;
  color: #6b7280;
  font-weight: 500;
  opacity: 0.8;
}

.header-actions {
  display: flex;
  gap: 16px;
}

.header-btn {
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  border: 1px solid rgba(18, 94, 255, 0.15);
  border-radius: 14px;
  padding: 12px 14px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #125EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  height: 44px;
  position: relative;
  overflow: hidden;
}

.header-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.05) 0%, rgba(18, 94, 255, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.header-btn:hover {
  background: linear-gradient(135deg, #eef4ff 0%, #dbeafe 100%);
  border-color: rgba(18, 94, 255, 0.25);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(18, 94, 255, 0.2),
    0 4px 12px rgba(18, 94, 255, 0.1);
}

.header-btn:hover::before {
  opacity: 1;
}

.header-btn:active {
  transform: translateY(-1px);
  box-shadow:
    0 4px 16px rgba(18, 94, 255, 0.15),
    0 2px 8px rgba(18, 94, 255, 0.08);
}

.header-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(97%) contrast(101%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.header-btn:hover .header-icon {
  filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(120%) contrast(101%);
  transform: scale(1.15);
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 28px 32px;
  scroll-behavior: smooth;
  background: linear-gradient(135deg, #fafbfc 0%, #f8faff 100%);
  position: relative;
}

.messages-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 32px;
  right: 32px;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(18, 94, 255, 0.06) 50%, transparent 100%);
}

/* 自定义滚动条 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(18, 94, 255, 0.04);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.2) 0%, rgba(18, 94, 255, 0.3) 100%);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.3) 0%, rgba(18, 94, 255, 0.4) 100%);
}

.welcome-message {
  display: flex;
  gap: 20px;
  margin-bottom: 36px;
  padding: 28px 32px;
  background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
  border-radius: 20px;
  border: 1px solid rgba(18, 94, 255, 0.08);
  box-shadow:
    0 8px 32px rgba(18, 94, 255, 0.06),
    0 4px 16px rgba(0, 0, 0, 0.02),
    0 2px 8px rgba(0, 0, 0, 0.01);
  position: relative;
  overflow: hidden;
  animation: welcomeSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.welcome-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #125EFF 0%, #1e7fff 50%, #3b82f6 100%);
}

.welcome-message::after {
  content: '';
  position: absolute;
  top: 4px;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.02) 0%, transparent 100%);
  pointer-events: none;
}

@keyframes welcomeSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-avatar {
  flex-shrink: 0;
  position: relative;
}

.welcome-avatar img {
  width: 52px;
  height: 52px;
  border-radius: 50%;
  box-shadow:
    0 4px 16px rgba(18, 94, 255, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.05);
  border: 3px solid rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.welcome-avatar::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(18, 94, 255, 0.2) 0%, rgba(18, 94, 255, 0.1) 100%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.welcome-message:hover .welcome-avatar::after {
  opacity: 1;
}

.welcome-content {
  flex: 1;
  z-index: 1;
}

.welcome-content h4 {
  margin: 0 0 14px 0;
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
  letter-spacing: -0.03em;
  background: linear-gradient(135deg, #1f2937 0%, #125EFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-content p {
  margin: 0;
  font-size: 16px;
  color: #6b7280;
  line-height: 1.7;
  font-weight: 500;
  opacity: 0.9;
}

/* 欢迎提示块样式（猜你想问） */
.welcome-prompts {
  margin: 20px 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  border: 1px solid rgba(18, 94, 255, 0.08);
  border-radius: 16px;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.02),
    0 4px 12px rgba(18, 94, 255, 0.04);
}

.welcome-prompts-header {
  margin-bottom: 16px;
}

.welcome-prompts-header h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.welcome-prompts-header h5::before {
  content: '💡';
  font-size: 18px;
}

.welcome-prompts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.welcome-prompt-item {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border: 1px solid rgba(18, 94, 255, 0.12);
  border-radius: 12px;
  padding: 16px;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.welcome-prompt-item:hover {
  background: linear-gradient(135deg, #f8faff 0%, #eef4ff 100%);
  border-color: rgba(18, 94, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(18, 94, 255, 0.15);
}

.welcome-prompt-item:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(18, 94, 255, 0.2);
}

.prompt-text {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.4;
  margin-bottom: 4px;
}

.prompt-description {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.3;
}

.message-wrapper {
  margin-bottom: 20px;
}

.workflow-container {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.workflow-container h4 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  letter-spacing: -0.02em;
}

.workflow-nodes {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.workflow-node {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border: 1px solid rgba(18, 94, 255, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.workflow-node.completed {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-color: rgba(34, 197, 94, 0.2);
}

.workflow-node.failed {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-color: rgba(239, 68, 68, 0.2);
}

.workflow-node.running {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: rgba(59, 130, 246, 0.2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(59, 130, 246, 0);
  }
}

.node-icon {
  flex-shrink: 0;
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
}

.node-content {
  flex: 1;
}

.node-title {
  font-weight: 600;
  margin-bottom: 6px;
  font-size: 15px;
  color: #1f2937;
  letter-spacing: -0.01em;
}

.node-description {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 6px;
  line-height: 1.5;
}

.node-result {
  font-size: 14px;
  color: #059669;
  font-weight: 500;
}

.loading-message {
  display: flex;
  gap: 16px;
  margin-bottom: 28px;
  padding: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.loading-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.loading-content {
  flex: 1;
}

.typing-indicator {
  display: flex;
  gap: 6px;
  margin-bottom: 12px;
  align-items: center;
}

.typing-indicator span {
  width: 10px;
  height: 10px;
  background: linear-gradient(135deg, #125EFF 0%, #1e7fff 100%);
  border-radius: 50%;
  animation: typing 1.6s infinite ease-in-out;
  box-shadow: 0 2px 4px rgba(18, 94, 255, 0.3);
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.3s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  30% {
    transform: translateY(-12px) scale(1.1);
    opacity: 1;
  }
}

.loading-text {
  margin: 0;
  font-size: 15px;
  color: #6b7280;
  font-weight: 400;
  line-height: 1.5;
}

.loading-spinner {
  animation: spin 1.2s linear infinite;
  color: #125EFF;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 主题样式 */
.theme-dark {
  --chat-bg: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  background: var(--chat-bg);
  color: white;
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .chat-header {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .chat-title {
  color: #f9fafb;
}

.theme-dark .chat-subtitle {
  color: #d1d5db;
}

.theme-dark .header-btn {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  border-color: rgba(255, 255, 255, 0.2);
  color: #e5e7eb;
}

.theme-dark .header-btn:hover {
  background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
  border-color: rgba(255, 255, 255, 0.3);
}

.theme-dark .messages-container {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.theme-dark .welcome-message,
.theme-dark .workflow-container,
.theme-dark .loading-message {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .welcome-content h4,
.theme-dark .workflow-container h4,
.theme-dark .node-title {
  color: #f9fafb;
}

.theme-dark .welcome-content p,
.theme-dark .node-description,
.theme-dark .loading-text {
  color: #d1d5db;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .ai-chat-container {
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }

  .messages-container {
    padding: 12px 16px;
    /* 为移动端键盘留出空间 */
    padding-bottom: env(keyboard-inset-height, 0px);
  }

  .chat-header {
    padding: 12px 16px;
    border-radius: 0;
    /* 适配刘海屏 */
    padding-top: max(12px, env(safe-area-inset-top));
  }

  .chat-title {
    font-size: 18px;
    font-weight: 600;
  }

  .chat-subtitle {
    font-size: 13px;
    margin-top: 2px;
  }

  .welcome-message,
  .workflow-container,
  .loading-message {
    padding: 16px;
    margin-bottom: 16px;
    border-radius: 16px;
  }

  .welcome-avatar img {
    width: 36px;
    height: 36px;
  }

  .welcome-content h4 {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .welcome-content p {
    font-size: 14px;
    line-height: 1.4;
  }

  /* 移动端欢迎提示样式 */
  .welcome-prompts {
    margin: 16px 12px;
    padding: 16px;
  }

  .welcome-prompts-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .welcome-prompt-item {
    padding: 12px;
  }

  .prompt-text {
    font-size: 13px;
  }

  .prompt-description {
    font-size: 11px;
  }

  .header-actions {
    gap: 6px;
  }

  .header-btn {
    min-width: 36px;
    height: 36px;
    padding: 8px;
    border-radius: 8px;
  }

  .header-btn img {
    width: 18px;
    height: 18px;
  }

  .workflow-nodes {
    gap: 12px;
    padding: 12px 0;
  }

  .workflow-node {
    padding: 12px 16px;
    border-radius: 12px;
    min-width: 120px;
  }

  .node-title {
    font-size: 13px;
    font-weight: 600;
  }

  .node-description {
    font-size: 11px;
    margin-top: 4px;
  }

  .loading-message {
    text-align: center;
  }

  .loading-text {
    font-size: 14px;
    margin-top: 8px;
  }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
  .ai-chat-container {
    height: 100vh;
    max-height: 100vh;
  }

  .messages-container {
    padding: 8px 12px;
    padding-bottom: env(keyboard-inset-height, 0px);
  }

  .chat-header {
    padding: 10px 12px;
    padding-top: max(10px, env(safe-area-inset-top));
  }

  .chat-title {
    font-size: 16px;
  }

  .chat-subtitle {
    font-size: 12px;
  }

  .welcome-message,
  .workflow-container,
  .loading-message {
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 12px;
  }

  .welcome-avatar img {
    width: 32px;
    height: 32px;
  }

  .welcome-content h4 {
    font-size: 15px;
    margin-bottom: 4px;
  }

  .welcome-content p {
    font-size: 13px;
  }

  .header-actions {
    gap: 4px;
  }

  .header-btn {
    min-width: 32px;
    height: 32px;
    padding: 6px;
    border-radius: 6px;
  }

  .header-btn img {
    width: 16px;
    height: 16px;
  }

  .workflow-nodes {
    gap: 8px;
    padding: 8px 0;
  }

  .workflow-node {
    padding: 8px 12px;
    border-radius: 10px;
    min-width: 100px;
  }

  .node-title {
    font-size: 12px;
  }

  .node-description {
    font-size: 10px;
    margin-top: 2px;
  }

  .loading-text {
    font-size: 13px;
    margin-top: 6px;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .ai-chat-container {
    height: 100vh;
  }

  .chat-header {
    padding: 8px 16px;
    padding-top: max(8px, env(safe-area-inset-top));
  }

  .messages-container {
    padding: 8px 16px;
  }

  .welcome-message,
  .workflow-container,
  .loading-message {
    padding: 12px 16px;
    margin-bottom: 12px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .header-btn {
    min-height: 44px;
    min-width: 44px;
  }

  .header-btn:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  .workflow-node:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  /* 防止iOS双击缩放 */
  .ai-chat-container {
    touch-action: manipulation;
  }

  /* 优化滚动性能 */
  .messages-container {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}

/* 深色模式移动端优化 */
@media (max-width: 768px) {
  .theme-dark .ai-chat-container {
    background: #1a1a1a;
  }

  .theme-dark .chat-header {
    background: rgba(30, 30, 30, 0.95);
    backdrop-filter: blur(10px);
  }

  .theme-dark .welcome-message,
  .theme-dark .workflow-container,
  .theme-dark .loading-message {
    background: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}
</style>
