<template>
  <div class="usage-reports">
    <!-- 报告列表头部 -->
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg font-semibold text-gray-900">分析报告</h3>
      <n-button type="primary" @click="generateReport">
        <template #icon>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
        </template>
        生成报告
      </n-button>
    </div>

    <!-- 报告筛选 -->
    <div class="bg-white rounded-lg p-4 shadow-sm border mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">报告类型</label>
          <n-select v-model:value="filters.type" :options="reportTypes" placeholder="选择报告类型" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">时间范围</label>
          <n-select v-model:value="filters.timeRange" :options="timeRanges" placeholder="选择时间范围" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
          <n-select v-model:value="filters.status" :options="statusOptions" placeholder="选择状态" />
        </div>
        <div class="flex items-end">
          <n-button @click="searchReports" class="w-full">
            搜索
          </n-button>
        </div>
      </div>
    </div>

    <!-- 报告列表 -->
    <div class="bg-white rounded-lg shadow-sm border">
      <div class="p-6">
        <n-data-table
          :columns="columns"
          :data="reports"
          :pagination="pagination"
          :loading="loading"
        />
      </div>
    </div>

    <!-- 报告详情模态框 -->
    <n-modal v-model:show="showReportModal" preset="card" title="报告详情" style="width: 80%; max-width: 1000px;">
      <div v-if="selectedReport">
        <div class="mb-4">
          <h4 class="text-lg font-semibold mb-2">{{ selectedReport.title }}</h4>
          <div class="flex space-x-4 text-sm text-gray-600">
            <span>生成时间: {{ selectedReport.createdAt }}</span>
            <span>报告类型: {{ selectedReport.type }}</span>
            <span>状态: {{ selectedReport.status }}</span>
          </div>
        </div>
        
        <div class="prose max-w-none">
          <h5 class="text-md font-medium mb-3">报告摘要</h5>
          <p class="text-gray-700 mb-4">{{ selectedReport.summary }}</p>
          
          <h5 class="text-md font-medium mb-3">详细分析</h5>
          <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-gray-700">{{ selectedReport.content }}</p>
          </div>
          
          <h5 class="text-md font-medium mb-3 mt-6">建议措施</h5>
          <ul class="list-disc list-inside space-y-1 text-gray-700">
            <li v-for="suggestion in selectedReport.suggestions" :key="suggestion">
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-2">
          <n-button @click="downloadReport">下载报告</n-button>
          <n-button type="primary" @click="showReportModal = false">关闭</n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, h } from 'vue';
import { NButton, NSelect, NDataTable, NModal, NTag } from 'naive-ui';

const loading = ref(false);
const showReportModal = ref(false);
const selectedReport = ref(null);

const filters = ref({
  type: null,
  timeRange: null,
  status: null
});

const reportTypes = [
  { label: '使用情况报告', value: 'usage' },
  { label: '性能分析报告', value: 'performance' },
  { label: '错误分析报告', value: 'error' },
  { label: '用户行为报告', value: 'behavior' }
];

const timeRanges = [
  { label: '最近7天', value: '7d' },
  { label: '最近30天', value: '30d' },
  { label: '最近90天', value: '90d' },
  { label: '最近1年', value: '1y' }
];

const statusOptions = [
  { label: '已完成', value: 'completed' },
  { label: '生成中', value: 'generating' },
  { label: '失败', value: 'failed' }
];

const columns = [
  {
    title: '报告标题',
    key: 'title',
    width: 200
  },
  {
    title: '类型',
    key: 'type',
    width: 120,
    render(row) {
      const typeMap = {
        'usage': '使用情况',
        'performance': '性能分析',
        'error': '错误分析',
        'behavior': '用户行为'
      };
      return typeMap[row.type] || row.type;
    }
  },
  {
    title: '生成时间',
    key: 'createdAt',
    width: 150
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusMap = {
        'completed': { type: 'success', text: '已完成' },
        'generating': { type: 'warning', text: '生成中' },
        'failed': { type: 'error', text: '失败' }
      };
      const status = statusMap[row.status];
      return h(NTag, { type: status.type }, { default: () => status.text });
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render(row) {
      return h('div', { class: 'flex space-x-2' }, [
        h(NButton, {
          size: 'small',
          onClick: () => viewReport(row)
        }, { default: () => '查看' }),
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => downloadReport(row)
        }, { default: () => '下载' })
      ]);
    }
  }
];

const reports = ref([
  {
    id: 1,
    title: '2024年1月使用情况分析报告',
    type: 'usage',
    createdAt: '2024-01-31 10:30:00',
    status: 'completed',
    summary: '本月系统使用情况良好，用户活跃度较上月提升15%，主要功能使用率稳定。',
    content: '详细的使用情况分析内容...',
    suggestions: [
      '建议优化响应时间较慢的功能模块',
      '增加用户引导提升新功能使用率',
      '定期进行系统性能优化'
    ]
  },
  {
    id: 2,
    title: '系统性能监控报告',
    type: 'performance',
    createdAt: '2024-01-30 15:45:00',
    status: 'completed',
    summary: '系统整体性能稳定，平均响应时间在可接受范围内，但部分高峰时段存在性能瓶颈。',
    content: '详细的性能分析内容...',
    suggestions: [
      '优化数据库查询性能',
      '增加服务器资源配置',
      '实施负载均衡策略'
    ]
  },
  {
    id: 3,
    title: '错误日志分析报告',
    type: 'error',
    createdAt: '2024-01-29 09:15:00',
    status: 'generating',
    summary: '',
    content: '',
    suggestions: []
  }
]);

const pagination = {
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
};

function generateReport() {
  console.log('生成新报告...');
  // 这里可以调用API生成报告
}

function searchReports() {
  loading.value = true;
  // 模拟搜索
  setTimeout(() => {
    loading.value = false;
    console.log('搜索报告...', filters.value);
  }, 1000);
}

function viewReport(report) {
  selectedReport.value = report;
  showReportModal.value = true;
}

function downloadReport(report = selectedReport.value) {
  console.log('下载报告:', report?.title);
  // 这里可以实现报告下载功能
}

onMounted(() => {
  // 初始化数据
});
</script>

<style lang="less" scoped>
.usage-reports {
  .prose {
    h5 {
      margin-top: 1rem;
      margin-bottom: 0.5rem;
    }
  }
}
</style>